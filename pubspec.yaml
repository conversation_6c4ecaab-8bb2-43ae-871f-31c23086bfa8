name: fliggy_item_detail
description: A new Flutter plugin.
version: 0.0.1
author:
homepage:
# 应用名称，如 fliggy，影响keep同步及依赖版本号校验
app_name: fliggy
# 应用基线版本号，影响keep同步及依赖版本号校验
baseline: ***********
# 开启或关闭自动同步依赖。不写该参数，默认开启
sync_dep: true

environment:
  sdk: ">=2.12.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  fliggy_design: ^0.0.4
  flutter_fliggyKV:
    git:
      url: **************************:fliggy_mobile/flutter_fliggyKV.git
      ref: 2.0.5
  visibility_detector: ^0.4.0
  fbridge:
    git:
      ref: 3.2.24
      url: **************************:fliggy_mobile/fbridge.git
  fliggy_mtop:
    git:
      ref: 1.2.2
      url: **************************:fliggy_mobile/fliggy_mtop.git
  fliggy_usertrack:
    git:
      ref: 2.0.6
      url: **************************:fliggy_mobile/fliggy_usertrack.git
  fphoto_view:
    git:
      url: **************************:fliggy_mobile/fphoto_view.git
      ref: 'feature/preload_page'
  fplayer:
    git:
      url: "**************************:fliggy_mobile/fplayer.git"
      ref: '1.0.2'
  fbroadcast:
    git:
      url: **************************:fapi/fbroadcast.git
      ref: 1.3.0
  flutter_html:
    git:
      url: **************************:fliggy_mobile/flutter_html.git
      ref: master
  crypto:  3.0.2
  intl: ^0.17.0
  ffloat:
    git:
      url: **************************:fapi/ffloat.git
      ref: '2.0.1'
  ffperformance:
    git:
      ref: 2.1.7
      url: **************************:fliggy_mobile/ffperformance.git
#    git:
#      ref: 2.0.4
#      url: **************************:fliggy_mobile/ffperformance.git
  cached_network_image: 3.2.3
  flutter_cache_manager: 3.3.0
  sqflite: 2.0.2

dependency_overrides:
  ftoast:
    git:
      url: **************************:fapi/ftoast.git
      ref: 2.0.2
  ffi:  2.0.0
  fdata_center:
    git:
      url: **************************:fapi/fdata_center.git
      ref: 0.0.4
  flutter_fliggyKV:
    git:
      url: **************************:fliggy_mobile/flutter_fliggyKV.git
      ref: 2.0.1
  cached_network_image: 3.2.3
  flutter_cache_manager: 3.3.0
  sqflite: 2.0.2
  shared_preferences: 2.0.8
  dio: 4.0.6
  crypto: 3.0.2

  power_scroll_view:
    git:
      url: **************************:fliggy_mobile/power_scroll_view.git
      ref: 3.7.1
  fliggy_webview:
    git:
      ref: 0.3.0
      url: **************************:fliggy_mobile/fliggy_webview.git
  fround_image:
    git:
      url: **************************:fapi/fround_image.git
      ref: 0.6.7
  image: 3.1.1
  fbridge:
    git:
      url: **************************:fliggy_mobile/fbridge.git
      ref: 3.2.24
  fliggy_mtop:
    git:
      ref: 1.2.9
      url: **************************:fliggy_mobile/fliggy_mtop.git
  fliggy_usertrack:
    git:
      url: **************************:fliggy_mobile/fliggy_usertrack.git
      ref: 2.0.6
  fliggy_router:
    git:
      url: **************************:fliggy_mobile/fliggy_router.git
      ref: 2.1.21
  floading: ^2.0.0
  fjson_exports:
    git:
      ref: 1.3.0
      url: **************************:fliggy_mobile/fjson_exports.git
  provider: 6.0.5
  flutter_boost:
    git:
      ref: 4.4.8
      url: **************************:fliggy_mobile/flutter_boost.git
  fliggy_app_info:
    git:
      url: **************************:fliggy_mobile/fliggy_app_info.git
      ref: 1.0.7
  fimage:
    git:
      url: **************************:fapi/fimage.git
      ref: 1.0.2
  fbridge_channel:
    git:
      url: **************************:fliggy_mobile/fbridge_channel.git
      ref: 1.2.0
  fdensity:
    git:
      ref: 2.1.2
      url: **************************:fapi/fdensity.git
  ficonfont:
    git:
      ref: 1.3.1
      url: **************************:fliggy_mobile/ficonfont.git
  flutter_common:
    git:
      ref: 1.1.3
      url: **************************:fliggy_mobile/flutter_common.git
  flutter_adapter:
    git:
      url: "**************************:fliggy_mobile/flutter_adapter.git"
      ref: "v3.7.12.1"
  fpdart: 0.6.0
  archive:
    git:
      url: **************************:fliggy_mobile/archive.git
      ref: 3.3.1
  ffperformance:
    git:
      ref: 2.0.4
      url: **************************:fliggy_mobile/ffperformance.git
#  ffperformance:
#    path: '/Users/<USER>/Documents/android/ws/ffperformance'
#  fround_image:
#    path: '/Users/<USER>/Documents/android/ws/fround_image'
  fliggy_flutter_location:
    git:
      url: **************************:fliggy_mobile/fliggy_location.git
      ref: 0.2.0

  fplayer:
    git:
      url: '**************************:fliggy_mobile/fplayer.git'
      ref: 1.1.0
  win32:  4.1.4
  ftitlebar:
    git:
      ref: 1.1.2
      url: **************************:fapi/ftitlebar.git
  ffonts:
    git:
      url: '**************************:fapi/ffonts.git'
      ref: 'master'

  ftitlebar_adapter:
    git:
      ref: 1.1.0
      url: **************************:fliggy_mobile/ftitlebar_adapter.git
  quiver:  3.2.1
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^1.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  module:
    androidX: true
# This section identifies this Flutter project as a plugin project.
# The androidPackage and pluginClass identifiers should not ordinarily
# be modified. They are used by the tooling to maintain consistency when
# adding or updating assets for this project.

# 有Native代码再打开！
# 有Native代码再打开！
# 有Native代码再打开！
#  plugin:
#    androidPackage: com.taobao.trip.fliggy_item_detail
#    pluginClass: FlutterVacationPlugin

# To add assets to your plugin package, add an assets section, like this:
  assets:
    - assets/
#
# For details regarding assets in packages, see
# https://flutter.dev/assets-and-images/#from-packages
#
# An image asset can refer to one or more resolution-specific "variants", see
# https://flutter.dev/assets-and-images/#resolution-aware.

# To add custom fonts to your plugin package, add a fonts section here,
# in this "flutter" section. Each entry in this list should have a
# "family" key with the font family name, and a "fonts" key with a
# list giving the asset and other descriptors for the font. For
# example:
# fonts:
#   - family: Schyler
#     fonts:
#       - asset: fonts/Schyler-Regular.ttf
#       - asset: fonts/Schyler-Italic.ttf
#         style: italic
#   - family: Trajan Pro
#     fonts:
#       - asset: fonts/TrajanPro.ttf
#       - asset: fonts/TrajanPro_Bold.ttf
#         weight: 700
#
# For details regarding fonts in packages, see
# https://flutter.dev/custom-fonts/#from-packages

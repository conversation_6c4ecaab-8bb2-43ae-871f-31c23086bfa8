import 'dart:convert';
import 'package:fbridge/fbridge.dart';
import 'package:fbroadcast/fbroadcast.dart';
import 'package:ffperformance/ffperformance.dart';
import 'package:fliggy_mtop/fliggy_mtop.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';

import '../custom_widget/count_down/count_down_timer_manager.dart';
import '../data/net/request_callback.dart';
import '../data/net/request_type.dart';
import '../data/router_net/item_detail_preload_helper.dart';
import '../data/router_net/item_detail_cache_data_helper.dart';
import '../fliggy_item_detail.dart';
import '../performance/TimeTrackRecord.dart';
import '../utils/alarm_track_utils.dart';
import '../utils/common_config.dart';
import '../utils/track_utils.dart';
import 'item_detail_engine.dart';
import 'package:fround_image/fround_image.dart';
import 'package:fliggy_router/fliggy_router.dart';

import 'overlay_page_manager.dart';

class FlutterVacationDetailPage extends StatefulWidget {
  const FlutterVacationDetailPage({Key? key, this.params}) : super(key: key);
  final Map<dynamic, dynamic>? params;

  @override
  _FlutterVacationDetailPageState createState() =>
      _FlutterVacationDetailPageState();
}

class _FlutterVacationDetailPageState extends State<FlutterVacationDetailPage>
    with FliggyPageMixin<FlutterVacationDetailPage>, TickerProviderStateMixin
    implements RequestCallback {
  ItemDetailEngine? _engine;

  Map<String, dynamic>? _pageModelData;

  late final String? _itemId;

  Widget? _pageWidget;

  ///当first回调已经存在的时候，mini缓存不需要进行刷新
  bool _needMiniCache = true;
  final int _maxInstances = 7; // 最多存在实例数
  final List<String?> _instancesStack = <String?>[];
  String? _uniqueId;

  late TimeTrackRecord _performanceTimeRecord;
  int? _dataSource; // 记录数据来源
  String? _fromSpm;
  bool _isOverlay = false;
  bool _isOverlayToTop = false;
  double overlayHeight = OverlayPageManager.OVERLAY_DEFAULT_HEIGHT;
  bool isPageBack = true;

  @override
  void initState() {
    super.initState();
    isPageBack = false;
    final Map<dynamic, dynamic> paramsTemp =
        widget.params ?? <String, dynamic>{};
    _itemId = paramsTemp['item_id'] ?? paramsTemp['id'];
    _fromSpm = paramsTemp['spm'] ?? '';
    _isOverlay = _fromSpm == '181.8174911';

    _pageErrorTrack(context);

    FBroadcast.instance(context).register('pageHide', (dynamic value, _) {});

    // 初始化预请求埋点类
    _performanceTimeRecord = TimeTrackRecord(
        context, '${TrackUtils.vacationDetailSpmAB}.0.0', paramsTemp);
    _engine = ItemDetailEngine(context, paramsTemp, this);
    _performanceTimeRecord.init(_engine);
    _engine?.init(_itemId, _performanceTimeRecord, _isOverlay,
        overlayToTop: _isOverlayToTop);

    _getCacheData();
    _dataSource = _dataSource ?? 0;
    _engine?.doRequest(RequestType.first);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 页面渲染完成后执行的代码
      if (_uniqueId == null) {
        _uniqueId = BoostContainer.of(context)?.topPageUniqueId() ?? '';
        _instancesStack.add(_uniqueId);
        if (_instancesStack.length > _maxInstances) {
          BoostNavigator.instance.remove(_instancesStack[0]);
          _instancesStack.remove(_instancesStack[0]);
        }
      }
      if (PreLoadHelper.headPicCache.containsKey(_itemId) &&
          PreLoadHelper.headPicCache[_itemId] != null) {
        TrackUtils.headImageCacheTrack(trackParams: <String, String>{
          _itemId.toString(): PreLoadHelper.headPicCache[_itemId],
          'fromSpm': _fromSpm ?? ''
        });
      }
      FBridgeApi.newInstance(context).callSafe('swipe_back', <dynamic, dynamic>{
        'disable_area':
            jsonEncode(<String, dynamic>{'x': 0, 'y': 0, 'w': 100, 'h': 400})
      });
      TrackUtils.pageAllTrack(
          trackParams: <String, String>{'fromSpm': _fromSpm ?? ''});
      OverlayPageManager.overlayAppearDetail(context, _isOverlay);
      OverlayPageManager.overlayDidHalfScreenDetail(context, _isOverlay);
    });
  }

  ErrorWidgetBuilder? errorWidgetBuilder;

  ///页面报错监控
  void _pageErrorTrack(BuildContext context) {
    if (_itemId == null || _itemId == '') {
      AlarmTrackUtil.reportPageParamsEmpty();
    }
    errorWidgetBuilder = ErrorWidget.builder;
    ErrorWidget.builder = (FlutterErrorDetails details) {
      AlarmTrackUtil.pageError(details.exception.toString(), _itemId,
          trackParams: <String, dynamic>{'details': details.toString()});
      return Container(
        height: 20,
        margin: const EdgeInsets.only(bottom: itemDivider),
        padding: const EdgeInsets.all(10),
        decoration: BoxDecoration(
            color: const Color(0xffffffff),
            borderRadius: BorderRadius.circular(cardBorderRadius)),
      );
    };
    FBroadcast.instance(fcontext_fliggyRouteObserver)
        .register(BoostContainer.of(context)?.topPageUniqueId() ?? '',
            (dynamic status, _) {
      if (status == 'pagePush') {
      } else if (status == 'pageShow') {
        if (isPageBack) {
          OverlayPageManager.overlayAppearDetail(context, _isOverlay,
              isBack: true);
          if (_isOverlayToTop) {
            OverlayPageManager.overlayDidFullScreenDetail(context, _isOverlay,
                isBack: true);
          } else {
            OverlayPageManager.overlayDidHalfScreenDetail(context, _isOverlay,
                isBack: true);
          }
        }
        isPageBack = true;
      } else if (status == 'pageHide') {
        ErrorWidget.builder = errorWidgetBuilder!;
        if (!(_engine?.isPagePop ?? false)) {
          OverlayPageManager.overlayDisappearDetail(context, _isOverlay);
        }
      } else if (status == 'pagePop') {
      } else if (status == 'foreground') {
      } else if (status == 'background') {}
    });
  }

  @override
  Widget build(BuildContext context) {
    return _isOverlay
        ? _buildOverlayWidget()
        : Material(
            child: Container(
              color: const Color(0xfff2f3f5),
              child: _pageModelData == null ? placeHoldWidget() : _pageWidget,
            ),
          );
  }

  Widget _buildOverlayWidget() {
    return Stack(
      children: <Widget>[
        GestureDetector(
          onTap: () {
            _engine?.pagePop();
          },
          child: Container(
            height: overlayHeight + 30 < 0 ? 0 : overlayHeight + 30,
            color: const Color(0x99000000),
          ),
        ),
        Container(
          margin: EdgeInsets.only(top: overlayHeight < 0 ? 0 : overlayHeight),
          child: Material(
            color: Color(0xFFF2F3F5),
            borderRadius: BorderRadius.only(
                topLeft:
                    Radius.circular(OverlayPageManager.OVERLAY_BORDER_RADIUS),
                topRight:
                    Radius.circular(OverlayPageManager.OVERLAY_BORDER_RADIUS)),
            child: NotificationListener(
                onNotification: (ScrollNotification notification) {
                  if (notification is ScrollEndNotification) {
                    if (_isOverlayToTop) {
                      if (overlayHeight >
                          OverlayPageManager.OVERLAY_TOP_MAX_HEIGHT) {
                        _engine?.pagePop();
                      } else {
                        _overlayScrollToTop();
                      }
                    } else {
                      if (overlayHeight >
                          OverlayPageManager.OVERLAY_MAX_HEIGHT) {
                        _engine?.pagePop();
                      } else if (overlayHeight <
                          OverlayPageManager.OVERLAY_MIN_HEIGHT) {
                        _overlayScrollToTop();
                      } else {
                        _overlayScrollReset();
                      }
                    }
                  } else if (notification is ScrollUpdateNotification) {
                    // print(
                    //     '=====================${(notification as ScrollUpdateNotification).scrollDelta}');
                    final double moveDate =
                        (notification as ScrollUpdateNotification)
                                .scrollDelta! *
                            3;
                    if (!_isOverlayToTop) {
                      setState(() {
                        overlayHeight -= moveDate;
                        if (overlayHeight <= 0) {
                          overlayHeight = 0;
                          _isOverlayToTop = true;
                        }
                      });
                    }
                    FBroadcast.instance(_engine?.uniqueId)
                        .broadcast('overlayPage', value: <String, dynamic>{
                      'moveDate': moveDate,
                      'isOverlayToTop': _isOverlayToTop
                    });
                  } else if (notification is OverscrollNotification) {
                    setState(() {
                      overlayHeight += (notification as OverscrollNotification)
                              .dragDetails
                              ?.primaryDelta ??
                          0;
                    });
                  }
                  return false;
                },
                child: _pageModelData == null || _pageWidget == null
                    ? placeHoldWidget()
                    : _pageWidget!),
          ),
        ),
      ],
    );
  }

  void _overlayScrollToTop() {
    if (_isOverlay) {
      while (overlayHeight > 0) {
        setState(() {
          overlayHeight -= 1;
        });
        FBroadcast.instance(_engine?.uniqueId).broadcast('overlayPage',
            value: <String, dynamic>{
              'moveDate': 1.0,
              'isOverlayToTop': _isOverlayToTop
            });
      }
      _isOverlayToTop = true;
      OverlayPageManager.overlayDidFullScreenDetail(context, _isOverlay);
      FBroadcast.instance(_engine?.uniqueId).broadcast('overlayPage',
          value: <String, dynamic>{
            'moveDate': 1.0,
            'isOverlayToTop': _isOverlayToTop
          });
    }
  }

  void _overlayScrollReset() {
    if (_isOverlay) {
      while (overlayHeight > OverlayPageManager.OVERLAY_DEFAULT_HEIGHT) {
        setState(() {
          overlayHeight -= 1;
          FBroadcast.instance(_engine?.uniqueId).broadcast('overlayPage',
              value: <String, dynamic>{
                'moveDate': -1.0 * 3,
                'isOverlayToTop': _isOverlayToTop
              });
        });
      }
      while (overlayHeight < OverlayPageManager.OVERLAY_DEFAULT_HEIGHT) {
        setState(() {
          overlayHeight += 1;
          FBroadcast.instance(_engine?.uniqueId).broadcast('overlayPage',
              value: <String, dynamic>{
                'moveDate': -1.0 * 3,
                'isOverlayToTop': _isOverlayToTop
              });
        });
      }
    }
  }

  /// 骨架占位图
  Widget placeHoldWidget() {
    Widget? image;
    if (PreLoadHelper.headPicCache.containsKey(_itemId)) {
      final String? picUrl = PreLoadHelper.headPicCache[_itemId];
      if (picUrl != null && picUrl.isNotEmpty) {
        // 向内存注入图片
        image = FRoundImage.network(picUrl, width: 375, height: 375);
      }
    }
    // 底部安全距离
    // final double safeBottomHeight = MediaQuery.of(context).padding.bottom;

    return Container(
      child: Column(
        children: <Widget>[
          Stack(
            children: <Widget>[
              SizedBox(
                height: 375,
                width: 375,
              ),
              if (image != null) image,
            ],
          ),
          Expanded(
              child: FRoundImage.asset(
            'assets/placehold_body_image.png',
            package,
            width: 375,
            fit: BoxFit.fill,
          )),
          Container(
            color: const Color(0xFFFFFFFF),
            padding: EdgeInsets.only(bottom: 10),
            child: FRoundImage.asset(
              'assets/placehold_footer_image.png',
              package,
              width: 375,
              height: 55,
              fit: BoxFit.fitWidth,
            ),
          )
        ],
      ),
    );
  }

  @override
  void onError(RequestType requestType, MtopResponseModel responseModel,
      {bool retry = false}) {
    if (retry) {
      _engine?.doRequest(requestType);
    } else {
      if (requestType == RequestType.first) {
        // FBridgeApi.newInstance(context).callSafe('close_loading_view');
        FBridgeApi.newInstance(context).callSafe(
          'toast',
          <String, dynamic>{'message': '请求失败，请稍后再试'},
        );
        if (requestType != RequestType.second &&
            requestType != RequestType.third) {
          // 过滤掉二屏和三屏的请求
          _performanceTimeRecord.reportError();
        }
      }
    }
  }

  @override
  void onStart(RequestType requestType, String? componentId) {}

  @override
  void onSuccess(RequestType requestType, MtopResponseModel responseModel) {
    if (requestType != RequestType.second && requestType != RequestType.third) {
      // 过滤掉二屏和三屏的请求
      _performanceTimeRecord.getDataEndAndStartRender();
      _performanceTimeRecord.setDataSource(_dataSource ?? 0);
    }

    if (requestType == RequestType.preLoadCache ||
        requestType == RequestType.fullCache) {
      if (_needMiniCache) {
        if (mounted) {
          Widget? image;
          if (PreLoadHelper.headPicCache.containsKey(_itemId)) {
            final String? picUrl = PreLoadHelper.headPicCache[_itemId];
            if (picUrl != null && picUrl.isNotEmpty) {
              // 向内存注入图片
              image = FRoundImage.network(
                picUrl,
                width: 375,
                height: 375,
              );
            }
          }
          _pageModelData = responseModel.data;
          _pageWidget = _engine?.render(_pageModelData!,
              requestType: requestType, cacheHeadWidget: image);
          _performanceTimeRecord.reportSuccess();
        }
      }
    } else if (requestType == RequestType.first) {
      // FBridgeApi.newInstance(context).callSafe('close_loading_view');
      //当first回调已经存在的时候，mini缓存不需要进行刷新
      _needMiniCache = false;
      setState(() {
        if (mounted) {
          _pageModelData = responseModel.data;
          _pageWidget =
              _engine?.render(_pageModelData!, requestType: requestType);
          _performanceTimeRecord.reportSuccess();
          FBridgeApi.newInstance(context).callSafe('uploadCpsTrack', <dynamic, dynamic>{
            'url':widget.params?['url'],
            'sellerId':_engine?.itemDetailModel.sellerModel?.userId,
            'itemId':_itemId,
          });
        }
      });
      //清空内存缓存
      PreLoadHelper.preCacheData.clear();
      //更新缓存
      if (responseModel.data != null) {
        if (mounted) {
          final Map<String, dynamic> saveCache = <String, dynamic>{
            'data': responseModel.data
          };
          ItemDetailCacheDataHelper.saveCache(
            PreLoadHelper.preLoadCacheKey(_itemId),
            jsonEncode(saveCache),
          );
        }
      }
    }
  }

  @override
  String getPageName() {
    return TrackUtils.vacationDetailName;
  }

  @override
  String getPageSpmCnt() {
    return '${TrackUtils.vacationDetailSpmAB}.0.0';
  }

  @override
  void onResume() {
    super.onResume();

    final Map<String, String> params = <String, String>{};
    String pageEnterStr = '';
    if (widget.params != null) {
      try {
        pageEnterStr = jsonEncode(widget.params);
      } catch (_) {}
    }
    params['pageEnter'] = pageEnterStr;

    // 页面埋点上报
    FliggyUserTrackApi.getInstance().updatePageProperties(
      context,
      params,
    );
    if (widget.params != null) {
      if (widget.params!['id'] != null) {
        FBridgeApi.newInstance(context).callSafe('screenshot_track_update',
            <dynamic, dynamic>{'good_id': widget.params!['id']});
      } else if (widget.params!['itemId'] != null) {
        FBridgeApi.newInstance(context).callSafe('screenshot_track_update',
            <dynamic, dynamic>{'good_id': widget.params!['itemId']});
      }
    }
  }

  @override
  void dispose() {
    if (_instancesStack.contains(_uniqueId)) {
      _instancesStack.remove(_uniqueId);
    }
    // 全局倒计时定时器关闭
    CountdownTimerManager().dispose();
    _engine?.dispose();
    _engine = null;
    super.dispose();
  }

  ///获取缓存数据
  void _getCacheData() {
    final dynamic data = PreLoadHelper.preCacheData[_itemId];

    ///全量缓存数据
    if (data != null) {
      final MtopResponseModel responseModel = MtopResponseModel();
      responseModel.data = data;
      responseModel.success = true;
      _dataSource = _dataSource ?? 1; // 如果没有被赋值，进行赋值，否则保持原值
      onSuccess(RequestType.fullCache, responseModel);
      TrackUtils.fullCacheTrack(
          trackParams: <String, String>{'fromSpm': _fromSpm ?? ''});
    } else {
      ///预请求数据
      final dynamic value =
          FFPerformance.getCache(PreLoadHelper.preLoadCacheKey(_itemId));
      final dynamic data = ItemDetailCacheDataHelper.checkCacheData(value);
      if (data != null) {
        final MtopResponseModel responseModel = MtopResponseModel();
        responseModel.data = data;
        responseModel.success = true;
        _dataSource = _dataSource ?? 1;
        onSuccess(RequestType.preLoadCache, responseModel);
        TrackUtils.preLoadCacheTrack(
            trackParams: <String, String>{'fromSpm': _fromSpm ?? ''});
      } else {
        TrackUtils.noCacheTrack(trackParams: <String, String>{
          'fromSpm': _fromSpm ?? '',
          'itemId': _itemId ?? ''
        });
        AlarmTrackUtil.noCacheData(_itemId);
      }
    }
  }
}

import 'package:fbridge/fbridge.dart';
import 'package:flutter/cupertino.dart';

class OverlayPageManager {
  static const double OVERLAY_DEFAULT_HEIGHT = 200;
  static const double OVERLAY_MAX_HEIGHT = OVERLAY_DEFAULT_HEIGHT + 30;
  static const double OVERLAY_MIN_HEIGHT = OVERLAY_DEFAULT_HEIGHT - 30;
  static const double OVERLAY_TOP_MAX_HEIGHT = 30;
  static const double OVERLAY_BORDER_RADIUS = 12;
  static const String APPEAR = 'overlayAppearDetail';
  static const String APPEAR_HALF = 'overlayDidHalfScreenDetail';
  static const String APPEAR_FULL = 'overlayDidFullScreenDetail';
  static const String DISAPPEAR = 'overlayDisappearDetail';

  ///进入页面
  static void overlayAppearDetail(BuildContext context,bool isOverlay, {bool isBack = false}) {
    if (isOverlay) {
      FBridgeApi.newInstance(context).callSafe(
        'post_event',
        <String, dynamic>{
          'event':APPEAR,
          'data': <String, dynamic>{
            'isBack': isBack,
            'pageType': 'flutter',
            'pageParams': <String, dynamic>{
              'flutter_path': 'fliggy_item_detail/home'
            }
          }
        },
      );
    }
  }

  ///进入半浮层，包括子页面返回
  static void overlayDidHalfScreenDetail(BuildContext context,bool isOverlay,
      {bool isBack = false}) {
    if (isOverlay) {
      FBridgeApi.newInstance(context).callSafe(
        'post_event',
        <String, dynamic>{
          'event':APPEAR_HALF,
          'data': <String, dynamic>{
            'isBack': isBack,
            'pageType': 'flutter',
            'pageParams': <String, dynamic>{
              'flutter_path': 'fliggy_item_detail/home'
            }
          }
        },
      );
    }
  }

  ///半浮层滑动进入全屏
  static void overlayDidFullScreenDetail(BuildContext context,bool isOverlay,
      {bool isBack = false}) {
    if (isOverlay) {
      FBridgeApi.newInstance(context).callSafe(
        'post_event',
        <String, dynamic>{
          'event':APPEAR_FULL,
          'data': <String, dynamic>{
            'isBack': isBack,
            'pageType': 'flutter',
            'pageParams': <String, dynamic>{
              'flutter_path': 'fliggy_item_detail/home'
            }
          }
        },
      );
    }
  }

  ///商详跳转下一页
  static void overlayDisappearDetail(BuildContext context,bool isOverlay,
      {bool isBack = false}) {
    if (isOverlay) {
      FBridgeApi.newInstance(context).callSafe(
        'post_event',

        <String, dynamic>{
          'event':DISAPPEAR,
          'data': <String, dynamic>{
            'isBack': isBack,
            'pageType': 'flutter',
            'pageParams': <String, dynamic>{
              'flutter_path': 'fliggy_item_detail/home'
            }
          }

        },
      );
    }
  }
}

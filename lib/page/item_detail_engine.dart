import 'dart:convert';
import 'dart:io';

import 'package:fbridge/fbridge.dart';
import 'package:flutter/material.dart';
import 'package:power_scroll_view/power_scroll_view.dart';
import 'package:fbroadcast/fbroadcast.dart';
import 'package:fliggy_router/fliggy_router.dart';

import '../data/item_detail_model.dart';
import '../data/net/request_callback.dart';
import '../data/net/request_type.dart';
import '../performance/TimeTrackRecord.dart';
import '../render/render_view.dart';
import '../sku/sku_manager.dart';
import 'item_detail_change_notifier.dart';
import 'item_detail_scroll_controller.dart';
import 'overlay_page_manager.dart';

class ItemDetailEngine {
  ItemDetailEngine(
      this.context, this.pageParams, RequestCallback requestCallback)
      : pageChangeNotifier =
            ItemDetailChangeNotifier(pageParams, requestCallback),
        skuManager = SkuManager(context);

  final ItemDetailChangeNotifier pageChangeNotifier;
  final SkuManager skuManager;
  final BuildContext context;
  final ItemDetailScrollController powerScrollController =
      ItemDetailScrollController();
  String? itemId;
  bool isOverlay = false;

  ///此次进入商品唯一key
  String? uniqueId;

  bool isPagePop = false;

  ///滚动Controller
  ScrollController get scrollController =>
      powerScrollController.scrollController ?? ScrollController();

  final Map<dynamic, dynamic> pageParams;

  ///@description 用来存储当且页面的cache信息
  final Map<dynamic, dynamic> pageCache = <dynamic, dynamic>{};

  ///url解析数据
  Map<dynamic, dynamic> pageUrlMap = <dynamic, dynamic>{};

  /// 阻止手势侧滑数值
  int noBack = 400;
  Widget? cacheHeadImageWidget;

  void init(String? itemId, TimeTrackRecord performanceTimeRecord, bool overlay,
      {bool overlayToTop = false}) {
    //解析页面url
    final String? pageUrl = pageParams['url'];
    isOverlay = overlay;
    if (pageUrl != null) {
      final Uri uri = Uri.dataFromString(pageUrl);
      pageUrlMap = <String, dynamic>{...uri.queryParameters};
    }
    this.itemId = itemId;
    uniqueId = '${DateTime.now().millisecondsSinceEpoch}$itemId';
    //初始化数据管理器
    performanceTimeRecord.pageInitEndAndStartGetData();
    //页面滚动监听
    final Map<String, dynamic> eventArgs = <String, dynamic>{};
    scrollController.addListener(() {
      // 往下滑就不要
      if (scrollController.offset >= 0) {
        eventArgs['offset'] = scrollController.offset;
        itemDetailModel.bottomConsultV2Offset.value = scrollController.offset;
        FBroadcast.instance(uniqueId)
            .broadcast('titleBarStatus', value: eventArgs);

        // todo:版本号限制9.10.2,上线前看一下动态包限制,如果低于,加一个版本号控制(没有桥,写起来很麻烦,而且还是异步获取,在滚动时判断不合适)
        if (scrollController.offset > 130 && noBack > 250) {
          // 向下滑动头图的一半左右
          if (Platform.isIOS) {
            noBack = 250;
            FBridgeApi.newInstance(context)
                .callSafe('swipe_back', <dynamic, dynamic>{
              'disable_area': jsonEncode(
                  <String, dynamic>{'x': 0, 'y': 0, 'w': 100, 'h': noBack})
            });
          }
        } else if (scrollController.offset > 275 && noBack > 0) {
          // 向下滑动头图离开,约为 370-100
          if (Platform.isIOS) {
            noBack = 0;
            // 这里变化太快了,如果频繁的调桥会出问题
            FBridgeApi.newInstance(context)
                .callSafe('swipe_back', <dynamic, dynamic>{
              'disable_area': jsonEncode(
                  <String, dynamic>{'x': 0, 'y': 0, 'w': 100, 'h': noBack})
            });
          }
        } else if (scrollController.offset < 130 && noBack <= 250) {
          // 向上滑动头图出现
          if (Platform.isIOS) {
            noBack = 400;
            // 这里变化太快了,如果频繁的调桥会出问题
            FBridgeApi.newInstance(context)
                .callSafe('swipe_back', <dynamic, dynamic>{
              'disable_area': jsonEncode(
                  <String, dynamic>{'x': 0, 'y': 0, 'w': 100, 'h': noBack})
            });
          }
        }
      }
    });
  }

  ///@description 页面渲染
  ///@param json 页面数据
  Widget render(Map<String, dynamic> json,
      {RequestType requestType = RequestType.first, Widget? cacheHeadWidget}) {
    cacheHeadImageWidget = cacheHeadWidget;
    pageChangeNotifier.changeStateByProtocol(json, requestType: requestType);
    skuManager.changeState(this);
    return RenderView(this);
  }

  ///@description 一屏请求
  void doRequest(RequestType requestType) =>
      pageChangeNotifier.doRequest(requestType);

  ///刷新页面
  void refreshPage(
      {Map<String, dynamic>? json,
      RequestType requestType = RequestType.first}) {
    if (json != null) {
      pageChangeNotifier.changeStateByProtocol(json, requestType: requestType);
      skuManager.changeState(this);
    }
    pageChangeNotifier.refreshPage();
  }

  ///刷新组件
  void refreshComponent(List<String> componentIds) =>
      pageChangeNotifier.refreshComponent(componentIds);

  ///获取数据
  ItemDetailModel get itemDetailModel => pageChangeNotifier.itemDetailModel;

  ///@description 页面数据
  Map<dynamic, dynamic> get dataModel => itemDetailModel.data;

  ///组件在body中的position,默认返回-1.
  ///使用tag获取时，单页面存在多个相同tag组件时，随机返回一个。使用id获取唯一组件。
  int getBodyPosition(String id) => itemDetailModel.getBodyPosition(id);

  ///@description 页面滚动到指定组件
  ///@param id 组件id
  void scrollToNode(String id) {
    final int index = getBodyPosition(id);
    if (index == -1) {
      return;
    }
    final PowerIndex powerIndex = PowerIndex(sectionIndex: 0, index: index);
    powerScrollController.titleTabScroll(
        powerIndex, id, itemDetailModel.titleBarModel?.tabGlobalKey[id],
        offset: 150);
  }

  void scrollToOffset(double offset) {
    powerScrollController.animateToOffset(offset);
  }

  void dispose() {
    itemDetailModel.headMediaModel?.dispose();
    FBroadcast.instance(uniqueId).dispose();
    scrollController.dispose();
    powerScrollController.dispose();
  }

  void pagePop() {
    isPagePop = true;
    FliggyNavigatorApi.getInstance().pop(context, exts: <String, dynamic>{
      'animated': true,
    });
    // OverlayPageManager.overlayDisappearDetail(context,isOverlay);
  }
}

import '../component/price_coupons_introduce_new/component.dart';
import '../component/recommend_across_shop/component.dart';

import '../component/air_ticket_recommend/component.dart';
import '../component/alitrip_prom/component.dart';
import '../component/appoint_info/component.dart';
import '../component/askall_vacation/component.dart';
import '../component/billion_service/component.dart';
import '../component/book_info/component.dart';
import '../component/booking_notice/component.dart';
import '../component/bottom_bar/component.dart';
import '../component/bottom_bar/consult/component.dart';
import '../component/bottom_bar/consultv2/component.dart';
import '../component/bottom_bar/hit/component.dart';
import '../component/bottom_tips/component.dart';
import '../component/buy_ensure/component.dart';
import '../component/buy_notice_simple/component.dart';
import '../component/buy_notice_v2/component.dart';
import '../component/common_book_rule/component.dart';
import '../component/common_comment/component.dart';
import '../component/common_spu_entry/component.dart';
import '../component/component_key_constant.dart';
import '../component/detail_package_list/component.dart';
import '../component/dnw_packages_choose/component.dart';
import '../component/fee_desc/component.dart';
import '../component/fee_exclude/component.dart';
import '../component/fee_exclude_info/component.dart';
import '../component/fee_include/component.dart';
import '../component/fee_include_info/component.dart';
import '../component/fliggy_detail_flight_reference_route_body/component.dart';
import '../component/fliggy_vacation_travel_summary/component.dart';
import '../component/flight_refund/component.dart';
import '../component/flight_shelf/component.dart';
import '../component/head_media/component.dart';
import '../component/highlight/component.dart';
import '../component/hotel/hotel_shelf_v2/component.dart';
import '../component/hotel_attract/component.dart';
import '../component/hotel_facility/component.dart';
import '../component/hotel_member_card_v2/component.dart';
import '../component/hotel_notice/component.dart';
import '../component/hotel_package_include/component.dart';
import '../component/hotel_shelf/component.dart';
import '../component/huabei_rights/component.dart';
import '../component/item_group_date/component.dart';
import '../component/item_select/component.dart';
import '../component/item_video/component.dart';
import '../component/mind_rule/component.dart';
import '../component/normal_factor/component.dart';
import '../component/other_desc/component.dart';
import '../component/package_festival_list/component.dart';
import '../component/package_shelf_filterable/component.dart';
import '../component/phone_card_shelf/component.dart';
import '../component/platform_insurance_info/component.dart';
import '../component/price_coupons_introduce/component.dart';
import '../component/product_desc/component.dart';
import '../component/product_highlights/component.dart';
import '../component/purchase_notes/component.dart';
import '../component/rank_list_info/component.dart';
import '../component/rate_and_server/component.dart';
import '../component/rate_and_share/component.dart';
import '../component/recommend/component.dart';
import '../component/recommend_by_poi/component.dart';
import '../component/related_shelf/component.dart';
import '../component/restaurant/component.dart';
import '../component/rich_content/component.dart';
import '../component/route_brand/component.dart';
import '../component/route_fee_detail/component.dart';
import '../component/route_fee_detail2/component.dart';
import '../component/route_graphic/component.dart';
import '../component/route_itinerary_plan/component.dart';
import '../component/route_itinerary_plan_fy25/component.dart';
import '../component/route_itinerary_point/component.dart';
import '../component/route_itinerary_point_fy25/component.dart';
import '../component/route_shelf/component.dart';
import '../component/route_shelf_fy25/component.dart';
import '../component/second_loading/component.dart';
import '../component/self_pay/component.dart';
import '../component/service_ensure/component.dart';
import '../component/service_guarantee/component.dart';
import '../component/service_info/component.dart';
import '../component/service_support_shop/component.dart';
import '../component/ship_vacation_travel_line/component.dart';
import '../component/shop/component.dart';
import '../component/shop_rate/component.dart';
import '../component/sku_info/component.dart';
import '../component/special_notice/component.dart';
import '../component/spu_entry/component.dart';
import '../component/stock_shelf/component.dart';
import '../component/store_qualification/component.dart';
import '../component/third_loading/component.dart';
import '../component/ticket_booking/component.dart';
import '../component/ticket_new_scenic/component.dart';
import '../component/ticket_service/component.dart';
import '../component/ticket_shelf_v3/component.dart';
import '../component/ticket_vacation_market/component.dart';
import '../component/time_zone/component.dart';
import '../component/titlebar/component.dart';
import '../component/traffic/flight_shelf_v2/component.dart';
import '../component/traffic_desc_info/component.dart';
import '../component/travel_description/component.dart';
import '../component/travel_detail/component.dart';
import '../component/travel_hotel_mul_store/component.dart';
import '../component/travel_hotel_store/component.dart';
import '../component/travel_odetail/component.dart';
import '../component/travel_policy/component.dart';
import '../component/use_rules/component.dart';
import '../component/vacation_detail_fun_packages_v2/component.dart';
import '../component/vacation_detail_route_presale_process/component.dart';
import '../component/vacation_hotel_member_card/component.dart';
import '../component/vacation_hotel_scenic_intro_card/component.dart';
import '../component/vacation_public_benefit/component.dart';
import '../component/vacation_scenic_membership_card/component.dart';
import '../component/vacation_server_line/component.dart';
import '../component/visa_procedure/component.dart';
import '../component/visa_shelf/component.dart';
import '../component/visa_shelf_v2/component.dart';
import '../component/zero_save_buy_rule/component.dart';
import '../custom_widget/bottom_radius.dart';
import '../custom_widget/common_divider.dart';
import '../custom_widget/page_layer/float-live/component.dart';
import '../custom_widget/top_radius.dart';
import '../render/component/component.dart';

///详情页结构
final List<String> pageStructure =
    firstScreenStructure + secondScreenStructure + thirdScreenStructure;

///平台有价券结构
final List<String> pageStructureCoupon = <String>[
      ComponentKeyConstant.headMedia,
      ComponentKeyConstant.alitripProm,
      ComponentKeyConstant.serviceGuarantee,
      ComponentKeyConstant.ticketService,
      ComponentKeyConstant.platformInsuranceInfo,
      ComponentKeyConstant.vacationServerLine,
      ComponentKeyConstant.mindRule,
      ComponentKeyConstant.itemGroupDate,
      ComponentKeyConstant.vacationScenicMembershipCard,
      ComponentKeyConstant.relatedShelf,
      ComponentKeyConstant.stockShelf,
      ComponentKeyConstant.flightShelfV2,
      ComponentKeyConstant.dnwPackagesChoose,
      ComponentKeyConstant.ticketShelfV3,
      ComponentKeyConstant.vacationTicketMarket,
    ] +
    <String>[
      ComponentKeyConstant.secondLoading,
      ComponentKeyConstant.priceCouponsIntroduceNew,

      ComponentKeyConstant.vacationPublicBenefit,
      ComponentKeyConstant.highlight,
      ComponentKeyConstant.productDesc,
      ComponentKeyConstant.ticketNewScenic,
      ComponentKeyConstant.priceCouponsIntroduce,

      ComponentKeyConstant.itemVideo,
      ComponentKeyConstant.appointInfo,
      ComponentKeyConstant.routeFeeDetail2,
      ComponentKeyConstant.richContent,
      // ComponentKeyConstant.hotelFacility,
      ComponentKeyConstant.hotelPackageInclude,
      ComponentKeyConstant.hotelNotice,
      ComponentKeyConstant.bookingNotice,
      ComponentKeyConstant.buyNoticeV2,
      ComponentKeyConstant.feeInclude,
      ComponentKeyConstant.buyNoticeSimple,
      ComponentKeyConstant.commonComment,
      ComponentKeyConstant.shopRate,
      ComponentKeyConstant.askallVacation,
      ComponentKeyConstant.shop,

      ComponentKeyConstant.thirdLoading,
    ] +
    thirdScreenStructure;

///一屏结构
final List<String> firstScreenStructure = <String>[
  ComponentKeyConstant.headMedia,
  // ComponentKeyConstant.billionService,
  // // ComponentKeyConstant.itemSelect,
  // ComponentKeyConstant.normalFactor,
  // // ComponentKeyConstant.commonBookRule,
  // // ComponentKeyConstant.spuEntry,
  // ComponentKeyConstant.zeroSaveBuyRule,
  // ComponentKeyConstant.rankListInfo,
  // ComponentKeyConstant.rateAndShare,
  // ComponentKeyConstant.rateAndServer,
  // ComponentKeyConstant.bottomRadiusDivider,
  // ComponentKeyConstant.buyEnsure,
  // ComponentKeyConstant.skuInfo,
  ComponentKeyConstant.alitripProm,
  // ComponentKeyConstant.visaShelf,
  // ComponentKeyConstant.visaShelfV2,
  ComponentKeyConstant.serviceGuarantee,
  ComponentKeyConstant.ticketService,
  // ComponentKeyConstant.visaProcedure,
  ComponentKeyConstant.platformInsuranceInfo,
  ComponentKeyConstant.vacationServerLine,
  // ComponentKeyConstant.ticketPoi,
  // ComponentKeyConstant.huabeiRights,
  // ComponentKeyConstant.vacationHotelScenicIntroCard,
  ComponentKeyConstant.mindRule,
  // ComponentKeyConstant.travelHotelStore,
  // ComponentKeyConstant.travelHotelMulStore,
  // ComponentKeyConstant.vacationHotelMemberCard,
  // ComponentKeyConstant.hotelMemberCardV2,
  ComponentKeyConstant.itemGroupDate,
  // ComponentKeyConstant.phoneCardShelf,
  ComponentKeyConstant.vacationScenicMembershipCard,
  ComponentKeyConstant.relatedShelf,
  ComponentKeyConstant.stockShelf,
  // ComponentKeyConstant.flightShelf,
  ComponentKeyConstant.flightShelfV2,
  // ComponentKeyConstant.hotelShelf,
  // ComponentKeyConstant.playShelf,
  // ComponentKeyConstant.vacationDetailFunPackagesV2,
  // ComponentKeyConstant.packageShelfFilterable,
  ComponentKeyConstant.dnwPackagesChoose,
  ComponentKeyConstant.ticketShelfV3,
  // ComponentKeyConstant.routeShelf,
  // ComponentKeyConstant.routeShelfFy25,
  // ComponentKeyConstant.routeItineraryPointFy25,
  // ComponentKeyConstant.routeItineraryPlanFy25,
  // ComponentKeyConstant.hotelAttract,
  // ComponentKeyConstant.productHighlights,
  ComponentKeyConstant.vacationTicketMarket,
  // ComponentKeyConstant.routeItineraryPoint,
  // ComponentKeyConstant.routeItineraryPlan,
  // ComponentKeyConstant.routeFeeDetail,
  // ComponentKeyConstant.routeGraphic,
  // ComponentKeyConstant.ticketBooking,
  // ComponentKeyConstant.detailPackageList,
  // ComponentKeyConstant.packageFestivalList,
  // ComponentKeyConstant.travelOdetail,
  // ComponentKeyConstant.fliggyVacationTravelSummary,
  // ComponentKeyConstant.trafficDescInfo,
  // ComponentKeyConstant.travelDetail,
  // ComponentKeyConstant.travelDescription,
  // ComponentKeyConstant.vacationDetailRoutePresaleProcess,
  ComponentKeyConstant.commonComment,
  ComponentKeyConstant.shopRate,
];

///二屏结构
final List<String> secondScreenStructure = <String>[
  // ComponentKeyConstant.restaurant,
  // ComponentKeyConstant.recommendByPoi,
  // ComponentKeyConstant.commonSpuEntry,
  ComponentKeyConstant.askallVacation,
  ComponentKeyConstant.shop,
  // ComponentKeyConstant.airTicketRecommend,
  ComponentKeyConstant.vacationPublicBenefit,
  ComponentKeyConstant.highlight,
  ComponentKeyConstant.productDesc,
  ComponentKeyConstant.ticketNewScenic,
  ComponentKeyConstant.priceCouponsIntroduce,
  ComponentKeyConstant.priceCouponsIntroduceNew,
  ComponentKeyConstant.itemVideo,
  // ComponentKeyConstant.fliggyDetailFlightReferenceRouteBody,
  // ComponentKeyConstant.travelPolicy,
  ComponentKeyConstant.appointInfo,
  ComponentKeyConstant.routeFeeDetail2,
  ComponentKeyConstant.richContent,
  // ComponentKeyConstant.hotelFacility,
  ComponentKeyConstant.hotelPackageInclude,
  ComponentKeyConstant.hotelNotice,
  ComponentKeyConstant.bookingNotice,
  ComponentKeyConstant.buyNoticeV2,
  // ComponentKeyConstant.feeDesc,
  ComponentKeyConstant.feeInclude,
  // ComponentKeyConstant.feeExclude,
  // ComponentKeyConstant.selfPay,
  // ComponentKeyConstant.shipVacationTravelLine,
  // ComponentKeyConstant.otherDesc,
  // ComponentKeyConstant.feeIncludeInfo,
  // ComponentKeyConstant.feeExcludeInfo,
  // ComponentKeyConstant.refund,
  // ComponentKeyConstant.timeZone,
  // ComponentKeyConstant.bookInfo,
  // ComponentKeyConstant.purchaseNotes,
  // ComponentKeyConstant.useRules,
  // ComponentKeyConstant.specialNotice,
  // ComponentKeyConstant.flightRefund,
  ComponentKeyConstant.buyNoticeSimple,
  // ComponentKeyConstant.storeQualification,
  ComponentKeyConstant.secondLoading,
  ComponentKeyConstant.thirdLoading,
];

///三屏结构
final List<String> thirdScreenStructure = <String>[
  ComponentKeyConstant.recommend,
  ComponentKeyConstant.recommendAcrossShop,
  ComponentKeyConstant.bottomTips,
];

///组件
final Map<String, Component> itemComponentMap = <String, Component>{
  ComponentKeyConstant.headMedia: HeadMediaProcessComponent(),
  // ComponentKeyConstant.itemSelect: ItemSelectComponent(),
  ComponentKeyConstant.normalFactor: NormalFactorComponent(),
  // ComponentKeyConstant.commonBookRule: CommonBookRuleComponent(),
  // ComponentKeyConstant.spuEntry: SpuEntryComponent(),
  ComponentKeyConstant.rateAndShare: RateAndShareComponent(),
  // ComponentKeyConstant.buyEnsure: BuyEnsureComponent(),
  // ComponentKeyConstant.skuInfo: SkuInfoComponent(),
  ComponentKeyConstant.alitripProm: AlitripPromComponent(),
  // ComponentKeyConstant.visaShelf: VisaShelfComponent(),
  // ComponentKeyConstant.visaShelfV2: VisaShelfV2Component(),
  ComponentKeyConstant.serviceGuarantee: ServiceGuaranteeComponent(),
  ComponentKeyConstant.ticketService: TicketServiceComponent(),
  ComponentKeyConstant.routeBrand: RouteBrandComponent(),
  ComponentKeyConstant.serviceEnsure: ServiceEnsureComponent(),
  ComponentKeyConstant.serviceInfo: ServiceInfoComponent(),
  ComponentKeyConstant.serviceSupportShop: ServiceSupportShopComponent(),
  // ComponentKeyConstant.visaProcedure: VisaProcedureComponent(),
  ComponentKeyConstant.platformInsuranceInfo: PlatformInsuranceInfoComponent(),
  ComponentKeyConstant.vacationServerLine: VacationServerLineComponent(),
  // ComponentKeyConstant.ticketPoi: TicketPoiComponent(), // 这个和vacationTicketMarket现在有冲突,新版本理论上都是用的vacationTicketMarket,先把这个注释掉,UI 也写完了,需要的话再看看标展示
  // ComponentKeyConstant.huabeiRights: HuabeiRightsComponent(),
  // ComponentKeyConstant.vacationHotelScenicIntroCard:
  //     VacationHotelScenicIntroCardComponent(),
  ComponentKeyConstant.mindRule: MindRuleComponent(),
  // ComponentKeyConstant.travelHotelStore: TravelHotelStoreComponent(),
  // ComponentKeyConstant.travelHotelMulStore: TravelHotelMulStoreComponent(),
  // ComponentKeyConstant.vacationHotelMemberCard:
  //     VacationHotelMemberCardComponent(),
  // ComponentKeyConstant.hotelMemberCardV2: HotelMemberCardV2Component(),
  ComponentKeyConstant.itemGroupDate: ItemGroupDateComponent(),
  // ComponentKeyConstant.phoneCardShelf: PhoneCardShelfComponent(),
  ComponentKeyConstant.vacationScenicMembershipCard:
      VacationScenicMembershipCardComponent(),
  ComponentKeyConstant.relatedShelf: RelatedShelfComponent(),
  ComponentKeyConstant.stockShelf: StockShelfComponent(),
  // ComponentKeyConstant.flightShelf: FlightShelfComponent(),
  ComponentKeyConstant.flightShelfV2: FlightShelfV2Component(),
  // ComponentKeyConstant.hotelShelf: HotelShelfComponent(),
  // ComponentKeyConstant.vacationDetailFunPackagesV2:
  //     VacationDetailFunPackagesV2Component(),
  // ComponentKeyConstant.packageShelfFilterable:
  //     PackageShelfFilterableComponent(),

  // 先迁走了，旧的后面看可以删除了
  ComponentKeyConstant.dnwPackagesChoose: HotelShelfV2Component(),
  ComponentKeyConstant.ticketShelfV3: TicketShelfV3Component(),
  // ComponentKeyConstant.routeShelf: RouteShelfComponent(),
  // ComponentKeyConstant.hotelAttract: HotelAttractComponent(),
  // ComponentKeyConstant.productHighlights: ProductHighlightsComponent(),
  ComponentKeyConstant.vacationTicketMarket: VacationTicketMarketComponent(),
  // ComponentKeyConstant.routeItineraryPoint: RouteItineraryPointComponent(),
  // ComponentKeyConstant.routeItineraryPlan: RouteItineraryPlanComponent(),
  // ComponentKeyConstant.routeFeeDetail: RouteFeeDetailComponent(),
  // ComponentKeyConstant.ticketBooking: TicketBookingComponent(),
  // ComponentKeyConstant.detailPackageList: DetailPackageListComponent(),
  // ComponentKeyConstant.packageFestivalList: PackageFestivalListComponent(),
  // ComponentKeyConstant.travelOdetail: TravelOdetailComponent(),
  // ComponentKeyConstant.fliggyVacationTravelSummary:
  //     FliggyVacationTravelSummaryComponent(),
  // ComponentKeyConstant.trafficDescInfo: TrafficDescInfoComponent(),
  // ComponentKeyConstant.travelDetail: TravelDetailComponent(),
  // ComponentKeyConstant.travelDescription: TravelDescriptionComponent(),
  // ComponentKeyConstant.vacationDetailRoutePresaleProcess:
  //     VacationDetailRoutePresaleProcessComponent(),
  ComponentKeyConstant.commonComment: CommonCommentComponent(),
  ComponentKeyConstant.shopRate: ShopRateComponent(),
  // ComponentKeyConstant.commonSpuEntry: CommonSpuEntryComponent(),
  ComponentKeyConstant.askallVacation: AskallVacationComponent(),
  ComponentKeyConstant.shop: ShopComponent(),
  // ComponentKeyConstant.airTicketRecommend: AirTicketRecommendComponent(),
  ComponentKeyConstant.vacationPublicBenefit: VacationPublicBenefitComponent(),
  ComponentKeyConstant.highlight: HighlightComponent(),
  ComponentKeyConstant.productDesc: ProductDescComponent(),
  ComponentKeyConstant.ticketNewScenic: TicketNewScenicComponent(),
  ComponentKeyConstant.priceCouponsIntroduce: PriceCouponsIntroduceComponent(),
  ComponentKeyConstant.priceCouponsIntroduceNew:
      PriceCouponsIntroduceNewComponent(),
  ComponentKeyConstant.itemVideo: ItemVideoComponent(),
  // ComponentKeyConstant.fliggyDetailFlightReferenceRouteBody:
  //     FliggyDetailFlightReferenceRouteBodyComponent(),
  // ComponentKeyConstant.travelPolicy: TravelPolicyComponent(),
  ComponentKeyConstant.appointInfo: AppointInfoComponent(),
  ComponentKeyConstant.richContent: RichContentComponent(),
  // ComponentKeyConstant.hotelFacility: HotelFacilityComponent(),
  ComponentKeyConstant.hotelPackageInclude: HotelPackageIncludeComponent(),
  ComponentKeyConstant.hotelNotice: HotelNoticeComponent(),
  ComponentKeyConstant.bookingNotice: BookingNoticeComponent(),
  ComponentKeyConstant.buyNoticeV2: BuyNoticeV2Component(),
  // ComponentKeyConstant.feeDesc: FeeDescComponent(),
  ComponentKeyConstant.feeInclude: FeeIncludeComponent(),
  // ComponentKeyConstant.feeExclude: FeeExcludeComponent(),
  // ComponentKeyConstant.selfPay: SelfPayComponent(),
  // ComponentKeyConstant.shipVacationTravelLine:
  //     ShipVacationTravelLineComponent(),
  // ComponentKeyConstant.otherDesc: OtherDescComponent(),
  // ComponentKeyConstant.feeIncludeInfo: FeeIncludeInfoComponent(),
  // ComponentKeyConstant.feeExcludeInfo: FeeExcludeInfoComponent(),
  // ComponentKeyConstant.timeZone: TimeZoneComponent(),
  // ComponentKeyConstant.bookInfo: BookInfoComponent(),
  // ComponentKeyConstant.purchaseNotes: PurchaseNotesComponent(),
  // ComponentKeyConstant.useRules: UseRulesComponent(),
  // ComponentKeyConstant.specialNotice: SpecialNoticeComponent(),
  // ComponentKeyConstant.flightRefund: FlightRefundComponent(),
  ComponentKeyConstant.buyNoticeSimple: BuyNoticeSimpleComponent(),
  // ComponentKeyConstant.storeQualification: StoreQualificationComponent(),
  ComponentKeyConstant.recommend: RecommendComponent(),
  ComponentKeyConstant.recommendAcrossShop: RecommendAcrossShopComponent(),
  ComponentKeyConstant.bottomTips: BottomTipsComponent(),
  // ComponentKeyConstant.routeShelfFy25: RouteShelfFy25Component(),
  // ComponentKeyConstant.routeItineraryPointFy25: RouteItineraryPointFy25Component(),
  // ComponentKeyConstant.routeItineraryPlanFy25: RouteItineraryPlanFy25Component(),
  // ComponentKeyConstant.routeGraphic: RouteGraphicComponent(),
  // ComponentKeyConstant.restaurant: RestaurantComponent(),
  // ComponentKeyConstant.recommendByPoi: RecommendByPoiComponent(),
  ComponentKeyConstant.routeFeeDetail2: RouteFeeDetail2Component(),
  ComponentKeyConstant.rateAndServer: RateAndServerComponent(),
  ComponentKeyConstant.zeroSaveBuyRule: ZeroSaveBuyRuleComponent(),
  ComponentKeyConstant.rankListInfo: RankListInfoComponent(),
  ComponentKeyConstant.bottomRadiusDivider: BottomRadiusDividerComponent(),
  ComponentKeyConstant.topRadiusDivider: TopRadiusDividerComponent(),
  ComponentKeyConstant.commonDivider: CommonDividerComponent(),
  ComponentKeyConstant.titleBar: TitleBarComponent(),
  ComponentKeyConstant.bottomBar: BottomBarComponent(),
  ComponentKeyConstant.bottomBarConsult: BottomBarConsultComponent(),
  ComponentKeyConstant.bottomBarConsultV2: BottomBarConsultV2Component(),
  ComponentKeyConstant.bottomBarHit: BottomBarHitComponent(),
  ComponentKeyConstant.billionService: BillionServiceComponent(),
  ComponentKeyConstant.floatLive: FloatLiveComponent(),
  ComponentKeyConstant.secondLoading: SecondLoadingComponent(),
  ComponentKeyConstant.thirdLoading: ThirdLoadingComponent(),
};

class PageStructureManager {}

import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fliggy_design/fliggy_design.dart';
import '../../../track/exposure_container.dart';
import '../../null_widget.dart';
import 'model.dart';
import 'notifier.dart';
import 'package:fround_image/fround_image.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:ficonfont/ficonfont.dart' as FiconFont;

class FloatLiveWidget extends StatefulWidget {
  const FloatLiveWidget({Key? key}) : super(key: key);

  @override
  State<FloatLiveWidget> createState() => _FloatLiveWidgetState();
}

class _FloatLiveWidgetState extends State<FloatLiveWidget> {
  // bool enable = true;

  @override
  Widget build(BuildContext context) {
    final FloatLiveChangeNotifier changeNotifier =
        Provider.of<FloatLiveChangeNotifier>(context);
    final FloatLiveModel? floatLiveModel =
        changeNotifier.itemDetailModel.floatLiveModel;

    if (floatLiveModel == null || floatLiveModel.icon == null) {
      return nullWidget;
    }

    const String spmCD = 'float_live.default';
    const String controlName = 'float_live_default';

    // build时曝光
    changeNotifier.ctrlExposure(context, spmCD, <String, String>{});
// return Container();
    return GestureDetector(
      onTap: () {
        changeNotifier
            .ctrlClicked(context, spmCD, controlName, <String, String>{});
        FliggyNavigatorApi.getInstance()
            .push(context, floatLiveModel!.jumpUrl!);
      },
      child: SizedBox(
        height: floatLiveModel.headShow != null ? 45 : 50,
        // width: 68,
        child: Stack(
          children: <Widget>[
            if (floatLiveModel.headShow != null)
              // 头像
              Container(
                alignment: Alignment.topCenter,
                child: FRoundImage.network(
                  floatLiveModel!.headShow!,
                  height: 40,
                  width: 40,
                ),
              ),
            // 下面标签标题
            if (floatLiveModel.headShow != null)
              Container(
                alignment: Alignment.bottomCenter,
                child: FRoundImage.network(
                  floatLiveModel!.icon!,
                  height: 14,
                ),
              )
            else if (floatLiveModel.isInLiving ?? false)
              _liveButtonWithTitle()
            else
              _playBackButtonWithTitle()
          ],
        ),
      ),
    );
  }

  Widget _playBackButtonWithTitle() => _titleWithIconButton(
      title: '看讲解',
      iconUrl:
          'https://gw.alicdn.com/imgextra/i2/O1CN01MZS2E01YDV9ItXyiU_!!6000000003025-2-tps-128-128.png');

  Widget _liveButtonWithTitle() => _titleWithIconButton(
      title: '直播中',
      iconUrl:
          'https://gw.alicdn.com/imgextra/i3/O1CN01SN5mrH1D12RBhIgvY_!!6000000000155-1-tps-128-128.gif');

  Widget _titleWithIconButton(
          {required String title, required String iconUrl}) =>
      SizedBox(
          width: 41,
          height: 41,
          child: ClipRRect(
              borderRadius: BorderRadius.circular(6),
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 2, sigmaY: 4),
                child: Container(
                  color: Colors.white.withOpacity(0.72), // 调整透明度
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      FRoundImage.network(
                        iconUrl,
                        width: 19,
                        height: 19,
                      ),
                      const SizedBox(
                        height: 2,
                      ),
                      Text(
                        title,
                        style: const TextStyle(
                            fontSize: 9,
                            fontWeight: FontWeight.w500,
                            color: fd.color_darkgray),
                      ),
                    ],
                  ),
                ),
              )));
}

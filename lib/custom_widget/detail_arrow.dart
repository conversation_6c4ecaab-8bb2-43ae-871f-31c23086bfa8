import 'dart:math';

import 'package:flutter/cupertino.dart';
///参考视觉稿
///https://mgdone.alibaba-inc.com/file/132373049478672?fileOpenFrom=search_result&page_id=M&shareId=8be03270-e56c-4045-89f8-4093a214422c
/// 小箭头组
Widget rightArrowSmall = Image.network('https://gw.alicdn.com/imgextra/i2/O1CN01XZbJ3z22OtSnMaORL_!!6000000007111-2-tps-15-24.png',width: 3.5,height: 6,fit: BoxFit.fill,);
Widget rightArrowSmallWhite = Image.network('https://gw.alicdn.com/imgextra/i4/O1CN01g3dx8p1PmvEAGvymo_!!6000000001884-2-tps-15-24.png',width: 3.5,height: 6,fit: BoxFit.fill,);
Widget rightArrowSmallOrange = Image.network('https://gw.alicdn.com/imgextra/i2/O1CN01ro5jjf1J5yPsYIiqn_!!6000000000978-2-tps-14-24.png',width: 3.5,height: 6,fit: BoxFit.fill,);

Widget topArrowSmall = Image.network('https://gw.alicdn.com/imgextra/i4/O1CN01KSghnb1rexaE5l177_!!6000000005657-2-tps-24-15.png',width: 6,height: 3.5,fit: BoxFit.fill,);

Widget bottomArrowSmall = Image.network('https://gw.alicdn.com/imgextra/i1/O1CN01UZeYkH1QFmBC5XJW6_!!6000000001947-2-tps-24-15.png',width: 6,height: 3.5,fit: BoxFit.fill,);

/// 大箭头组
Widget rightArrowBig = Image.network('https://gw.alicdn.com/imgextra/i2/O1CN01XZbJ3z22OtSnMaORL_!!6000000007111-2-tps-15-24.png',width: 5.5,height: 9,fit: BoxFit.fill,);

Widget topArrowBig = Transform.rotate(
    angle: pi / 2, // 90度，使用弧度制
    child:rightArrowBig,
);

Widget bottomArrowBig = Transform.rotate(
    angle: (pi * 3) / 2, // 90度，使用弧度制
    child: rightArrowBig,
);
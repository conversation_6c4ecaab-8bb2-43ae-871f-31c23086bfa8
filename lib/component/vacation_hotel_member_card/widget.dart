import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class VacationHotelMemberCardWidget extends StatelessWidget {
  const VacationHotelMemberCardWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VacationHotelMemberCardChangeNotifier changeNotifier =
        Provider.of<VacationHotelMemberCardChangeNotifier>(context);
    return Container();
  }
}
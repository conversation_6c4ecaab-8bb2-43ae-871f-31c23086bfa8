import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../custom_widget/block_title.dart';
import 'notifier.dart';
import 'package:flutter_common/api/empty.dart';
import 'package:fliggy_router/fliggy_router.dart';
class AskallVacationWidget extends StatelessWidget {
  const AskallVacationWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AskallVacationChangeNotifier changeNotifier =
        Provider.of<AskallVacationChangeNotifier>(context);
    changeNotifier.fromJson();
    final Map<dynamic, dynamic>? everybodySayModel = changeNotifier.everybodySayModel;
    if (everybodySayModel == null) {
      return empty;
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
      color: const Color(0xFFFFFFFF),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            BlockTitle(everybodySayModel['title'],btnText: everybodySayModel['nums'],onButtonClick: (){
              FliggyNavigatorApi.getInstance()
                                .push(context, everybodySayModel['jumpH5Url'], anim: Anim.slide);
            },),
            Padding(
              padding: const EdgeInsets.only(top: 9, bottom: 9),
              child: _buildSays(everybodySayModel['values']),
            ),
          ]));
  }
  Widget _buildSays(List<dynamic> datas) {
    if (datas == null) {
      return empty;
    }
    final List <Widget> widgets = <Widget>[];
    for (final String data in datas) {
      widgets.add(Text(
          data,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(
            color: Color(0xFF333333),
            fontSize: 13,
          ),
        ),
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: widgets);

  }
}

import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import './mock.dart';
class AskallVacationChangeNotifier extends ComponentChangeNotifier {
  AskallVacationChangeNotifier(ComponentContext context) : super(context);
      Map<dynamic, dynamic>? everybodySayModel;

  @override
  void fromJson() {
    //mock数据
    // everybodySayModel = mockData['everybodySay']['data'];
    // return;
    if (dataModel == null || dataModel['everybodySay'] == null || dataModel['everybodySay']['data'] == null) {
      return;
    }
    everybodySayModel = dataModel['everybodySay']['data'];
  }
}

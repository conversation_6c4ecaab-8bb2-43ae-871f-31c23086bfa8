import 'package:flutter/material.dart';

class BottomTipsWidget extends StatelessWidget {
  const BottomTipsWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120,
      padding:const EdgeInsets.only(top: 10),
      alignment: Alignment.topCenter,
      child: const Text(
        '已经到底喽',
        style: TextStyle(fontSize: 12, color: Color(0xff999999)),
      ),
    );
  }
}

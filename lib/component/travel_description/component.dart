import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description TODO 组件功能
class TravelDescriptionComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<TravelDescriptionChangeNotifier>.value(
      value: TravelDescriptionChangeNotifier(context),
      child: const TravelDescriptionWidget(),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class TravelDescriptionWidget extends StatelessWidget {
  const TravelDescriptionWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TravelDescriptionChangeNotifier changeNotifier =
        Provider.of<TravelDescriptionChangeNotifier>(context);
    return Container();
  }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class RouteFeeDetailWidget extends StatelessWidget {
  const RouteFeeDetailWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RouteFeeDetailChangeNotifier changeNotifier =
        Provider.of<RouteFeeDetailChangeNotifier>(context);
    return Container();
  }
}
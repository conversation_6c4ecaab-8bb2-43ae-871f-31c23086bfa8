import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class VacationMemberPriceWidget extends StatelessWidget {
  const VacationMemberPriceWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VacationMemberPriceChangeNotifier changeNotifier =
        Provider.of<VacationMemberPriceChangeNotifier>(context);
    return Container();
  }
}
import 'package:flutter/cupertino.dart';

import '../../utils/safe_access.dart';
import 'package:fjson_exports/fjson_exports.dart';

class PriceCouponsIntroduceModel {
  PriceCouponsIntroduceModel();

  /// 左上角标题
  String? title;

  List<PriceCouponsDescModel>? descList;
  List<PriceCouponsDescModel>? subDescList;
  bool? canExpand;

  /// 是否展开
  ValueNotifier<bool> isExpand = ValueNotifier<bool>(true);

  factory PriceCouponsIntroduceModel.fromJson(Map<dynamic, dynamic> json) {
    final PriceCouponsIntroduceModel model = PriceCouponsIntroduceModel();
    model.title = safeString(json['title']);
    model.descList = <PriceCouponsDescModel>[];
    for (final dynamic item
        in safeNonNullList(json['coupons'], (dynamic e) => e)) {
      model.descList!.add(PriceCouponsDescModel.fromJson(item));
    }
    if ((model.descList?.length ?? 1) > 2) {
      model.canExpand = true;
      model.isExpand.value = false;
      model.subDescList = <PriceCouponsDescModel>[];
      model.subDescList!.add(model.descList![0]);
      model.subDescList!.add(model.descList![1]);
    }

    return model;
  }
}

class PriceCouponsDescModel {
  PriceCouponsDescModel();

  /// 左边标题
  String? title;

  /// 右边文案，暂时都是文字，不设置更深结构了
  String? text;

  /// 右边文案列表，暂时都是文字，不设置更深结构了
  List<PriceCouponsTextList>? textList;
  String? value;

  factory PriceCouponsDescModel.fromJson(Map<dynamic, dynamic> json) {
    final PriceCouponsDescModel model = PriceCouponsDescModel();

    model.value = json['value'];
    model.title = json['title'];

    model.textList = <PriceCouponsTextList>[];
    if (json.containsKey('textList')) {
      for (final dynamic item
          in safeNonNullList(json['textList'], (dynamic e) => e)) {
        final Map<String, dynamic> map = safeNonNullMap(item, (dynamic e) => e);
        model.textList!.add(PriceCouponsTextList.fromJson(map));
      }
    }

    if (model.value == null && json.containsKey('text')) {
      model.text = safeNonNullString(json['text']);
    }

    return model;
  }
}

class PriceCouponsTextList {
  String? text;
  bool? highlight;

  PriceCouponsTextList.fromJson(Map<String, dynamic> json) {
    text = SafeAccess.safeParseString(json['text']);
    highlight = SafeAccess.safeParseBoolean(json['highlight']);
  }
}

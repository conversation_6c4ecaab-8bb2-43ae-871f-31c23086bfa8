import '../../custom_widget/block_title.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../utils/common_config.dart';
import 'model.dart';
import 'notifier.dart';

class PriceCouponsIntroduceWidget extends StatelessWidget {
  const PriceCouponsIntroduceWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final PriceCouponsIntroduceChangeNotifier changeNotifier =
        Provider.of<PriceCouponsIntroduceChangeNotifier>(context);
    if (changeNotifier.itemDetailModel.priceCouponsIntroduceModel == null ||
        (changeNotifier.itemDetailModel.itemModel?.couponItem ?? false)) {
      return SizedBox.shrink();
    }
    changeNotifier.ctrlExposure(context, 'yjq_couponlist.default', null);
    return Container(
      decoration: BoxDecoration(
          color: Color(0xFFFFFFFF), borderRadius: BorderRadius.circular(6)),
      padding: EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 12),
      margin: const EdgeInsets.only(bottom: itemDivider),
      child: Column(
        children: <Widget>[
          BlockTitle(changeNotifier
                  .itemDetailModel.priceCouponsIntroduceModel!.title ??
              ''),
          if (changeNotifier
                  .itemDetailModel.priceCouponsIntroduceModel!.descList !=
              null)
            ...changeNotifier
                .itemDetailModel.priceCouponsIntroduceModel!.descList!
                .asMap()
                .entries
                .map((MapEntry<int, PriceCouponsDescModel> desc) =>
                _buildTableRow(context,changeNotifier,desc.value, desc.key))
                .toList(),


        ],
      ),
    );
  }

  Widget _buildTableRow(BuildContext context,PriceCouponsIntroduceChangeNotifier changeNotifier,PriceCouponsDescModel subValue,int index) {
    changeNotifier.ctrlExposure(context, 'yjq_couponlist.$index', null);
    return Column(
      children: <Widget>[
        Container(
          height: 0.5,
          width: 333,
          margin: EdgeInsets.only(
              top: index > 0
                  ? 9
                  : 0),
          color: Color(0xFFEBEDF0),
        ),
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Container(
                width: 0.5,
                color: Color(0xFFEBEDF0),
              ),
              Container(
                width: 90,
                color: Color(0xFFF7F8FA),
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Center(
                  child: Text(
                    subValue.title ?? '',
                    style: TextStyle(fontSize: 13),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              Container(
                width: 0.5,
                color: Color(0xFFEBEDF0),
              ),
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 15, horizontal: 10.5),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      if (subValue.text != null)
                        Text(
                          subValue.text!,
                          style: TextStyle(
                              fontSize: 13,
                              color: Color(0xFF92949A),
                              height: 1.3),
                        ),
                      if (subValue.textList != null)
                        ...subValue.textList!
                            .map((PriceCouponsTextList text) => Text(
                                  text.text ?? '',
                                  style: TextStyle(
                                      fontSize: 13,
                                      color: Color(0xFF92949A),
                                      height: 1.3),
                                ))
                            .toList(),
                    ],
                  ),
                ),
              ),
              Container(
                width: 0.5,
                color: Color(0xFFEBEDF0),
              ),
            ],
          ),
        ),
        /// 这条是补的最下面的分割线
        Container(
          height: 0.5,
          width: 333,
          color: Color(0xFFEBEDF0),
        ),
      ],
    );
  }
}

///组件名字和h5是对应的，开发时可以参考h5逻辑：https://code.alibaba-inc.com/rxpi/travel-detail/blob/master/config/page/next.js
class ComponentKeyConstant {

  static const String headMedia = 'headMedia'; /// 商详头图 ++  日常价格  ++ 大促价格  ++ 会员价格  ++ 领劵优惠  ++ 百亿补贴等发劵  ++ 心智和销量  ++ 主标题  ++

  static const String billionService = 'billionService'; // 10亿服务保障  ++

  static const String itemSelect = 'itemSelect'; // 酒景的商品切换能力

  static const String normalFactor = 'normalFactor'; // 通用类目商品特点  ++

  static const String commonBookRule = 'commonBookRule'; // 通用囤好货心智区

  static const String spuEntry = 'spuEntry'; // 「境内跟团游」Spu页入口

  static const String rateAndShare = 'rateAndShare'; // 分享和评分  ++

  static const String rateAndServer = 'rateAndServer'; //fy25新增服务和评价

  static const String buyEnsure = 'buyEnsure'; // 飞猪购

  static const String skuInfo = 'skuInfo'; // 飞猪购

  static const String alitripProm = 'alitripProm'; // 大促引导 ++

  static const String visaShelf = 'visaShelf'; // 签证货架
  static const String visaShelfV2 = 'visaShelfV2'; // 签证货架

  static const String serviceGuarantee = 'serviceGuarantee'; // 新版，服务模块

  static const String ticketService = 'ticketService';// 「国内票务」服务保障  ++
  static const String routeBrand = 'routeBrand';

  static const String serviceEnsure = 'serviceEnsure'; // 「国内票务」服务保障  ++

  static const String serviceInfo = 'serviceInfo';

  static const String serviceSupportShop = 'serviceSupportShop';

  static const String visaProcedure = 'visaProcedure'; // 签证流程

  static const String platformInsuranceInfo = 'platformInsuranceInfo'; // 包车权益 ++

  static const String vacationServerLine = 'vacationServerLine'; // 通用服务 ++

  static const String ticketPoi = 'ticketPoi';

  static const String huabeiRights = 'huabeiRights'; // 花呗权益

  static const String vacationHotelScenicIntroCard = 'vacationHotelScenicIntroCard';

  static const String mindRule = 'mindRule';
  static const String travelHotelStore = 'travelHotelStore';

  static const String vevelHotelStore = 'travelHotelStore';

  static const String travelHotelMulStore = 'travelHotelMulStore';

  static const String vacationHotelMemberCard = 'vacationHotelMemberCard';

  static const String hotelMemberCardV2 = 'hotelMemberCardV2';//和vacationScenicMembershipCard同一个组件

  static const String itemGroupDate = 'itemGroupDate';

  static const String phoneCardShelf = 'phoneCardShelf';

  static const String vacationScenicMembershipCard = 'vacationScenicMembershipCard'; // 「门票/酒景」会员卡片，主供长隆

  static const String relatedShelf = 'relatedShelf';//货架

  static const String stockShelf = 'stockShelf';

  static const String flightShelf = 'flightShelf';

  static const String flightShelfV2 = 'flightShelfV2';

  static const String hotelShelf = 'hotelShelf'; //「在线预约」新货架
  static const String playShelf = 'playShelf'; //「fy25大玩乐」新货架

  static const String vacationDetailFunPackagesV2 = 'vacationDetailFunPackagesV2'; // 「酒景套餐」老货架，境外玩乐等货架

  static const String packageShelfFilterable = 'packageShelfFilterable'; // 「酒景套餐」新货架

  static const String dnwPackagesChoose = 'dnwPackagesChoose'; // 「在线预约套餐」老货架

  static const String ticketShelfV3 = 'ticketShelfV3'; // 「国内票务」新货架 ++

  static const String zeroSaveBuyRule = 'zeroSaveBuyRule'; // 0元囤
  static const String rankListInfo = 'rankListInfo'; // 「全行业」排名信息

  static const String routeShelf = 'routeShelf'; // 「境内跟团游」货架
  static const String routeShelfFy25 = 'routeShelfFy25'; // 「fy25新版线路」货架
  static const String routeItineraryPointFy25 = 'routeItineraryPointFy25'; // 「fy25境内跟团游」行程亮点
  static const String routeItineraryPlanFy25 = 'routeItineraryPlanFy25'; // 「fy25境内跟团游」行程概览

  static const String hotelAttract = 'hotelAttract'; // 「在线预约」囤好货引流

  static const String hotelPackageToCalendar = 'hotelPackageToCalendar'; // 酒店套餐日历互通

  static const String productHighlights = 'productHighlights'; // 机票产品亮点

  static const String vacationTicketMarket = 'vacationTicketMarket'; // 「国内票务」推荐热卖,二销货架

  static const String routeItineraryPoint = 'routeItineraryPoint'; // 「境内跟团游」行程亮点

  static const String routeItineraryPlan = 'routeItineraryPlan'; // 「境内跟团游」行程概览

  static const String routeFeeDetail = 'routeFeeDetail'; // 「境内跟团游」行程费用明细 费用包含/费用不含
  static const String routeGraphic = 'routeGraphic'; // 「fy25新版」线路图文挪一屏

  static const String ticketBooking = 'ticketBooking'; // 门票二次预约业务，在有价劵类目展示

  static const String detailPackageList = 'detailPackageList'; // 线路，非境内跟团游类目共用

  static const String packageFestivalList = 'packageFestivalList';

  static const String travelOdetail = 'travelOdetail';

  static const String fliggyVacationTravelSummary = 'fliggyVacationTravelSummary';

  static const String trafficDescInfo = 'trafficDescInfo';

  static const String travelDetail = 'travelDetail';

  static const String travelDescription = 'travelDescription';

  static const String vacationDetailRoutePresaleProcess = 'vacationDetailRoutePresaleProcess'; // 线路，二次预约流程

  static const String commonComment = 'commonComment';//  商品评价 ++

  static const String comment = 'comment';//  ++

  static const String commentVacation = 'commentVacation'; // 两版评价，后续会统一  ++

  static const String shopRate = 'shopRate';
  ///-----------二屏
  static const String restaurant = 'restaurant';//「fy25大玩乐」餐厅简介
  static const String recommendByPoi = 'recommendByPoi';

  static const String commonSpuEntry = 'commonSpuEntry';

  static const String askallVacation = 'askallVacation';

  static const String shop = 'shop'; // 店铺卡片 + 店铺推荐 ++

  static const String airTicketRecommend = 'airTicketRecommend'; // 机票交叉签证

  static const String vacationPublicBenefit = 'vacationPublicBenefit'; // 公益宝贝

  static const String highlight = 'highlight'; // 通用，商家说亮点  ++

  static const String productDesc = 'productDesc';

  static const String ticketNewScenic = 'ticketNewScenic';

  static const String priceCouponsIntroduce = 'priceCouponsIntroduce';// 有价券说明
  static const String priceCouponsIntroduceNew = 'priceCouponsIntroduceNew';// 有价券说明

  static const String itemVideo = 'itemVideo'; // 「国内票务」宝贝视频

  static const String fliggyDetailFlightReferenceRouteBody = 'fliggyDetailFlightReferenceRouteBody';

  static const String travelPolicy = 'travelPolicy'; // 「境内跟团游」当地政策

  static const String appointInfo = 'appointInfo'; // 线路，商品亮点
  static const String routeFeeDetail2 = 'routeFeeDetail2'; // 「fy25」线路费用信息挪二屏

  static const String richContent = 'richContent'; // 图文详情  ++

  static const String hotelFacility = 'hotelFacility';

  static const String hotelPackageInclude = 'hotelPackageInclude';

  static const String hotelNotice = 'hotelNotice';

  static const String bookingNotice = 'bookingNotice';

  static const String buyNoticeV2 = 'buyNoticeV2';

  static const String feeDesc = 'feeDesc';

  static const String feeInclude = 'feeInclude'; //  杂七杂八的说明

  static const String feeExclude = 'feeExclude';

  static const String selfPay = 'selfPay';

  static const String shipVacationTravelLine = 'shipVacationTravelLine';

  static const String otherDesc = 'otherDesc';

  static const String feeIncludeInfo = 'feeIncludeInfo';

  static const String feeExcludeInfo = 'feeExcludeInfo';

  static const String refund = 'refund';

  static const String timeZone = 'timeZone';

  static const String bookInfo = 'bookInfo';

  static const String purchaseNotes = 'purchaseNotes';

  static const String useRules = 'useRules';

  static const String specialNotice = 'specialNotice';

  static const String flightRefund = 'flightRefund';

  static const String buyNoticeSimple = 'buyNoticeSimple'; // 新版，二屏购买信息合并

  static const String storeQualification = 'storeQualification'; // 「微信定制」商家资质展示

  static const String recommend = 'recommend'; // 三屏推荐  ++看了又看
  static const String recommendAcrossShop = 'recommendAcrossShop'; // 三屏推荐  ++邻家好货
  static const String bottomTips = 'bottomTips'; // 已经到底了

  static const String bottomRadiusDivider = 'bottomRadiusDivider';// 底部圆角分割线  ++
  static const String topRadiusDivider = 'topRadiusDivider';// 底部圆角分割线  ++
  static const String commonDivider = 'commonDivider';// 分割线  ++
  static const String titleBar = 'titleBar';// titleBar  ++
  static const String bottomBar = 'bottomBar';// bottomBar

  static const String bottomBarConsult = 'bottomBarConsult'; // 底部咨询条
  static const String bottomBarConsultV2 = 'bottomBarConsultV2'; // 底部咨询条
  static const String bottomBarHit = 'bottomBarHit'; // 底部咨询条

  static const String floatLive = 'floatLive'; // 页面里固定的直播入口，不播放
  static const String secondLoading = 'secondLoading'; // 二屏loading页面
  static const String thirdLoading = 'thirdLoading'; // 三屏loading页面


}



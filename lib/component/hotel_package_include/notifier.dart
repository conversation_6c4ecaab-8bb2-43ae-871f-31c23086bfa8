import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';

class HotelPackageIncludeChangeNotifier extends ComponentChangeNotifier {
  Map<dynamic, dynamic>? moduleData;
  HotelPackageIncludeChangeNotifier(ComponentContext context) : super(context);
  @override
  void fromJson() {
    //mock数据
    // moduleData = mockData['appoint']['data'];
    // return;
    if (dataModel['hotelPackageInclude'] == null || dataModel['hotelPackageInclude']['data'] == null) {
      return;
    }
    moduleData = dataModel['hotelPackageInclude']['data'];
  }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class AirTicketRecommendWidget extends StatelessWidget {
  const AirTicketRecommendWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AirTicketRecommendChangeNotifier changeNotifier =
        Provider.of<AirTicketRecommendChangeNotifier>(context);
    return Container();
  }
}
import 'package:flutter/cupertino.dart';
import 'package:fliggy_router/fliggy_router.dart';
import '../../custom_widget/dialog_webview.dart';
import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import '../comment_vacation/model.dart';
import '../rate_and_share/model.dart';
import '../service_guarantee/model.dart';
import '../shop/shop_card/shop_card_model.dart';
import 'model.dart';

class RateAndServerChangeNotifier extends ComponentChangeNotifier {
  CommonBrandVOList? get serverData =>
      itemDetailModel.serviceGuaranteeDataModel?.commonBrandVOList?[0];

  CommentVacationModel? get rateData => itemDetailModel.commentVacationModel;

  ShareContent? get shareContent =>
      itemDetailModel.rateAndShareDataModel?.shareContent;

  SpuRankInfoModel? get rankInfo => itemDetailModel.spuRankInfoModel;

  ShopCardDataModel? get shopData => itemDetailModel.shopCardDataModel;

  RateAndServerChangeNotifier(ComponentContext context) : super(context);

  void serverClick(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      ctrlClicked(context, 'basicInfo.guarantee', 'basicInfo_guarantee', null);
      final DialogWebView dialogWebView =
          DialogWebView(context: context, url: url,itemDetailEngine: itemDetailEngine);
      dialogWebView.showPop();
    }
  }
  void rateClick(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      ctrlClicked(context, 'basicInfo.simple_rate', 'basicInfo_simple_rate', null);
      FliggyNavigatorApi.getInstance().push(context, url);
    }
  }
  void rankClick(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      ctrlClicked(context, 'basicInfo.rank', 'basicInfo_rank', null);
      FliggyNavigatorApi.getInstance().push(context, url);
    }
  }
  void shopClick(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      ctrlClicked(context, 'basicInfo.shop', 'basicInfo_shop', null);
      FliggyNavigatorApi.getInstance().push(context, url);
    }
  }
}

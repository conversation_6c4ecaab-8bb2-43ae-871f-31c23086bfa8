import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fround_image/fround_image.dart';
import '../../custom_widget/carousel_slider/carousel_slider.dart';
import '../../custom_widget/detail_arrow.dart';
import '../../utils/TextConfig.dart';
import '../../utils/common_config.dart';
import '../comment_vacation/model.dart';
import '../rate_and_share/model.dart';
import '../service_guarantee/model.dart';
import '../shop/shop_card/shop_card_model.dart';
import 'model.dart';
import 'notifier.dart';

class RateAndServerWidget extends StatelessWidget {
  const RateAndServerWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RateAndServerChangeNotifier changeNotifier =
        Provider.of<RateAndServerChangeNotifier>(context);
    final RateAndShareDataModel? rateAndShareDataModel =
        changeNotifier.itemDetailModel.rateAndShareDataModel;
    // 比 rate and share优先级低
    if (rateAndShareDataModel != null) {
      return const SizedBox.shrink();
    }

    return Container(
      color: const Color(0xFFFFFFFF),
      padding:
          const EdgeInsets.only(left: paddingLeft, right: paddingRight, top: 6),
      child: Column(
        children: <Widget>[
          if (changeNotifier.serverData != null)
            _buildServer(context, changeNotifier, changeNotifier.serverData!),
          if (changeNotifier.rateData != null &&
              changeNotifier.rateData?.score != '')
            _buildRate(context, changeNotifier, changeNotifier.rateData!),
          if (changeNotifier.shareContent != null)
            Container(
              margin: const EdgeInsets.fromLTRB(0, 12, 0, 0),
              height: 20.00,
              decoration: const BoxDecoration(
                image: DecorationImage(
                    image: NetworkImage(
                        'https://gw.alicdn.com/imgextra/i3/O1CN01UjnEnE26g4cm8QAqG_!!6000000007690-2-tps-1332-156.png'),
                    fit: BoxFit.fill),
              ),
              child: _buildShareContent(
                  context, changeNotifier, changeNotifier.shareContent!),
            ),
          if (changeNotifier.rankInfo != null &&
              changeNotifier.rankInfo?.desc != '')
            _buildRank(context, changeNotifier, changeNotifier.rankInfo!),
          if (changeNotifier.shopData != null &&
              (changeNotifier.shopData?.topEntry ?? false))
            _buildShop(context, changeNotifier, changeNotifier.shopData!),
        ],
      ),
    );
  }

  Widget _buildServer(BuildContext context,
      RateAndServerChangeNotifier changeNotifier, CommonBrandVOList serveData) {
    return GestureDetector(
      onTap: () {
        changeNotifier.serverClick(context, serveData.popWindowUrl);
      },
      child: Padding(
        padding: const EdgeInsets.only(top: 4, bottom: 4),
        child: Container(
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                  colors: <Color>[Color(0xFFfff9de), Color(0xFFffffff)]),
              // color: const Color(0xFFfff9de),
              borderRadius: BorderRadius.circular(6),
            ),
          child: Row(
            children: <Widget>[
              if (serveData.icon != '')
                Image.network(
                  serveData.icon!,
                  height: 16,
                ),
              // if (serveData.icon != '')
              //   Container(
              //     margin: const EdgeInsets.only(left: 5, right: 5),
              //     width: 1,
              //     height: 12,
              //     color: const Color(0xff919499),
              //   ),
              Padding(
                  padding: const EdgeInsets.only(left: 4),
                  child: Text(
                    serveData.mainInfo!,
                    style:
                        const TextStyle(color: Color(0xff0F131A), fontSize: 12),
                  )),
              const Spacer(),
              rightArrowSmall,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRate(
      BuildContext context,
      RateAndServerChangeNotifier changeNotifier,
      CommentVacationModel rateData) {
    return GestureDetector(
      onTap: () {
        changeNotifier.rateClick(context, rateData.jumpUrl);
      },
      child: Row(
        children: <Widget>[
          Container(
            height: 18,
            padding: EdgeInsets.only(
                left: 6, right: 6, bottom: Platform.isIOS ? 2 : 0),
            margin: const EdgeInsets.only(right: 3),
            alignment: Alignment.center,
            decoration: BoxDecoration(
                color: const Color(0xff6666FF),
                borderRadius: BorderRadius.circular(6.00)),
            child: Text(rateData.score!,
                style: TextStyle(
                    color: Color(0xffffffff),
                    fontSize: 14,
                    fontWeight: FontWeightExt.bold, height: 1.3)),
          ),
          Text(
            rateData.scoreDesc!,
            style: const TextStyle(color: Color(0xff6666FF), fontSize: 12),
          ),
          if (rateData.textList!.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(left: 5, right: 5),
              width: 1,
              height: 12,
              color: const Color(0xff919499),
            ),
          if (rateData.textList!.isNotEmpty)
            Expanded(
              child: Container(
                padding: const EdgeInsets.only(right: 12),
                child: CarouselSlider(
                  items: rateData.textList!.map((String text) {
                    return Builder(
                      builder: (BuildContext context) {
                        return Text(text,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                                color: Color(0xff0F131A), fontSize: 12.0));
                      },
                    );
                  }).toList(),
                  options: CarouselOptions(
                    autoPlay: true,
                    aspectRatio: 90 / 14,
                    viewportFraction: 1,
                    scrollDirection: Axis.vertical,
                    autoPlayInterval: const Duration(seconds: 2),
                    scrollPhysics: NeverScrollableScrollPhysics(),
                  ),
                ),
              ),
            ),
          Container(
            margin: const EdgeInsets.only(right: 4),
            child: Text(
              rateData.totalCountText!,
              style: const TextStyle(color: Color(0xff0F131A), fontSize: 12),
            ),
          ),
          rightArrowSmall,
        ],
      ),
    );
  }

  Widget _buildShareContent(BuildContext context,
      RateAndServerChangeNotifier changeNotifier, ShareContent shareContent) {
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 7, 0, 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            height: 14,
            margin: const EdgeInsets.fromLTRB(0, 1, 0, 0),
            child: CarouselSlider(
              items: shareContent.tagList!.map((TagList text) {
                return Builder(
                  builder: (BuildContext context) {
                    return Text(text.tagName!,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                            color: Color.fromARGB(255, 255, 57, 77),
                            fontSize: 12.0));
                  },
                );
              }).toList(),
              options: CarouselOptions(
                autoPlay: true,
                aspectRatio: 80 / 14,
                viewportFraction: 1,
                scrollDirection: Axis.vertical,
                autoPlayInterval: const Duration(seconds: 2),
                scrollPhysics: NeverScrollableScrollPhysics(),
              ),
            ),
          ),
          Expanded(
            child: Container(
              margin: const EdgeInsets.fromLTRB(9, 0, 9, 0),
              child: Text(shareContent.desc!,
                  textAlign: TextAlign.left,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                      color: Color.fromARGB(255, 15, 19, 26), fontSize: 12.00)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRank(BuildContext context,
      RateAndServerChangeNotifier changeNotifier, SpuRankInfoModel rankInfo) {
    return GestureDetector(
      onTap: () {
        changeNotifier.rankClick(context, rankInfo.jumpInfo?.jumpH5Url);
      },
      child: Padding(
        padding: const EdgeInsets.only(top: 4, bottom: 4),
        child: Row(
          children: <Widget>[
            Container(
              margin: const EdgeInsets.only(right: 9),
              child: FRoundImage.network(
                'https://gw.alicdn.com/imgextra/i2/O1CN01qQdeRO1FUvQYBcTjm_!!6000000000491-2-tps-39-41.png',
                height: 13,
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(right: 4.0),
                child: Text(
                  rankInfo.desc!,
                  style:
                      const TextStyle(color: Color(0xff805540), fontSize: 12),
                ),
              ),
            ),
            rightArrowSmall,
          ],
        ),
      ),
    );
  }

  Widget _buildShop(BuildContext context,
      RateAndServerChangeNotifier changeNotifier, ShopCardDataModel shopData) {
    return GestureDetector(
      onTap: () {
        changeNotifier.shopClick(context, shopData.jumpInfo?.jumpH5Url);
      },
      child: Padding(
        padding: const EdgeInsets.only(top: 4, bottom: 4),
        child: Row(
          children: <Widget>[
            if (shopData.shopPic != '')
              Container(
                decoration: BoxDecoration(
                    border: Border.all(
                        color: const Color.fromARGB(76, 210, 212, 217)),
                    borderRadius: BorderRadius.circular(6.00)),
                margin: const EdgeInsets.only(right: 6),
                child: FRoundImage.network(
                  shopData.shopPic!,
                  fit: BoxFit.fitHeight,
                  height: 18.00,
                ),
              ),
            Text(
              shopData.sellerName!,
              style: const TextStyle(color: Color(0xff805540), fontSize: 12),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(right: 6.0),
                child: Text(
                  shopData.shopDesc!,
                  style:
                      const TextStyle(color: Color(0xff919499), fontSize: 12),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.only(right: 4.0),
              child: const Text(
                '进店',
                style: TextStyle(color: Color(0xff0F131A), fontSize: 12),
              ),
            ),
            rightArrowSmall,
          ],
        ),
      ),
    );
  }
}

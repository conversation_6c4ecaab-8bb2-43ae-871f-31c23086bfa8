import '../../utils/safe_access.dart';

class SpuRankInfoModel {
  String? title;
  String? desc;
  JumpInfo? jumpInfo;

  SpuRankInfoModel.fromJson(Map<String, dynamic> dataModel) {
    final Map<String, dynamic> spuRankInfo =
        SafeAccess.safeParseMap(dataModel['spuRankInfo']);
    final Map<String, dynamic> json =
        SafeAccess.safeParseMap(spuRankInfo['data']);
    title = SafeAccess.safeParseString(json['title']);
    desc = SafeAccess.safeParseString(json['desc']);
    jumpInfo = json['jumpInfo'] != null
        ? JumpInfo.fromJson(SafeAccess.safeParseMap(json['jumpInfo']))
        : null;
  }
}

class JumpInfo {
  String? jumpH5Url;

  JumpInfo.fromJson(Map<String, dynamic> json) {
    jumpH5Url = SafeAccess.safeParseString(json['jumpH5Url']);
  }
}

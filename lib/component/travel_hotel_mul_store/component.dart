import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description TODO 组件功能
class TravelHotelMulStoreComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<TravelHotelMulStoreChangeNotifier>.value(
      value: TravelHotelMulStoreChangeNotifier(context),
      child: const TravelHotelMulStoreWidget(),
    );
  }
}
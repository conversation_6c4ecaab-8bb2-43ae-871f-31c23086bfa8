import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class TravelHotelMulStoreWidget extends StatelessWidget {
  const TravelHotelMulStoreWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TravelHotelMulStoreChangeNotifier changeNotifier =
        Provider.of<TravelHotelMulStoreChangeNotifier>(context);
    return Container();
  }
}
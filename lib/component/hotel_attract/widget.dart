import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class HotelAttractWidget extends StatelessWidget {
  const HotelAttractWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final HotelAttractChangeNotifier changeNotifier =
        Provider.of<HotelAttractChangeNotifier>(context);
    return Container();
  }
}
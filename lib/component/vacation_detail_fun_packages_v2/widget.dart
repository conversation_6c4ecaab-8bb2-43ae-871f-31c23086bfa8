import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class VacationDetailFunPackagesV2Widget extends StatelessWidget {
  const VacationDetailFunPackagesV2Widget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VacationDetailFunPackagesV2ChangeNotifier changeNotifier =
        Provider.of<VacationDetailFunPackagesV2ChangeNotifier>(context);
    return Container();
  }
}
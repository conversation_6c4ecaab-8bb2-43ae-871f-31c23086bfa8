import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';

class HotelNoticeChangeNotifier extends ComponentChangeNotifier {
  Map<dynamic, dynamic>? moduleData;
  HotelNoticeChangeNotifier(ComponentContext context) : super(context);

  @override
  void fromJson() {
    //mock数据
    // moduleData = mockData['appoint']['data'];
    // return;
    if (dataModel['hotelNotice'] == null || dataModel['hotelNotice']['data'] == null) {
      return;
    }
    moduleData = dataModel['hotelNotice']['data'];
  }
}
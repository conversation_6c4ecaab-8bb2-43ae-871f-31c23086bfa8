import '../../custom_widget/common_text_list_line_horizontal.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';
import '../../utils/common_config.dart';


class HotelNoticeWidget extends StatelessWidget {
  const HotelNoticeWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final HotelNoticeChangeNotifier changeNotifier =
        Provider.of<HotelNoticeChangeNotifier>(context);
    changeNotifier.fromJson();
    final Map<dynamic, dynamic>? moduleData = changeNotifier.moduleData;
    if (moduleData == null) {
      return SizedBox.shrink();
    }
    return Container(
      margin: const EdgeInsets.only(bottom: itemDivider),
      padding: const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 9),
      decoration: BoxDecoration(
          color: Color(0xffffffff),
          borderRadius: BorderRadius.circular(cardBorderRadius)),
      child: CommonTextListLineHorizontal(
        moduleData['title'],
        moduleData['descList'],
        textKey: 'mainInfo',
        descKey: 'subInfo',
      ),
    );
  }
}
import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import 'mock.dart';
class ItemVideoChangeNotifier extends ComponentChangeNotifier {
  ItemVideoChangeNotifier(ComponentContext context) : super(context);
  Map<dynamic, dynamic>? moduleData;
  @override
  void fromJson() {
    //mock数据
    // moduleData = mockData['video']['data'];
    // return;
    if (dataModel == null || dataModel['video'] == null || dataModel['video']['data'] == null) {
      return;
    }
    moduleData = dataModel['video']['data'];
  }
}
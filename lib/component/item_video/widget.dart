import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../custom_widget/block_title.dart';
import '../head_media/vacation_player/vacation_fplayer.dart';
import '../ticket_new_scenic/common_video_player.dart';
import '../ticket_new_scenic/common_video_player_model.dart';
import 'notifier.dart';
import 'package:flutter_common/api/empty.dart';
import '../../utils/common_config.dart';
class ItemVideoWidget extends StatelessWidget {
  const ItemVideoWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ItemVideoChangeNotifier changeNotifier =
        Provider.of<ItemVideoChangeNotifier>(context);
    changeNotifier.fromJson();
    final Map<dynamic, dynamic>? moduleData = changeNotifier.moduleData;
    if (moduleData == null || moduleData['videoUrl'] == null) {
      return empty;
    }
    return Container(
      margin: const EdgeInsets.only(bottom: itemDivider),
      padding: const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 9),
      decoration: BoxDecoration(
          color: Color(0xffffffff),
          borderRadius: BorderRadius.circular(cardBorderRadius)),
          child: 
          Column(            children: <Widget>[
            BlockTitle( moduleData['title'] ?? ''),
            Container(
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(cardBorderRadius),

              ),
              child: _buildVideo(context,moduleData['videoUrl'],moduleData['backgroundPicUrl']),)
            ],
          )
    );
  }
  Widget _buildVideo(BuildContext context,String videoUrl,String? backgroundPicUrl) {
    //获取当前context宽度,高度宽度比例为666/374
      final double width = MediaQuery.of(context).size.width;
      final double height = width * 374 / 666;
      final FliggyVacationPlayerController controller =
          FliggyVacationPlayerController();
      final CommonVideoPlayerModel videoModel = CommonVideoPlayerModel(
          controller: controller,
          url: videoUrl,
          coverUrl: backgroundPicUrl,
          hideControl: true,
          fitMode: BoxFit.contain,
          loadShowCoverImg: true,
          muted: false,
          width:width,
          height: height);
      return CommonVideoPlayer(
            videoModel: videoModel
          );
  }
}
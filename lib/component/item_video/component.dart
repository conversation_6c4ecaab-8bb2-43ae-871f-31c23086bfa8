import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 商品视频
class ItemVideoComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<ItemVideoChangeNotifier>.value(
      value: ItemVideoChangeNotifier(context),
      child: const ItemVideoWidget(),
    );
  }
}
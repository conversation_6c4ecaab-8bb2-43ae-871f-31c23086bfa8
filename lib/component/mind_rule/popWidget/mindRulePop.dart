import 'package:flutter/material.dart';

import '../model.dart';

/// <AUTHOR>
/// @date Created on 2024/12/25
/// @email <EMAIL>
/// @company Alibaba Group
/// @description
Widget MindRulePopWidget(MindRulePopupModel model) {
  return Padding(
    padding: EdgeInsets.only(left: 12,top: 12),
    child: <PERSON>umn(
      children: <Widget>[
        Row(
          children: <Widget>[
            Container(
              constraints: BoxConstraints(maxWidth: 360),
              child: Text(
                model.explain ?? '',
                style: TextStyle(
                  color: Color(0xFF0F131A),
                  fontSize: 14,
                  height: 2, // 对应 lineSpacing: 6ap
                ),
                maxLines: 99,
              ),
            )
          ],
        )
      ],
    ),
  );
}

import 'dart:io';

import 'package:flutter/material.dart';

import '../../../utils/common_util.dart';
import '../../../utils/safe_access.dart';
import '../model.dart';

/// <AUTHOR>
/// @date Created on 2024/12/4
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

Widget MindTagsPopWidget(MindTagsVO model) {
  return Column(
    children: <Widget>[
      if (model.popDesc != null)
        Padding(
          padding: EdgeInsets.fromLTRB(12, 12, 12, 0),
          child: Text(
            model.popDesc!,
            style: TextStyle(
              color: Color(0xFF0F131A),
              fontSize: 13,
              // height: 1.46, // 对应 lineSpacing: 6ap
            ),
          ),
        ),
      if (model.popupDest != null)
        Padding(
            padding: EdgeInsets.fromLTRB(12, 13, 12, 13),
            child: Column(
              children: model.popupDest!.map<Widget>((CommonRule content) {
                return Padding(
                  padding: EdgeInsets.only(bottom: 24),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      if (content.icon != null)
                        Container(
                          margin: EdgeInsets.only(top: Platform.isIOS ?2:0),
                          child: Image.network(
                            content.icon!,
                            width: 15,
                            height: 15,
                          ),
                        ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            if (content.title != null)
                              Text(
                                content.title!,
                                style: TextStyle(
                                  color: stringToColor(
                                    content.titleColor ?? '#FF0F131A',
                                  ),
                                  fontWeight: FontWeight.w500,
                                  fontSize: 14,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            SizedBox(height: 9),
                            if (content.desc != null)
                              Text(
                                content.desc!,
                                style: TextStyle(
                                  color: Color(0xFF0F131A),
                                  fontSize: 13,
                                  height: 1.46, // 对应 lineSpacing: 6ap
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ))
      // 描述
    ],
  );
}

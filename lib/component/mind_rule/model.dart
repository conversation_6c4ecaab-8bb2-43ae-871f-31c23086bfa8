import '../../custom_widget/common_tag/common_tag_model.dart';
import '../../utils/safe_access.dart';
import 'package:fjson_exports/fjson_exports.dart';

/// 这个现在很难做全，现在只有 commonRuleList 的数据和样式
class MindRuleModel {
  MindRuleModel();

  // priceCompare 是从 priceCompare.data 中获取
  // 其他的数据都从 mindRule.data 中获取
  /// 最上面彩色部分
  MindTagsVO? mindTagsVO;

  /// 白色的描述
  List<CommonRule>? commonRuleList;

  /// 一般灰色的描述
  List<CommonRule>? industryRuleList;

  PriceCompare? priceCompare;

  Highlight? highlight;

  MindRulePopup? mindRulePopup;

  factory MindRuleModel.fromJson(Map<String, dynamic> globalData) {
    final MindRuleModel model = MindRuleModel();
    if (globalData.containsKey('mindRule')) {
      final Map<String, dynamic> mindRule =
          safeNonNullMap(globalData['mindRule'], (dynamic e) => e);

      final Map<String, dynamic> mindRuleJson =
          safeNonNullMap(mindRule['data'], (dynamic e) => e);
      final Map<String, dynamic> popup =
          safeNonNullMap(mindRuleJson['popup'], (dynamic e) => e);
      if (mindRuleJson.containsKey('commonRuleList')) {
        final List<dynamic> commonRuleList = mindRuleJson['commonRuleList'];
        if (commonRuleList.isNotEmpty) {
          model.commonRuleList = [];
          for (dynamic json in commonRuleList) {
            final CommonRule commonRule =
                CommonRule.fromJson(safeNonNullMap(json, (dynamic e) => e));
            if (commonRule.event != null) {
              commonRule.popup =
                  MindRulePopupModel.fromJson(popup[commonRule.event!]);
            }

            model.commonRuleList!.add(commonRule);
          }
        }
      }

      if (mindRuleJson.containsKey('industryRuleList')) {
        model.industryRuleList = [];
        final List<dynamic> industryRuleList = mindRuleJson['industryRuleList'];
        for (dynamic json in industryRuleList) {
          final CommonRule commonRule =
              CommonRule.fromJson(safeNonNullMap(json, (dynamic e) => e));
          if (commonRule.event != null) {
            commonRule.popup =
                MindRulePopupModel.fromJson(popup[commonRule.event!]);
          }

          model.industryRuleList!.add(commonRule);
        }
      }

      if (mindRuleJson.containsKey('mindTagsVO')) {
        model.mindTagsVO = MindTagsVO.fromJson(
            safeNonNullMap(mindRuleJson['mindTagsVO'], (dynamic e) => e));
      }

      if (mindRuleJson.containsKey('highlight')) {
        model.highlight = Highlight.fromJson(
            safeNonNullMap(mindRuleJson['highlight'], (dynamic e) => e));
      }
    }

    if (globalData.containsKey('priceCompare')) {
      final Map<String, dynamic> priceCompare =
          safeNonNullMap(globalData['priceCompare'], (dynamic e) => e);
      final Map<String, dynamic> priceCompareJson =
          safeNonNullMap(priceCompare['data'], (dynamic e) => e);
      model.priceCompare = PriceCompare.fromJson(priceCompareJson);
    }

    return model;
  }
}

/// 保障
class CommonRule {
  CommonRule();

  String? title;
  String? titleColor;

  String? icon;

  String? desc;

  /// 从这个event里，去popup里找对应的弹窗数据
  String? event;

  MindRulePopupModel? popup;

  factory CommonRule.fromJson(Map<String, dynamic> json) {
    final CommonRule model = CommonRule();
    model.title = safeString(json['title']);
    model.titleColor = json['titleColor'];
    model.icon = safeString(json['icon']);
    model.desc = safeString(json['desc']);
    model.event = safeString(json['event']);
    return model;
  }
}

/// 标签弹窗通用模版
class MindRulePopupModel {
  MindRulePopupModel();

  /// 标题
  String? explainTitle;

  /// 弹窗链接
  String? url;

  /// 弹窗文案
  String? explain;

  factory MindRulePopupModel.fromJson(Map<String, dynamic> json) {
    final MindRulePopupModel model = MindRulePopupModel();
    model.explainTitle = safeString(json['explainTitle']);
    model.url = safeString(json['url']);
    model.explain = safeString(json['explain']);
    return model;
  }
}

class PriceCompare {
  PriceCompare();

  String? compareTitle;
  String? jumpUrl;
  String? title;
  String? saveMoneyText;

  factory PriceCompare.fromJson(Map<String, dynamic> json) {
    final PriceCompare model = PriceCompare();
    model.title = json['title'];
    model.compareTitle = json['compareTitle'];
    model.jumpUrl = json['jumpUrl'];
    model.saveMoneyText = json['saveMoneyText'];
    return model;
  }
}

/// 亮点
class Highlight {
  Highlight();

  String? title;
  String? explainFloatingLayerUrl;

  HighlightPopup? highlightPopup;

  List<CommonTagModel>? commonTags;

  factory Highlight.fromJson(Map<String, dynamic> json) {
    final Highlight model = Highlight();
    model.title = safeString(json['title']);
    model.explainFloatingLayerUrl = safeString(json['explainFloatingLayerUrl']);
    if (json.containsKey('descList')) {
      final List<dynamic> descList = json['descList'];
      model.commonTags = <CommonTagModel>[];
      for (final dynamic json in descList) {
        final HighlightTag highlight =
            HighlightTag.fromJson(safeNonNullMap(json, (dynamic e) => e));
        final CommonTagModel commonTagModel = CommonTagModel(
            text: highlight.title,
            textColor: highlight.textColor,prefixText: highlight.highlightPrefix);
        model.commonTags!.add(commonTagModel);
      }
    }

    if (json.containsKey('explainList')) {
      model.highlightPopup = HighlightPopup.fromJson(
          safeNonNullMap(json['explainList'], (dynamic e) => e));
    }
    return model;
  }
}

/// 亮点标签
class HighlightTag {
  HighlightTag();

  String? title;

  String? textColor;
  String? boardColor;

  String? bgColor;
  String? highlightPrefix;

  factory HighlightTag.fromJson(Map<String, dynamic> json) {
    final HighlightTag model = HighlightTag();
    model.title = json['title'];
    model.highlightPrefix = json['highlightPrefix'];
    model.textColor = json['textColor'];
    model.boardColor = json['boardColor'];
    model.bgColor = json['backgroundColor'];
    return model;
  }
}

/// 亮点弹窗结构
class HighlightPopup {
  HighlightPopup();

  factory HighlightPopup.fromJson(Map<String, dynamic> json) {
    final HighlightPopup model = HighlightPopup();
    return model;
  }
}

class HighlightPopupItem {
  HighlightPopupItem();

  String? icon;

  String? title;

  String? text;

  String? titleColor;

  /// 新增的二级弹窗，但是找不到样式案例，native也没做，先标记一下
  HighlightPopupDetail? detail;

  factory HighlightPopupItem.fromJson(Map<String, dynamic> json) {
    final HighlightPopupItem model = HighlightPopupItem();
    model.icon = safeString(json['icon']);
    model.title = safeString(json['title']);
    model.text = safeString(json['text']);
    model.titleColor = safeString(json['titleColor']);
    if (json.containsKey('detail')) {
      model.detail = HighlightPopupDetail.fromJson(
          safeNonNullMap(json['detail'], (dynamic e) => e));
    }
    return model;
  }
}

class HighlightPopupDetail {
  HighlightPopupDetail();

  factory HighlightPopupDetail.fromJson(Map<String, dynamic> json) {
    final HighlightPopupDetail model = HighlightPopupDetail();
    return model;
  }
}

/// 囤好货心智
class MindTagsVO {
  MindTagsVO();

  /// 左侧图标
  String? icon;

  /// 右侧文案
  String? tag;

  /// 右侧文案颜色
  String? tagColor;

  /// 说实话，这个只是数据结构一致，但是实在不想再定义一个数据模型了，鬼知道这个组件这么复杂啊
  List<CommonRule>? popupDest;
  String? popTitleIconUrl;
  String? popDesc;

  // MindRulePopupVO? popup;

  factory MindTagsVO.fromJson(Map<String, dynamic> json) {
    final MindTagsVO model = MindTagsVO();
    model.icon = safeString(json['icon']);
    final Map tag = safeNonNullMap(json['tags'], (dynamic e) => e);
    model.tag = safeString(tag['value']);
    model.tagColor = safeString(tag['textColor']);
    if (json.containsKey('detailDesc')) {
      model.popupDest = [];
      final Map detailDesc =
          safeNonNullMap(json['detailDesc'], (dynamic e) => e);
      final List<dynamic> popupDest = detailDesc['contents'];
      for (dynamic json in popupDest) {
        model.popupDest!
            .add(CommonRule.fromJson(safeNonNullMap(json, (dynamic e) => e)));
      }

      model.popTitleIconUrl = safeString(detailDesc['icon']);
      model.popDesc = safeString(detailDesc['desc']);
    }
    return model;
  }
}

/// 囤好货心智弹窗
class MindRulePopupVO {
  MindRulePopupVO();

  List<CommonRule>? contents;

  String? icon;

  String? desc;

  factory MindRulePopupVO.fromJson(Map<String, dynamic> json) {
    final MindRulePopupVO model = MindRulePopupVO();
    if (json.containsKey('contents')) {
      model.contents = [];
      final List<dynamic> contents = json['contents'];
      for (dynamic json in contents) {
        model.contents!
            .add(CommonRule.fromJson(safeNonNullMap(json, (dynamic e) => e)));
      }
    }
    model.icon = safeString(json['icon']);
    model.desc = safeString(json['desc']);

    return model;
  }
}

/// 一般灰色区域内的列表
class IndustryRuleList {}

/// 弹层结构
class MindRulePopup {}

import 'package:flutter/cupertino.dart';

import '../../custom_widget/dialog_webview.dart';
import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import 'package:fliggy_router/fliggy_router.dart';

class MindRuleChangeNotifier extends ComponentChangeNotifier {
  MindRuleChangeNotifier(ComponentContext context) : super(context);

  void shoDialogWebPop(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      final DialogWebView dialogWebView =
      DialogWebView(context: context, url: url,itemDetailEngine: itemDetailEngine,popConfig: H5PopConfig(popHeight: MediaQuery.of(context).size.height * 1 / 2));
      dialogWebView.showPop();
    }
  }
  void shoDialogHeightWebPop(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      final DialogWebView dialogWebView =
      DialogWebView(context: context, url: url,itemDetailEngine: itemDetailEngine,popConfig: H5PopConfig(popHeight: MediaQuery.of(context).size.height * 3 / 4));
      dialogWebView.showPop();
    }
  }
  ///日历页面
  void gotoDateUrl(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      FliggyNavigatorApi.getInstance().push(context, url);
    }
  }
}
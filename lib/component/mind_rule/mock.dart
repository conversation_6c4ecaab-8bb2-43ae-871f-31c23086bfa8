const String mockData = '''
priceCompare: {
      data: {
        compareTitle: '单独订房',
        compareTitleTabs: [
          {
            packageName: '静逸双床房2晚可拆分+单人早餐券2张可拆分',
            skuId: 4913283139861,
          },
        ],
        jumpUrl: 'https://market.m.taobao.com/app/trip/rx-travel-detail/pages/price-compare',
        priceCompareText: '去比价',
        saveMoney: 20779,
        saveMoneyText: '省208',
        title: '酒店套餐',
      },
    },
    mindRule: {
      data: {
        commonRuleList: [
          {
            desc: '过期自动退',
            event: 'assurePopup',
            title: '保障',
          },
          {
            desc: '测试描述描述',
            event: 'activity',
            icon: 'https://gw.alicdn.com/imgextra/i1/O1CN01NCFLuB1v3dVuRslPk_!!6000000006117-2-tps-162-51.png',
            title: '活动',
          },
        ],
        highlight: {
          // 标签
          descList: [
            {
              backgroundColor: '#FFF0ED',
              boardColor: '#FFF0ED',
              icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01cNggAl1sKdZqQGI5m_!!6000000005748-2-tps-30-30.png',
              textColor: '',
              title: '免费改期',
            },
            {
              backgroundColor: '#FFF0ED',
              icon: 'https://gw.alicdn.com/imgextra/i4/O1CN01VfziZ51VMi04BTC24_!!6000000002639-2-tps-45-45.png',
              title: '国庆可住',
            },
          ],
          // 标签弹窗
          explainList: [
            {
              // 二级弹窗 新增
              detail: {
                background: 'https://gw.alicdn.com/imgextra/i2/O1CN01y99ole1RUGH7BQhbE_!!6000000002114-0-tps-1125-450.jpg',
                // 二级弹窗里面的详情
                items: [
                  {
                    // 图标
                    icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01cNggAl1sKdZqQGI5m_!!6000000005748-2-tps-30-30.png',
                    // 文案 需要支持 \n 换行
                    text: '套餐预约后,支持在符合规则时间范围内免费改期1次',
                    // 标题
                    title: '权益介绍',
                  },
                  {
                    // 图标
                    icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01cNggAl1sKdZqQGI5m_!!6000000005748-2-tps-30-30.png',
                    // 该详情下方图片,可能多个
                    images: ['https://gw.alicdn.com/imgextra/i3/O1CN01SccPuI1SxrOK1OjKB_!!6000000002314-2-tps-667-134.png'],
                    // 文案 需要支持 \n 换行
                    text: '需满足以下条件的订单支持免费改期:\n1.预约订单下单时间:需在2024-09-14-2024-09-30\n2.改期发起时间需要在入住日期后5天',
                    // 标题
                    title: '改期规则',
                  },
                ],
                // 顶部子标题
                subTitle: '购买后立即预约心意日期,支持免费改期',
                // 顶部标题
                title: '免费改期',
                // 顶部图标,有图标用图标,没图标用标题
                titleIcon: 'https://gw.alicdn.com/imgextra/i1/O1CN01i9MfN41SyJsuItNVa_!!6000000002315-2-tps-296-80.png',
              },
              // 图标
              icon: 'https://gw.alicdn.com/imgextra/i2/O1CN01cNggAl1sKdZqQGI5m_!!6000000005748-2-tps-30-30.png',
              // 描述
              text: '套餐预约后,支持在符合规则的时间范围内免费改期1次',
              // 标题
              title: '免费改期',
              titleColor: '#FF8C1A',
            },
            {
              icon: 'https://gw.alicdn.com/imgextra/i4/O1CN01VfziZ51VMi04BTC24_!!6000000002639-2-tps-45-45.png',
              text: '该商品可预约国庆假期日期入住，少部分假期日期存在已约满，不可预约的情况',
              title: '国庆可住',
              titleColor: '#FF8C1A',
            },
          ],
          explainIcon: 'https://gw.alicdn.com/imgextra/i4/O1CN01Vx0JDB1XBNdouBDfj_!!6000000002885-2-tps-290-80.png',
          explainBackground: 'https://gw.alicdn.com/imgextra/i1/O1CN01wOly3526GQYeAksgo_!!6000000007634-0-tps-1125-450.jpg',
          // 弹窗顶部标题
          explainTitle: '亮点',
          // 弹窗顶部副标题 新增
          explainSubTitle: '更多精彩活动在这里',
          explainFloatingLayerUrl: 'https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/hotel-rules?id=111',
          title: '亮点',
        },
        industryRuleList: [
          {
            desc: '2022-09-24至2022-10-31',
            event: 'datePopup',
            title: '日期',
          },
        ],
        mindTagsVO: {
          detailDesc: {
            contents: [
              {
                desc: '先下单锁定优惠，确定出行日期后，再预约具体出行日期。',
                icon: 'https://gw.alicdn.com/tps/TB1DCgkNVXXXXXlapXXXXXXXXXX-24-24.png',
                title: '先囤后约',
              },
              {
                desc: '如您未进行预约，支持随时发起 100%退款，商品过期后自动 100%退款，整份宝贝部分预约不支持退款。',
                icon: 'https://gw.alicdn.com/tps/TB1DCgkNVXXXXXlapXXXXXXXXXX-24-24.png',
                title: '不约可退',
              },
              {
                desc: '在线预约出行日期，订单即时确认，高效便捷。',
                icon: 'https://gw.alicdn.com/tps/TB1DCgkNVXXXXXlapXXXXXXXXXX-24-24.png',
                title: '在线预约',
              },
            ],
            desc: '为保障您的多样出行需求，飞猪在线预约套餐商品，将提供以下特享服务内容，无负担，放心囤好货。',
            icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01QBsTF81WTPP43mEWM_!!6000000002789-2-tps-264-78.png',
          },
          icon: 'https://gw.alicdn.com/imgextra/i3/O1CN01QBsTF81WTPP43mEWM_!!6000000002789-2-tps-264-78.png',
          tags: {
            textColor: '#FCA500',
            value: '先囤后约 · 不约可退 · 在线预约',
          },
        },
        popup: {
          activity: {
            explainTitle: '活动',
            url: 'www.baidu.com',
          },
          datePopup: {
            explain: '可住日期：2022-05-28至2022-09-29\n预约开放日期：2022-05-27 至 2022-09-28\n其他规则：至少提前1天预约，不可拆分使用',
            explainTitle: '日期',
          },
          assurePopup: {
            explain: '过期自动退：如果您未进行预约，商品过期后自动100%退款，整份宝贝部分预约不支持退款',
            explainTitle: '保障',
          },
        },
      },
      tag: 'mindRule',
    },
''';

final Map<String, dynamic> mindRule = {
  'data': {
    'commonRuleList': [
      {
        'desc': '过期自动退',
        'event': 'assurePopup',
        'title': '保障',
      },
      {
        'desc': '测试描述描述',
        'event': 'activity',
        'icon':
        'https://gw.alicdn.com/imgextra/i1/O1CN01NCFLuB1v3dVuRslPk_!!6000000006117-2-tps-162-51.png',
        'title': '活动',
      },
    ],
    'highlight': {
      // 标签
      'descList': [
        {
          'backgroundColor': '#FFF0ED',
          'boardColor': '#FFF0ED',
          'icon':
          'https://gw.alicdn.com/imgextra/i2/O1CN01cNggAl1sKdZqQGI5m_!!6000000005748-2-tps-30-30.png',
          'textColor': '',
          'title': '免费改期',
        },
        {
          'backgroundColor': '#FFF0ED',
          'icon':
          'https://gw.alicdn.com/imgextra/i4/O1CN01VfziZ51VMi04BTC24_!!6000000002639-2-tps-45-45.png',
          'title': '国庆可住',
        },
      ],
      // 标签弹窗
      'explainList': [
        {
          // 二级弹窗 新增
          'detail': {
            'background':
            'https://gw.alicdn.com/imgextra/i2/O1CN01y99ole1RUGH7BQhbE_!!6000000002114-0-tps-1125-450.jpg',
            // 二级弹窗里面的详情
            'items': [
              {
                // 图标
                'icon':
                'https://gw.alicdn.com/imgextra/i2/O1CN01cNggAl1sKdZqQGI5m_!!6000000005748-2-tps-30-30.png',
                // 文案 需要支持 \n 换行
                'text': '套餐预约后,支持在符合规则时间范围内免费改期1次',
                // 标题
                'title': '权益介绍',
              },
              {
                // 图标
                'icon':
                'https://gw.alicdn.com/imgextra/i2/O1CN01cNggAl1sKdZqQGI5m_!!6000000005748-2-tps-30-30.png',
                // 该详情下方图片,可能多个
                'images': [
                  'https://gw.alicdn.com/imgextra/i3/O1CN01SccPuI1SxrOK1OjKB_!!6000000002314-2-tps-667-134.png'
                ],
                // 文案 需要支持 \n 换行
                'text':
                '需满足以下条件的订单支持免费改期:\n1.预约订单下单时间:需在2024-09-14-2024-09-30\n2.改期发起时间需要在入住日期后5天',
                // 标题
                'title': '改期规则',
              },
            ],
            // 顶部子标题
            'subTitle': '购买后立即预约心意日期,支持免费改期',
            // 顶部标题
            'title': '免费改期',
            // 顶部图标,有图标用图标,没图标用标题
            'titleIcon':
            'https://gw.alicdn.com/imgextra/i1/O1CN01i9MfN41SyJsuItNVa_!!6000000002315-2-tps-296-80.png',
          },
          // 图标
          'icon':
          'https://gw.alicdn.com/imgextra/i2/O1CN01cNggAl1sKdZqQGI5m_!!6000000005748-2-tps-30-30.png',
          // 描述
          'text': '套餐预约后,支持在符合规则的时间范围内免费改期1次',
          // 标题
          'title': '免费改期',
          'titleColor': '#FF8C1A',
        },
        {
          'icon':
          'https://gw.alicdn.com/imgextra/i4/O1CN01VfziZ51VMi04BTC24_!!6000000002639-2-tps-45-45.png',
          'text': '该商品可预约国庆假期日期入住，少部分假期日期存在已约满，不可预约的情况',
          'title': '国庆可住',
          'titleColor': '#FF8C1A',
        },
      ],

      'explainIcon':
      'https://gw.alicdn.com/imgextra/i4/O1CN01Vx0JDB1XBNdouBDfj_!!6000000002885-2-tps-290-80.png',
      'explainBackground':
      'https://gw.alicdn.com/imgextra/i1/O1CN01wOly3526GQYeAksgo_!!6000000007634-0-tps-1125-450.jpg',
      // 弹窗顶部标题
      'explainTitle': '亮点',
      // 弹窗顶部副标题 新增
      'explainSubTitle': '更多精彩活动在这里',
      'explainFloatingLayerUrl':
      'https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/hotel-rules?id=111',
      'title': '亮点',
    },
    'industryRuleList': [
      {
        'desc': '2022-09-24至2022-10-31',
        'event': 'datePopup',
        'title': '日期',
      },
    ],
    'mindTagsVO': {
      'detailDesc': {
        'contents': [
          {
            'desc': '先下单锁定优惠，确定出行日期后，再预约具体出行日期。',
            'icon':
            'https://gw.alicdn.com/tps/TB1DCgkNVXXXXXlapXXXXXXXXXX-24-24.png',
            'title': '先囤后约',
          },
          {
            'desc': '如您未进行预约，支持随时发起 100%退款，商品过期后自动 100%退款，整份宝贝部分预约不支持退款。',
            'icon':
            'https://gw.alicdn.com/tps/TB1DCgkNVXXXXXlapXXXXXXXXXX-24-24.png',
            'title': '不约可退',
          },
          {
            'desc': '在线预约出行日期，订单即时确认，高效便捷。',
            'icon':
            'https://gw.alicdn.com/tps/TB1DCgkNVXXXXXlapXXXXXXXXXX-24-24.png',
            'title': '在线预约',
          },
        ],
        'desc': '为保障您的多样出行需求，飞猪在线预约套餐商品，将提供以下特享服务内容，无负担，放心囤好货。',
        'icon':
        'https://gw.alicdn.com/imgextra/i3/O1CN01QBsTF81WTPP43mEWM_!!6000000002789-2-tps-264-78.png',
      },
      'icon':
      'https://gw.alicdn.com/imgextra/i3/O1CN01QBsTF81WTPP43mEWM_!!6000000002789-2-tps-264-78.png',
      'tags': {
        'textColor': '#FCA500',
        'value': '先囤后约 · 不约可退 · 在线预约',
      },
    },
    'popup': {
      'activity': {
        'explainTitle': '活动',
        'url': 'www.baidu.com',
      },
      'datePopup': {
        'explain':
        '可住日期：2022-05-28至2022-09-29\n预约开放日期：2022-05-27 至 2022-09-28\n其他规则：至少提前1天预约，不可拆分使用',
        'explainTitle': '日期',
      },
      'assurePopup': {
        'explain': '过期自动退：如果您未进行预约，商品过期后自动100%退款，整份宝贝部分预约不支持退款',
        'explainTitle': '保障',
      },
    },
  },
  'tag': 'mindRule',
};
import 'dart:io';

import '../../custom_widget/common_tag/common_tag_model.dart';
import '../../custom_widget/common_tag/common_tag_widget.dart';
import '../../custom_widget/dialog_webview.dart';
import 'popWidget/mindTagsPop.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../custom_widget/detail_arrow.dart';
import '../../custom_widget/dialog_flutter.dart';
import '../../utils/common_config.dart';
import '../../utils/safe_access.dart';
import 'model.dart';
import 'notifier.dart';
import 'package:fround_image/fround_image.dart';
import 'package:flutter/services.dart';

import 'popWidget/mindRulePop.dart';

class MindRuleWidget extends StatelessWidget {
  const MindRuleWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final MindRuleChangeNotifier changeNotifier =
        Provider.of<MindRuleChangeNotifier>(context);

    if (changeNotifier.itemDetailModel.mindRuleModel == null) {
      return SizedBox.shrink();
    }

    final MindRuleModel model = changeNotifier.itemDetailModel.mindRuleModel!;
    return Container(
      width: 375,
      clipBehavior: Clip.hardEdge,
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(6),
      ),
      padding: EdgeInsets.only(
          bottom: (model.highlight != null ||
                  model.priceCompare != null ||
                  model.industryRuleList != null ||
                  model.commonRuleList != null)
              ? 6
              : 0),
      margin: const EdgeInsets.only(bottom: itemDivider),
      child: Stack(
        children: <Widget>[
          if (model.mindTagsVO != null) _buildMindTagsVO(context, model),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFFFFFFF),
              borderRadius: BorderRadius.circular(6),
            ),
            width: 357,
            margin: EdgeInsets.only(top: 39),
            padding: EdgeInsets.only(top: 6),
            child: Column(
              children: <Widget>[
                if (model.highlight != null)
                  _buildHighlight(changeNotifier, context, model),
                if (model.priceCompare != null)
                  _buildPriceCompare(
                      changeNotifier, context, model.priceCompare!),
                if (model.industryRuleList != null)
                  _buildIndustryRuleList(changeNotifier, context, model),
                if (model.commonRuleList != null)
                  _buildCommonRuleList(changeNotifier, context, model),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMindTagsVO(BuildContext context, MindRuleModel model) {
    return GestureDetector(
      onTap: () {
        final Widget contentWidget = MindTagsPopWidget(model.mindTagsVO!);
        final DialogFlutterView dialogFlutterView = DialogFlutterView(
          context: context,
          contentWidget: contentWidget,
          popConfig: FlutterPopConfig(
              popTitleWidget: FRoundImage.network(
                model.mindTagsVO!.popTitleIconUrl ?? '',
                height: 20,
                fit: BoxFit.fitHeight,
              ),
              popBtnTitle: '确定',
              popHeight: MediaQuery.of(context).size.height * 3 / 4),
        );
        dialogFlutterView.showPop();
      },
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFF5EB),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(6), topRight: Radius.circular(6)),
        ),
        margin: EdgeInsets.only(left: 1, right: 1, top: 1),
        padding: EdgeInsets.only(
            left: 11,
            right: 11,
            bottom: Platform.isIOS ? 17.5 : 19,
            top: Platform.isIOS ? 9.5 : 12),
        child: Row(
          children: <Widget>[
            if (model.mindTagsVO!.icon != null)
              Image.network(
                model.mindTagsVO!.icon!,
                height: 13,
                fit: BoxFit.fitHeight,
              ),
            Container(
              height: 8,
              width: 0.5,
              color: Color(0x80FF8C1A),
              margin: EdgeInsets.symmetric(horizontal: 6),
            ),
            if (model.mindTagsVO!.tag != null)
              Text(
                model.mindTagsVO!.tag!,
                style: TextStyle(
                  color: SafeAccess.hexColor(
                      model.mindTagsVO!.tagColor ?? '0xFFFF7300'),
                  fontWeight: FontWeight.w500,
                  fontSize: 13,
                ),
              ),
            Spacer(),
            rightArrowSmallOrange
          ],
        ),
      ),
    );
  }

  Widget _buildHighlight(MindRuleChangeNotifier changeNotifier,
      BuildContext context, MindRuleModel model) {
    changeNotifier.ctrlExposure(context, 'mind-rule.highlight', null);
    return GestureDetector(
      onTap: () {
        changeNotifier.ctrlClicked(
            context, 'mind-rule.highlight', 'mind-rule.highlight', null);
        changeNotifier.shoDialogWebPop(
            context, model.highlight?.explainFloatingLayerUrl);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: <Widget>[
            Text(
              model.highlight!.title ?? '',
              style: TextStyle(color: Color(0xFF919499), fontSize: 13),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Container(
                margin: EdgeInsets.only(right: 5),
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: model.highlight!.commonTags
                            ?.map((CommonTagModel tag) => Container(
                                  margin: EdgeInsets.only(right: 4),
                                  child: CommonTagWidget(tag),
                                ))
                            .toList() ??
                        <Widget>[],
                  ),
                ),
              ),
            ),
            rightArrowSmall
          ],
        ),
      ),
    );
  }

  Widget _buildPriceCompare(MindRuleChangeNotifier changeNotifier,
      BuildContext context, PriceCompare priceCompare) {
    // Implement price compare widget based on your PriceCompare model
    changeNotifier.ctrlExposure(context, 'mind-rule.compare', null);
    return GestureDetector(
      onTap: () {
        changeNotifier.ctrlClicked(
            context, 'mind-rule.compare', 'mind-rule.compare', null);
        changeNotifier.shoDialogHeightWebPop(context, priceCompare.jumpUrl);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Row(
          children: <Widget>[
            Text('比价',
                style: TextStyle(color: Color(0xFF919499), fontSize: 13)),
            SizedBox(width: 12),
            Text(
              priceCompare.title ?? '',
              style: TextStyle(
                fontSize: 13,
                color: Color(0xFF5c5f66),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            Container(
                margin: EdgeInsets.symmetric(horizontal: 5),
                child: Image.network(
                  'https://gw.alicdn.com/imgextra/i4/O1CN01fnmeEi27UuhVRSB2h_!!6000000007801-2-tps-39-30.png',
                  width: 13,
                  height: 17,
                )),
            Text(
              priceCompare.compareTitle ?? '',
              style: TextStyle(
                fontSize: 13,
                color: Color(0xFF5c5f66),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            Expanded(
                child: Text(
              priceCompare.saveMoneyText ?? '',
              style: TextStyle(
                fontSize: 13,
                color: Color(0xFFFF3E00),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            )),
            if (priceCompare.jumpUrl != null) rightArrowSmall
          ],
        ),
      ),
    );
  }

  Widget _buildIndustryRuleList(MindRuleChangeNotifier changeNotifier,
      BuildContext context, MindRuleModel model) {
    changeNotifier.ctrlExposure(context, 'mind-rule.industryrule', null);
    return Column(
      children: model.industryRuleList
              ?.map((CommonRule rule) => GestureDetector(
                    onTap: () {
                      changeNotifier.ctrlClicked(
                          context,
                          'mind-rule.industryrule',
                          'mind-rule.industryrule',
                          null);
                      if (rule.popup!.url != null) {
                        changeNotifier.gotoDateUrl(context, rule.popup!.url);
                      } else {
                        final Widget contentWidget =
                            MindRulePopWidget(rule.popup!);
                        final DialogFlutterView dialogFlutterView =
                            DialogFlutterView(
                          context: context,
                          contentWidget: contentWidget,
                          popConfig: FlutterPopConfig(
                              popTitle: rule.title,
                              popBtnTitle: '确定',
                              popHeight:
                                  MediaQuery.of(context).size.height * 1 / 2),
                        );
                        dialogFlutterView.showPop();
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.only(
                          left: 12, right: 12, top: 4, bottom: 6),
                      color: Color(0x00FFFFFF),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: SizedBox(
                              width: 327,
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Text(rule.title ?? '',
                                      style: TextStyle(
                                          color: Color(0xFF919499),
                                          fontSize: 13,
                                          height: 1.85)),
                                  SizedBox(width: 12),
                                  Expanded(
                                      child: Container(
                                    margin: EdgeInsets.only(right: 5),
                                    child: Text(
                                      rule.desc ?? '',
                                      style: TextStyle(
                                          fontSize: 13,
                                          color: Color(0xFF5c5f66),
                                          height: 1.85),
                                    ),
                                  )),
                                ],
                              ),
                            ),
                          ),
                          if (rule.event != null) rightArrowSmall,
                        ],
                      ),
                    ),
                  ))
              .toList() ??
          <Widget>[],
    );
  }

  Widget _buildCommonRuleList(MindRuleChangeNotifier changeNotifier,
      BuildContext context, MindRuleModel model) {
    changeNotifier.ctrlExposure(context, 'mind-rule.commonrule', null);
    return Column(
      children: model.commonRuleList
              ?.map((CommonRule rule) => GestureDetector(
                    onTap: () {
                      changeNotifier.ctrlClicked(context,
                          'mind-rule.commonrule', 'mind-rule.commonrule', null);
                      final Widget contentWidget =
                          MindRulePopWidget(rule.popup!);
                      final DialogFlutterView dialogFlutterView =
                          DialogFlutterView(
                        context: context,
                        contentWidget: contentWidget,
                        popConfig: FlutterPopConfig(
                            popTitle: rule.title,
                            popBtnTitle: '确定',
                            popHeight:
                                MediaQuery.of(context).size.height * 1 / 2),
                      );
                      dialogFlutterView.showPop();
                    },
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      color: Color(0x00FFFFFF),
                      child: Row(
                        children: <Widget>[
                          Text(rule.title ?? '',
                              style: TextStyle(
                                  color: Color(0xFF919499), fontSize: 13)),
                          SizedBox(width: 12),
                          if (rule.icon != null)
                            FRoundImage.network(rule.icon!,
                                height: 13, fit: BoxFit.fitHeight),
                          Expanded(
                              child: Text(
                            rule.desc ?? '',
                            style: TextStyle(
                              fontSize: 13,
                              color: Color(0xFF5c5f66),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          )),
                          if (rule.event != null) rightArrowSmall
                        ],
                      ),
                    ),
                  ))
              .toList() ??
          <Widget>[],
    );
  }
}

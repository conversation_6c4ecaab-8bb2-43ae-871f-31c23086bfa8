import '../../utils/safe_access.dart';

class ZeroSaveBuyRuleModel {
  String? backgroundColor;
  String? icon;

  String? popWindowUrl;
  String? text;
  String? textColor;
  String spmCD = 'zerosaveBuyrule.0';

  ZeroSaveBuyRuleModel.fromJson(Map<String, dynamic> rankListInfo) {
    final Map<String, dynamic> data =
        SafeAccess.safeParseMap(rankListInfo['data']);
    backgroundColor = data['backgroundColor'] ?? '#FEEDE5';
    icon = SafeAccess.safeParseString(data['icon']);
    popWindowUrl = SafeAccess.safeParseString(data['popWindowUrl']);
    text = data['text'];
    textColor = data['textColor'] ?? '#0f131a';
  }
}

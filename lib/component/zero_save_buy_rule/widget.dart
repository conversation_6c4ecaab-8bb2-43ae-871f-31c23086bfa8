import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';

import 'package:provider/provider.dart';
import '../../custom_widget/detail_arrow.dart';
import '../../custom_widget/null_widget.dart';
import '../../utils/common_config.dart';
import '../../utils/common_util.dart';
import 'model.dart';
import 'notifier.dart';
import 'package:fliggy_router/fliggy_router.dart';

/// <AUTHOR>
/// @date Created on 2024/10/22
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

class ZeroSaveBuyRuleWidget extends StatelessWidget {
  const ZeroSaveBuyRuleWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ZeroSaveBuyRuleChangeNotifier changeNotifier =
        Provider.of<ZeroSaveBuyRuleChangeNotifier>(context);
    final ZeroSaveBuyRuleModel? zeroSaveBuyRuleModel =
        changeNotifier.itemDetailModel.zeroSaveBuyRuleModel;

    // 没数据,不曝光
    if (zeroSaveBuyRuleModel == null || zeroSaveBuyRuleModel.text == null) {
      return nullWidget;
    }

    changeNotifier.ctrlExposure(context, zeroSaveBuyRuleModel.spmCD, null);

    return Container(
      width: 357,
      height: 30,
      color: const Color(0xFFFFFFFF),
      child: GestureDetector(
        onTap: () {
          // Open H5 URL
          if (zeroSaveBuyRuleModel.popWindowUrl != null) {
            changeNotifier.ctrlClicked(context, zeroSaveBuyRuleModel.spmCD,
                'zeroSaveBuyRuleModel', null);
            changeNotifier.openPopWindow(context, zeroSaveBuyRuleModel.popWindowUrl!);
          }
        },
        child: Container(
          height: 30,
          padding: EdgeInsets.only(right: 12),
          margin: EdgeInsets.only(top: 10, left: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            gradient: LinearGradient(
              colors: <Color>[stringToColor(zeroSaveBuyRuleModel.backgroundColor), Color(0xffffffff)],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
          child: Row(
            children: <Widget>[
              if (zeroSaveBuyRuleModel.icon != null)
                Image.network(
                  zeroSaveBuyRuleModel.icon!,
                  height: 21,
                ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: 6,right: 6),
                  child: Text(
                    zeroSaveBuyRuleModel.text!,
                    style: TextStyle(
                      fontSize: 12,
                      color:
                      stringToColor(zeroSaveBuyRuleModel.textColor),
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              rightArrowBig,
            ],
          ),
        ),
      ),
    );
  }
}

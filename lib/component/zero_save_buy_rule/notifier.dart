import 'package:flutter/material.dart';

import '../../custom_widget/dialog_webview.dart';
import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import '../../utils/track_utils.dart';
import 'model.dart';

class ZeroSaveBuyRuleChangeNotifier extends ComponentChangeNotifier {
  ZeroSaveBuyRuleChangeNotifier(ComponentContext context) : super(context);

  ZeroSaveBuyRuleModel? get zeroSaveBuyRuleModel =>
      itemDetailModel.zeroSaveBuyRuleModel;

  @override
  void ctrlExposure(
      BuildContext context, String spmCD, Map<String, dynamic>? trackParams) {
    TrackUtils.exposureTrack(
        context, zeroSaveBuyRuleModel?.spmCD ?? '', trackParams,
        model: itemDetailModel);
  }

  void openPopWindow(BuildContext context, String url) {
    if (url.isNotEmpty) {
      final DialogWebView dialogWebView = DialogWebView(
        context: context,
        url: url,
        itemDetailEngine: itemDetailEngine,
        popConfig: H5PopConfig(popHeight: 400),
      );
      dialogWebView.showPop();
    }
  }
}

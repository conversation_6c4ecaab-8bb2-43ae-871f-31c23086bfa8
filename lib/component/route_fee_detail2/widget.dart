import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class RouteFeeDetail2Widget extends StatelessWidget {
  const RouteFeeDetail2Widget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RouteFeeDetail2ChangeNotifier changeNotifier =
        Provider.of<RouteFeeDetail2ChangeNotifier>(context);
    return Container();
  }
}
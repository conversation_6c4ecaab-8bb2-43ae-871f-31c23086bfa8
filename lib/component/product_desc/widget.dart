import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../custom_widget/common_text_list_line.dart';
import 'notifier.dart';
import 'package:flutter_common/api/empty.dart';
class ProductDescWidget extends StatelessWidget {
  const ProductDescWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProductDescChangeNotifier changeNotifier =
        Provider.of<ProductDescChangeNotifier>(context);
    changeNotifier.fromJson();
    final Map<dynamic, dynamic>? moduleData = changeNotifier.moduleData;
    if (moduleData == null) {
      return empty;
    }
    return Container(
        color: const Color(0xFFFFFFFF),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
        child:CommonTextListLine(moduleData['title'],moduleData['descList'],textKey: 'mainInfo',descKey: 'subInfo',)
        );
  }
}

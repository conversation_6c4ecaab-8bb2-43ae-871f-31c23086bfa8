import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import './mock.dart';
class ProductDescChangeNotifier extends ComponentChangeNotifier {
  ProductDescChangeNotifier(ComponentContext context) : super(context);
  Map<dynamic, dynamic>? moduleData;

  @override
  void fromJson() {
    //mock数据
    // moduleData = mockData['productDesc']['data'];
    // return;
    if (dataModel == null || dataModel['productDesc'] == null || dataModel['productDesc']['data'] == null) {
      return;
    }
    moduleData = dataModel['productDesc']['data'];
  }
}

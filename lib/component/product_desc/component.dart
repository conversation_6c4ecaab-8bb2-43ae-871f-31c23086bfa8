import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 产品说明
class ProductDescComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<ProductDescChangeNotifier>.value(
      value: ProductDescChangeNotifier(context),
      child: const ProductDescWidget(),
    );
  }
}
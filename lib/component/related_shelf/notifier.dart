import 'package:flutter/cupertino.dart';

import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import '../../utils/safe_access.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'model.dart';

class RelatedShelfChangeNotifier extends ComponentChangeNotifier {
  RelatedShelfDataModel? relatedShelfDataModel;
  RelatedShelfChangeNotifier(ComponentContext context) : super(context);
  @override
  void fromJson() {
    relatedShelfDataModel ??= dataModel['relatedItems'] != null
        ? RelatedShelfModel.fromJson(SafeAccess.safeParseMap(dataModel['relatedItems'])).data
        : null;
  }
  void itemClick(BuildContext context,String? url){
    if(url != null&& url.isNotEmpty){
      FliggyNavigatorApi.getInstance().push(context, url, anim: Anim.slide);
    }
  }
}

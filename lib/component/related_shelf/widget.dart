import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fround_image/fround_image.dart';
import '../../custom_widget/block_title.dart';
import '../../custom_widget/null_widget.dart';
import '../../utils/TextConfig.dart';
import '../../utils/common_config.dart';
import 'model.dart';
import 'notifier.dart';

class RelatedShelfWidget extends StatelessWidget {
  const RelatedShelfWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RelatedShelfChangeNotifier changeNotifier =
        Provider.of<RelatedShelfChangeNotifier>(context);
    changeNotifier.fromJson();
    final RelatedShelfDataModel? relatedShelfDataModel =
        changeNotifier.relatedShelfDataModel;
    return relatedShelfDataModel == null || relatedShelfDataModel.itemType == 0
        ? nullWidget
        : Container(
            padding: const EdgeInsets.fromLTRB(
                paddingLeft, 0, paddingRight, paddingBottom),
            margin: const EdgeInsets.only(bottom: itemDivider),
            decoration: BoxDecoration(
                color: const Color.fromARGB(255, 255, 255, 255),
                borderRadius: BorderRadius.circular(6.00)),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  BlockTitle(relatedShelfDataModel.desc!),
                  if (relatedShelfDataModel.itemType == 1)
                    _buildItemWidget(
                        context,changeNotifier,
                        relatedShelfDataModel
                            .relatedItems![0].relatedTripItems![0],
                        null)
                  else if (relatedShelfDataModel.itemType == 2)
                    SizedBox(
                      height: 55,
                      child: ListView.separated(
                        padding: EdgeInsets.zero,
                        scrollDirection: Axis.horizontal,
                        separatorBuilder: (BuildContext context, int index) =>
                            const Divider(
                          height: 15,
                          color: Color(0x00000000),
                        ),
                        itemCount: relatedShelfDataModel
                                .relatedItems![0].relatedTripItems?.length ??
                            0,
                        itemBuilder: (BuildContext context, int index) {
                          final RelatedTripItems? item = relatedShelfDataModel
                              .relatedItems![0].relatedTripItems?[index];
                          return SizedBox(
                              width: 200,
                              child: _buildItemWidget(context,changeNotifier, item, 200));
                        },
                      ),
                    )
                  else if (relatedShelfDataModel.itemType == 3)
                    SizedBox(
                      height: 126,
                      child: ListView.separated(
                        padding: EdgeInsets.zero,
                        scrollDirection: Axis.horizontal,
                        separatorBuilder: (BuildContext context, int index) =>
                            const Divider(
                          height: 15,
                          color: Color(0x00000000),
                        ),
                        itemCount:
                            relatedShelfDataModel.relatedItems?.length ?? 0,
                        itemBuilder: (BuildContext context, int index) {
                          final RelatedItems? relatedItems =
                              relatedShelfDataModel.relatedItems![index];
                          final int relatedItemsLength =
                              relatedItems?.relatedTripItems?.length ?? 0;
                          final RelatedTripItems? item1 = relatedItemsLength > 0
                              ? (relatedItems?.relatedTripItems?[0])
                              : null;
                          final RelatedTripItems? item2 = relatedItemsLength > 1
                              ? (relatedItems?.relatedTripItems?[1])
                              : null;
                          return SizedBox(
                            width: 200,
                            child: Column(
                              children: <Widget>[
                                _buildItemWidget(context,changeNotifier, item1, 200),
                                const SizedBox(
                                  height: 15,
                                ),
                                _buildItemWidget(context, changeNotifier,item2, 200),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                ]));
  }

  ///猜你喜欢item
  Widget _buildItemWidget(
      BuildContext context, RelatedShelfChangeNotifier changeNotifier,RelatedTripItems? item, double? width) {
    return item == null
        ? nullWidget
        : GestureDetector(
            onTap: () {
              changeNotifier.itemClick(
                  context, item.tripJumpInfo?.jumpH5Url);
            },
            child: SizedBox(
              height: 55.00,
              width: width,
              child: Row(children: <Widget>[
                if (item.itemPic != '')
                  FRoundImage.network(
                    item.itemPic!,
                    borderRadius: BorderRadius.circular(6.00),
                    width: 55,
                    height: 55,
                  ),
                Container(
                    width: width != null ? width! - 55 : null,
                    padding: const EdgeInsets.only(left: 9),
                    child: _buildTitle(item)),
              ]),
            ),
          );
  }

  Widget _buildTitle(RelatedTripItems item) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Text(item.itemName!,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.start,
            style: TextStyle(
                fontWeight: FontWeightExt.bold,
                color: Color.fromARGB(255, 51, 51, 51),
                fontSize: 14.00)),
        Container(
          margin: const EdgeInsets.only(top: 2, bottom: 4),
          child: Text(item.desc!,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.start,
              style: const TextStyle(
                  color: Color.fromARGB(255, 153, 153, 153), fontSize: 11.00)),
        ),
        _buildPrice(item),
      ],
    );
  }

  Widget _buildPrice(RelatedTripItems item) {
    return Row(children: <Widget>[
      const Text('¥',
          textAlign: TextAlign.end,
          style: TextStyle(
              color: Color.fromARGB(255, 255, 64, 26), fontSize: 12.00)),
      Text(item.currPrice!,
          textAlign: TextAlign.end,
          style: TextStyle(
              fontWeight: FontWeightExt.bold,
              color: Color.fromARGB(255, 255, 64, 26),
              fontSize: 15.00)),
    ]);
  }
}

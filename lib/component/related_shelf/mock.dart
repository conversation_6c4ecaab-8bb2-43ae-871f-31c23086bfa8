const String mockData = '''
{
  "relatedItems": {
    "data": {
      "desc": "黄龙溪欢乐田园 - 正在热卖",
      "relatedItems": [
        {
          "relatedTripItems": [
            {
              "currPrice": "359",
              "event": {},
              "itemId": "673051052021",
              "itemName": "露营帐篷套票",
              "itemPic": "//img.alicdn.com/i1/6000000007351/O1CN01eoQ4iu24AoEpacODA_!!6000000007351-2-itemdesc.png",
              "tripJumpInfo": {
                "jumpH5Url": "https://market.m.taobao.com/app/trip/h5-traveldx-detail/pages/index/index.html?id=673051052021&_fli_newpage=1&_fli_native_ver=9.9.3&categoryId=*********",
                "jumpNative": false
              }
            },
            {
              "currPrice": "107",
              "event": {},
              "itemId": "670063735250",
              "itemName": "大门票（提前一天预定）",
              "itemPic": "//img.alicdn.com/i2/6000000000918/O1CN01B6ntSm1IeULe8sw82_!!6000000000918-0-itemdesc.jpg",
              "tripJumpInfo": {
                "jumpH5Url": "https://market.m.taobao.com/app/trip/h5-traveldx-detail/pages/index/index.html?id=670063735250&_fli_newpage=1&_fli_native_ver=9.9.3&categoryId=*********",
                "jumpNative": false
              }
            }
          ],
          "sort": 1
        },
        {
          "relatedTripItems": [
            {
              "currPrice": "138",
              "event": {},
              "itemId": "673020188198",
              "itemName": "欢乐田园+观光车套票",
              "itemPic": "//img.alicdn.com/i1/6000000003199/O1CN01VsrCAX1ZVBlzft4xc_!!6000000003199-2-itemdesc.png",
              "tripJumpInfo": {
                "jumpH5Url": "https://market.m.taobao.com/app/trip/h5-traveldx-detail/pages/index/index.html?id=673020188198&_fli_newpage=1&_fli_native_ver=9.9.3&categoryId=*********",
                "jumpNative": false
              }
            }
          ],
          "sort": 2
        }
      ]
    },
    "tag": "relatedItems"
  }
}
''';

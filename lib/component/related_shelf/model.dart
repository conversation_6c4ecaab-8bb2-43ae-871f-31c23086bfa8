import '../../utils/safe_access.dart';

class RelatedShelfModel {
  RelatedShelfDataModel? data;
  String? tag;

  RelatedShelfModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null
        ? RelatedShelfDataModel.fromJson(SafeAccess.safeParseMap(json['data']))
        : null;
    tag = SafeAccess.safeParseString(json['tag']);
  }
}

class RelatedShelfDataModel {
  String? desc;
  List<RelatedItems>? relatedItems;
  ///0:无组件 1:一个 2:一排横滑 3.两排横滑
  int itemType = 0;

  RelatedShelfDataModel.fromJson(Map<String, dynamic> json) {
    desc = SafeAccess.safeParseString(json['desc']);
    if (json['relatedItems'] != null) {
      relatedItems = <RelatedItems>[];
      SafeAccess.safeParseList(json['relatedItems']).forEach((dynamic v) {
        relatedItems!.add(RelatedItems.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    itemType = _itemLength();
  }

  int _itemLength() {
    final int relatedItemsLength =
        SafeAccess.safeParseInt(relatedItems?.length);
    if (relatedItemsLength == 0) {
      return 0;
    }
    if (relatedItemsLength == 1) {
      final int relatedTripItemsLength =
          SafeAccess.safeParseInt(relatedItems![0]?.relatedTripItems?.length);
      if(relatedTripItemsLength<=1){
        return relatedTripItemsLength;
      }
      return 2;
    }
    return 3;
  }
}

class RelatedItems {
  List<RelatedTripItems>? relatedTripItems;
  int? sort;

  RelatedItems.fromJson(Map<String, dynamic> json) {
    if (json['relatedTripItems'] != null) {
      relatedTripItems = <RelatedTripItems>[];
      SafeAccess.safeParseList(json['relatedTripItems']).forEach((dynamic v) {
        relatedTripItems!
            .add(RelatedTripItems.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    sort = SafeAccess.safeParseInt(json['sort']);
  }
}

class RelatedTripItems {
  String? currPrice;
  String? itemId;
  String? itemName;
  String? desc;
  String? itemPic;
  TripJumpInfo? tripJumpInfo;

  RelatedTripItems.fromJson(Map<String, dynamic> json) {
    currPrice = SafeAccess.safeParseString(json['currPrice']);
    itemId = SafeAccess.safeParseString(json['itemId']);
    itemName = SafeAccess.safeParseString(json['itemName']);
    desc = SafeAccess.safeParseString(json['desc']);
    itemPic = SafeAccess.safeParseString(json['itemPic']);
    tripJumpInfo = json['tripJumpInfo'] != null
        ? TripJumpInfo.fromJson(SafeAccess.safeParseMap(json['tripJumpInfo']))
        : null;
  }
}

class TripJumpInfo {
  String? jumpH5Url;
  bool? jumpNative;

  TripJumpInfo.fromJson(Map<String, dynamic> json) {
    jumpH5Url = SafeAccess.safeParseString(json['jumpH5Url']);
    jumpNative = SafeAccess.safeParseBoolean(json['jumpNative']);
  }
}

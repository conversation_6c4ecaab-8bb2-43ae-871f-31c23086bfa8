import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class RouteItineraryPlanFy25Widget extends StatelessWidget {
  const RouteItineraryPlanFy25Widget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RouteItineraryPlanFy25ChangeNotifier changeNotifier =
        Provider.of<RouteItineraryPlanFy25ChangeNotifier>(context);
    return Container();
  }
}
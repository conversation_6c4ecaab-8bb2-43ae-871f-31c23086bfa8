import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description TODO 组件功能
class RouteItineraryPlanFy25Component extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<RouteItineraryPlanFy25ChangeNotifier>.value(
      value: RouteItineraryPlanFy25ChangeNotifier(context),
      child: const RouteItineraryPlanFy25Widget(),
    );
  }
}
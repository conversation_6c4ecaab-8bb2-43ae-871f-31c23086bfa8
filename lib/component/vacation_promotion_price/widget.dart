import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class VacationPromotionPriceWidget extends StatelessWidget {
  const VacationPromotionPriceWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VacationPromotionPriceChangeNotifier changeNotifier =
        Provider.of<VacationPromotionPriceChangeNotifier>(context);
    return Container();
  }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class RecommendByPoiWidget extends StatelessWidget {
  const RecommendByPoiWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RecommendByPoiChangeNotifier changeNotifier =
        Provider.of<RecommendByPoiChangeNotifier>(context);
    return Container();
  }
}
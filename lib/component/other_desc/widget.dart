import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class OtherDescWidget extends StatelessWidget {
  const OtherDescWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final OtherDescChangeNotifier changeNotifier =
        Provider.of<OtherDescChangeNotifier>(context);
    changeNotifier.fromJson();
    return changeNotifier.otherDescDataModel == null?Container():Container();
  }
}
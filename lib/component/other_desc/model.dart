import '../../utils/safe_access.dart';

class OtherDescModel {
  OtherDescDataModel? data;

  OtherDescModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null
        ? OtherDescDataModel.fromJson(SafeAccess.safeParseMap(json['data']))
        : null;
  }
}

class OtherDescDataModel {
  String? title;
  List<KeyValueList>? keyValueList;

  OtherDescDataModel.fromJson(Map<String, dynamic> json) {
    title = SafeAccess.safeParseString(json['title']);
    if (json['keyValueList'] != null) {
      keyValueList = <KeyValueList>[];
      SafeAccess.safeParseList(json['keyValueList']).forEach((dynamic v) {
        keyValueList!.add(KeyValueList.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
  }
}

class KeyValueList {
  String? content;

  KeyValueList.fromJson(Map<String, dynamic> json) {
    content = SafeAccess.safeParseString(json['content']);
  }
}

import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import '../../utils/safe_access.dart';
import 'model.dart';

class OtherDescChangeNotifier extends ComponentChangeNotifier {
  OtherDescDataModel? otherDescDataModel;

  OtherDescChangeNotifier(ComponentContext context) : super(context);

  @override
  void fromJson() {
    otherDescDataModel ??= dataModel['notice'] != null
        ? OtherDescModel.fromJson(SafeAccess.safeParseMap(dataModel['notice'])).data
        : null;
  }
}

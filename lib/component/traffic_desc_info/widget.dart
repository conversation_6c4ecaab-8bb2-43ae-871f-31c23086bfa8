import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class TrafficDescInfoWidget extends StatelessWidget {
  const TrafficDescInfoWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TrafficDescInfoChangeNotifier changeNotifier =
        Provider.of<TrafficDescInfoChangeNotifier>(context);
    return Container();
  }
}
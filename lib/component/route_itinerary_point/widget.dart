import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class RouteItineraryPointWidget extends StatelessWidget {
  const RouteItineraryPointWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RouteItineraryPointChangeNotifier changeNotifier =
        Provider.of<RouteItineraryPointChangeNotifier>(context);
    return Container();
  }
}
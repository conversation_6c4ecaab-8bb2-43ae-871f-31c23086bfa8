import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier/media_container_change_notifier.dart';
import 'widget.dart';
/// 商详头图组件
///
/// 数据绑定:headMedia
/// 数据结构:
/// 653276581362这个品没有数据
class HeadMediaProcessComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<HeadMediaProcessChangeNotifier>.value(
      value: HeadMediaProcessChangeNotifier(context),
      child: FliggyVacationHeadMediaWidget(),
    );
  }
}

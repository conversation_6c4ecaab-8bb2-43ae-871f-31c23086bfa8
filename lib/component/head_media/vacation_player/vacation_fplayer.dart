import 'dart:async';
import 'dart:io';
import 'dart:math';

import 'package:fbroadcast/fbroadcast.dart';
import 'package:ficonfont/ficonfont.dart';
import 'package:flutter/material.dart';
import 'package:fplayer/fplayer.dart' as fplayer;

import '../../../render/component/component_change_notifier.dart';
import '../notifier/media_container_change_notifier.dart';
import 'vacation_player.dart';
import 'package:fround_image/fround_image.dart';
import 'package:provider/provider.dart';

/// 用于构建 [FPlayerListenerWidget] 中，判断是否需要刷新
typedef Check = bool Function(FliggyVacationPlayerController controller);

/// 用户刷新
typedef SetStateBlock = void Function(void Function());

typedef BroadBlock = void Function(dynamic);

typedef BroadcastRegisterBlock = void Function(
    dynamic, void Function(dynamic)?);

/// 播放状态变化事件
const String VacationPlayer_OnUpdateStatus = 'VacationPlayer_OnUpdateStatus';

/// 改变操作面板的展示状态
/// 详见 [FliggyVacationPlayerController.showPanel]
const String VacationPlayer_ShowPanelChanged =
    'VacationPlayer_ShowPanelChanged';

enum FPlayerStatus {
  None, //正常状态
  VideoPrepared,
  VideoPlay,
  VideoPause,
  VideoComplete,
  VideoError
}

enum FPlayerRate {
  x1, //1倍速
  x0_5, //0.5倍速
  x1_5, //1.5倍速
  x2, //2倍速
  x4, //4倍速
}

/// 被同一个 [FliggyVacationPlayerController] 控制的播放组件，所有的状态都是同步的
class FliggyVacationPlayerController {
  late fplayer.FPlayerController _controller;

  FPlayerStatus _status = FPlayerStatus.None;

  FliggyVacationPlayerController() {
    _controller = fplayer.FPlayerController();
  }

  // final BoxFit _fitMode = BoxFit.cover;

  int updateFlag = 0;

  bool _mute = true;

  double duration = 0;

  double _currentPosition = 0;

  bool _canUpdate = true;

  String scene = '';

  /// 计时隐藏 Panel 的计时器
  Timer? hidePanelTimer;

  /// 计时隐藏 Panel 的时长
  int hidePanelTime = 3000;

  String? _url;
  final Set<VoidCallback> _listeners = <VoidCallback>{};

  final Set<VoidCallback> _progressListeners = <VoidCallback>{};

  /// 视频播放地址
  String get url => _url ?? '';

  bool _showControlButton = false;

  /// 是否展示操作面板
  /// 默认展示，这符合刚进入时的操作
  bool _showPanel = true;

  /// 获取操作面板显示状态
  bool get showPanel => _showPanel;

  /// 修改操作面板展示状态
  set showPanel(bool value) {
    if (value != _showPanel) {
      _showPanel = value;
      FBroadcast.instance(hashCode).broadcast(VacationPlayer_ShowPanelChanged);
    }
  }

  bool _hasDispose = false;

  /// 标识是否已经被释放
  /// 被释放后，就不能再使用了
  bool get hasDispose => _hasDispose;

  /// 可监听一些视屏组件的事件
  /// 事件列表见：[Key_OnUpdateStatus]
  /// [callback] 中可以接收一个任意类型的参数
  /// 注：监听环境是以 Controller 实例作为隔离的，不同 Controller 间的同名事件互不干扰。
  void listen({required String event, ValueChanged? callback}) {
    FBroadcast.instance(hashCode).register(event, (dynamic value, _) {
      callback?.call(value);
    });
  }

  /// 可在组件环境内发送一些内部事件，通过 [listen] 注册的监听器，可以接收到
  /// 注：内部定义的事件，只能被监听，主动发送
  void event({required String event, dynamic data}) {
    // if (event != Key_OnUpdateStatus ||
    //     event != Key_ShowPanelChanged ||
    //     event != Key_ChangeShowCenterButton ||
    //     event != Key_OnFullScreenChanged ||
    //     event != Key_OnControllerDestroy) {
    FBroadcast.instance(hashCode).broadcast(event, value: data);
    // }
  }

  /// 控制是否展示 PauseButton
  void showPauseButton(bool show) {
    Timer(Duration(milliseconds: 0), () {
      showPanel = show;
      // FBroadcast.instance(hashCode)
      //     .broadcast(Key_ChangeShowCenterButton, value: show);
    });
  }

  /// 清理延迟隐藏 Panel 的任务
  void cancelHidePanelTime() {
    hidePanelTimer?.cancel();
  }

  /// 延迟隐藏 Panel
  void delayHidePanel({int delay = 3000}) {
    cancelHidePanelTime();
    hidePanelTimer = Timer(Duration(milliseconds: delay), () {
      showPanel = false;
    });
  }

  void closeFullScreen() {
    print('closeFullScreen');
    if (_backListener != null) {
      _backListener!();
    }
  }

  VoidCallback? _backListener;

  void addBackListener(VoidCallback listener) {
    _backListener = listener;
  }

  void removeBackListener() {
    if (_backListener != null) {
      _backListener = null;
    }
  }

  set showControlButton(bool state) {
    _showControlButton = state;
  }

  /// 是否静音
  bool get muted => _mute;

  /// 设置静音
  set muted(bool value) {
    if (muted != value) {
      _mute = value;
      if (_hasLoaded()) {
        _controller.setMute(value);
      }
    }
  }

  /// 当前位置
  double get position => _currentPosition;

  set currentPosition(double value) {
    _currentPosition = value;

    final List<VoidCallback> copy = _progressListeners.toList();
    for (final VoidCallback element in copy) {
      element.call();
    }
  }

  /// 视频原始尺寸
  Size get naturalSize => _controller.naturalSize ?? Size.zero;

  set status(FPlayerStatus currentStatus) {
    _status = currentStatus;
    final List<VoidCallback> copy = _listeners.toList();
    for (final VoidCallback element in copy) {
      element.call();
    }
    FBroadcast.instance(hashCode)
        .broadcast(VacationPlayer_OnUpdateStatus, value: status);
  }

  /// 当前状态
  FPlayerStatus get status => _status;

  void sharePlayerSync() {}

  /// 是否在使用共享播放器
  bool isUseSharePlayer() {
    return false;
  }

  /// 播放
  void play() {
    _controller.play();
  }

  /// 暂停
  void pause() {
    _controller.pause();
  }

  /// 停止
  void stop() {
    print('stop');
    _controller.pause();
  }

  /// 跳转至指定时间点
  void seekTo(int playBackTime) {
    _controller.seekTo(playBackTime.toDouble());
  }

  /// 添加状态监听
  void addStatusListener(VoidCallback listener) {
    _listeners.add(listener);
    if (status != FPlayerStatus.None) {
      listener.call();
    }
  }

  /// 移除状态监听
  void removeStatusListener(VoidCallback listener) {
    _listeners.remove(listener);
  }

  /// 添加播放进度监听
  void addProgressListener(VoidCallback listener) {
    _progressListeners.add(listener);
  }

  /// 移除播放进度监听
  void removeProgressListener(VoidCallback listener) {
    _progressListeners.remove(listener);
  }

  void _dispose() {
    stop();
    _controller.destroy();
    // FBroadcast.instance(hashCode).broadcast(Key_OnControllerDestroy);
    FBroadcast.instance(hashCode).dispose();
    FBroadcast.instance(this).dispose();
    _listeners.clear();
    _progressListeners.clear();
    _hasDispose = true;
  }

  /// 是否加载成功
  bool _hasLoaded() {
    return status == FPlayerStatus.VideoPrepared ||
        status == FPlayerStatus.VideoPlay ||
        status == FPlayerStatus.VideoPause ||
        status == FPlayerStatus.VideoComplete;
  }

  /// 是否处于暂停状态
  bool isPause() {
    final bool r = status != FPlayerStatus.VideoPlay;
    return r;
  }

  void blockUpdate() {
    _canUpdate = false;
  }

  void startUpdate() {
    _canUpdate = true;
  }
}

/// 视频播放组件
class FPlayer extends StatefulWidget {
  /// 控制器
  final FliggyVacationPlayerController controller;

  /// 宽
  double width = 0;

  /// 高
  double height = 0;

  /// 视频 URL
  final String url;

  /// 是否允许循环播放。默认 false。
  bool needLoop = false;

  /// 是否允许自动播放。默认 false。
  bool autoPlay = false;

  /// 是否允许后台暂停播放。默认 true。
  bool pauseOnBackstage = true;

  /// 裁剪模式。详见 [BoxFit]。默认 [BoxFit.cover]。
  BoxFit? fitMode;

  /// 首次是否静音。默认 true。
  bool muted = true;

  /// 封面组件
  Widget? coverWidget;

  /// 是否启用暂停按钮。默认 true。
  bool pauseButtonEnable = true;

  /// 暂停按钮组件。有默认效果。
  Widget? onPauseWidget;

  /// 播放按钮组件。有默认效果。
  Widget? onPlayWidget;

  /// Loading 组件
  Widget? loadingWidget;

  /// 通过 [StatefulWidgetBuilder] 函数构建操作面板
  StatefulWidgetBuilder? panelBuilder;

  /// 边角。详见 [FPlayerCorner]
  FPlayerCorner? corner;

  /// 边框颜色
  Color? strokeColor;

  /// 边框宽度
  double? strokeWidth;

  /// 蒙层颜色
  Color? maskColor;

  /// 视频播放完后是否展示coverImg
  bool loadShowCoverImg = true;

  /// 为 [FPlayer] 增加动作组件。使用[Positioned] 包裹可以控制动作组件的位置。
  List<Widget>? actions;

  Function(UserEvent)? eventListener;

  bool disposeRelease = true;

  double bottomMargin = 0;

  /// 自动隐藏 Panel 的时间
  /// 单位：毫秒
  int hidePanelTime = 0;

  String? scene;

  FPlayer({
    Key? key,
    required this.controller,
    required this.url,
    this.width = 375.0,
    this.height = 375.0,
    this.needLoop = false,
    this.autoPlay = false,
    this.pauseOnBackstage = true,
    this.fitMode = BoxFit.cover,
    this.bottomMargin = 0,
    this.muted = true,
    this.coverWidget,
    this.onPauseWidget,
    this.onPlayWidget,
    this.loadingWidget,
    this.corner,
    this.strokeColor,
    this.strokeWidth,
    this.pauseButtonEnable = true,
    this.maskColor,
    this.panelBuilder,
    this.actions,
    this.eventListener,
    this.disposeRelease = true,
    this.hidePanelTime = 3000,
    this.scene,
    this.loadShowCoverImg = true,
  }) : super(key: key);

  @override
  _FPlayerState createState() => _FPlayerState();
}

class _FPlayerState extends State<FPlayer> {
  bool showOperationPanel = true;
  String? cacheVideoUrl;
  bool? cacheMuted;

  VoidCallback? listener;

  /// 计时隐藏 Panel 的计时器
  Timer? hidePanelTimer;

  double get curPosition => widget.controller.position ?? 0.0;

  double get duration => widget.controller.duration ?? 0.0;

  // ValueNotifier<bool> showPanelNotifier;

  bool isFirst = true;

  @override
  void initState() {
    widget.controller.addStatusListener(listener = () {
      if (!mounted) {
        return;
      }
      if (widget.controller._hasLoaded()) {
        widget.controller.muted = widget.muted;
        // widget.controller.rate = widget.controller.rate;
        if (listener != null) {
          widget.controller.removeStatusListener(listener!);
        }
      }
    });
    widget.controller.hidePanelTime = widget.hidePanelTime;
    super.initState();
  }

  @override
  void didUpdateWidget(FPlayer oldWidget) {
//    if (widget.controller?._controller == null) {
//      widget.controller?._controller = innerController;
//    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    /// 如果是真正控制着 [FPlayerController] 释放的组件，就触发释放
    if (widget.disposeRelease) {
      widget.controller.showPauseButton(false);
      widget.controller._dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // updateController();
    widget.controller.muted = widget.muted;

    final List<Widget> children = <Widget>[];

    /// player
    // children.add(IFPlayer(controller: widget.controller._controller));

    print(
        'sagara width:${widget.width} height:${widget.height} muted:${widget.muted} autoPlay:${widget.autoPlay} fitMode:${widget.fitMode} needLoop:${widget.needLoop}');
    children.add(SizedBox(
      width: widget.width,
      height: widget.height,
      child: fplayer.FPlayerView(
        widget.controller._controller,
        url: widget.url,
        loop: widget.needLoop,
        autoPlay: widget.autoPlay,
        muted: widget.muted,
        useCache: true,
        cachePlayer: false,
        cacheKey: widget.url,
        fit: widget.fitMode,
        onVideoStart: () {
          widget.controller.status = FPlayerStatus.VideoPlay;
          print('sagara 视频第一次播放');
        },
        onFirstVideoFrameRendered: (Map<dynamic, dynamic> dic) {
          widget.controller.status = FPlayerStatus.VideoPlay;

          print('sagara 视频第一帧渲染完成 $dic');
        },
        onVideoPlay: () {
          widget.controller.status = FPlayerStatus.VideoPlay;

          print('sagara 视频播放');
        },
        onVideoPause: () {
          widget.controller.status = FPlayerStatus.VideoPause;

          print('sagara 视频暂停');
        },
        onVideoComplete: () {
          widget.controller.status = FPlayerStatus.VideoComplete;

          print('sagara 视频播放完成');
        },
        onVideoError: (Map<dynamic, dynamic> error) {
          widget.controller.status = FPlayerStatus.VideoError;
          print('sagara 视频播放出错 $error');
        },
        onVideoPrepared: (Map<dynamic, dynamic> info) {
          final double duration = info['duration'] ?? 0;
          widget.controller.duration = duration;
          print('sagara 视频准备就绪:视频时长$duration');
          print('sagara 视频url:${widget.url}');
          print(
              'sagara 视频尺寸:width: ${widget.controller.naturalSize.width} height: ${widget.controller.naturalSize.height}');
          print('sagara 视频准备就绪: status${widget.controller.status}');

          if (widget.controller.status != FPlayerStatus.VideoPlay) {
            widget.controller.status = FPlayerStatus.VideoPrepared;
          }
        },
        onVideoTimeChanged: (Map<dynamic, dynamic> dic) {
          final dynamic time = dic['time'] ?? 0;
          widget.controller.currentPosition = time.toDouble();
          // print('sagara 视频播放中：$time');

          // _updateLog('视频进度发生变化');
        },
      ),
    ));

    /// 封面图
    if (widget.coverWidget != null) {
      children.add(buildCover());
    }

    /// mask
    children.add(buildMask());

    /// 暂停按钮
    if (widget.pauseButtonEnable) {
      children.add(buildCenterButton());
    }

    /// 动作按钮
    if (widget.actions != null) {
      children.addAll(widget.actions!.map((Widget element) {
        return Stateful(
          initState: (SetStateBlock _setState, Map<dynamic, dynamic> data) {
            FBroadcast.instance(widget.controller.hashCode).register(
              VacationPlayer_ShowPanelChanged,
              (dynamic value, BroadBlock? callback) {
                if (data['this'].mounted) {
                  _setState(() {});
                }
              },
              context: data,
            );
          },
          builder: (BuildContext context, SetStateBlock _setState,
              Map<dynamic, dynamic> data) {
            return widget.controller.showPanel ? element : Container();
          },
        );
      }));
    }

    /// 底部操作Bar
    if (widget.panelBuilder != null) {
      children.add(buildBottomBar());
    }

    /// loading
    if (widget.loadingWidget != null) {
      children.add(buildLoading());
    }

    Widget child = Container(
      width: widget.width,
      height: widget.height,
      foregroundDecoration: buildDecoration(),
      child: Stack(
        alignment: Alignment.center,
        children: children,
      ),
    );
    if (widget.corner != null) {
      child = ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(widget.corner?.leftTopCorner ?? 0),
          topRight: Radius.circular(widget.corner?.rightTopCorner ?? 0),
          bottomRight: Radius.circular(widget.corner?.rightBottomCorner ?? 0),
          bottomLeft: Radius.circular(widget.corner?.leftBottomCorner ?? 0),
        ),
        child: child,
      );
    }
    return child;
  }

  /// 封面 部件
  Widget buildCover() {
    return Stateful(
      initState: (SetStateBlock _setState, Map<dynamic, dynamic> data) {
        data['status'] = widget.controller.status;
        FBroadcast.instance(widget.controller.hashCode).register(
          VacationPlayer_OnUpdateStatus,
          (dynamic value, BroadBlock? callback) {
            if (data['status'] != widget.controller.status) {
              data['status'] = widget.controller.status;
              if (data['this'].mounted) {
                _setState(() {});
              }
            }
          },
          context: data,
        );
      },
      builder: (BuildContext context, SetStateBlock _setState,
          Map<dynamic, dynamic> data) {
        final FliggyVacationPlayerController controller = widget.controller;
        bool showCover = controller.status == FPlayerStatus.None ||
            controller.status == FPlayerStatus.VideoPrepared ||
            controller.status == FPlayerStatus.VideoError ||
            controller.status == FPlayerStatus.VideoComplete;

        if (controller.status == FPlayerStatus.VideoComplete &&
            widget.loadShowCoverImg != null &&
            widget.loadShowCoverImg == false) {
          showCover = false;
        }
        return showCover ? widget.coverWidget ?? Container() : Container();
      },
    );
  }

  /// loading 部件
  Widget buildLoading() {
    return Stateful(
      initState: (SetStateBlock _setState, Map<dynamic, dynamic> data) {
        data['status'] = widget.controller.status;
        FBroadcast.instance(widget.controller.hashCode).register(
          VacationPlayer_OnUpdateStatus,
          (dynamic value, BroadBlock? callback) {
            if (data['status'] != widget.controller.status) {
              data['status'] = widget.controller.status;
              if (data['this'].mounted) {
                _setState(() {});
              }
            }
          },
          context: data,
        );
      },
      builder: (BuildContext context, SetStateBlock _setState,
          Map<dynamic, dynamic> data) {
        final FliggyVacationPlayerController controller = widget.controller;
        final bool show = controller.status == FPlayerStatus.None;
        return show ? Center(child: widget.loadingWidget) : Container();
      },
    );
  }

  Positioned buildBottomBar() {
    return Positioned(
      bottom: widget.bottomMargin ?? 0,
      child: Stateful(
        initState: (SetStateBlock _setState, Map<dynamic, dynamic> data) {
          FBroadcast.instance(widget.controller.hashCode).register(
            VacationPlayer_ShowPanelChanged,
            (dynamic value, BroadBlock? callback) {
              if (data['this'].mounted) {
                _setState(() {});
              }
            },
            more: <String, BroadcastRegisterBlock>{
              // Key_OnFullScreenChanged: (value, callback) {
              //   if (data['this'].mounted) {
              //     _setState(() {});
              //   }
              // },
            },
            context: data,
          );
        },
        builder: (BuildContext context, SetStateBlock _setState,
            Map<dynamic, dynamic> data) {
          final FliggyVacationPlayerController controller = widget.controller;
          final Widget? panel = widget.panelBuilder?.call(context, _setState);
          if (panel is DefaultSliderPanelWidget) {
            panel
              .._showPanel = widget.controller.showPanel
              ..controller = controller
              ..playerWidth = widget.width
              ..playerHeight = widget.height;
          }
          return panel ?? Container();
        },
      ),
    );
  }

  GestureDetector buildMask() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        /// 点击取消 Panel 的计时隐藏
        // hidePanelTimer?.cancel();
        widget.controller.cancelHidePanelTime();
        widget.controller.showPanel = !widget.controller.showPanel;
        if (widget.controller.showPanel) {
          widget.controller.delayHidePanel();
        }
      },
      child: Container(
        color: widget.maskColor ?? Color(0x00FFFFFF),
      ),
    );
  }

  Decoration buildDecoration() {
    final BorderRadius borderRadius = widget.corner == null
        ? BorderRadius.zero
        : BorderRadius.only(
            topLeft: Radius.circular(widget.corner?.leftTopCorner ?? 0),
            topRight: Radius.circular(widget.corner?.rightTopCorner ?? 0),
            bottomRight: Radius.circular(widget.corner?.rightBottomCorner ?? 0),
            bottomLeft: Radius.circular(widget.corner?.leftBottomCorner ?? 0),
          );
    final Color sideColor = widget.strokeColor ?? Color(0x00FFFFFF);
    final BorderSide borderSide = BorderSide(
      width: widget.strokeWidth ?? 0,
      color: sideColor,
      style: BorderStyle.solid,
    );
    final ShapeDecoration decoration = ShapeDecoration(
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius,
        side: borderSide,
      ),
    );
    return decoration;
  }

  /// 暂停/播放按钮 部件
  Widget buildCenterButton() {
    return Stateful(
      initState: (SetStateBlock _setState, Map<dynamic, dynamic> data) {
        data['status'] = widget.controller.status;
        data['canShow'] = true;
        data['show'] = false;
        FBroadcast.instance(widget.controller.hashCode).register(
          VacationPlayer_OnUpdateStatus,
          (dynamic value, BroadBlock? callback) {
            if (data['status'] != widget.controller.status) {
              data['status'] = widget.controller.status;
              if (data['this'].mounted) {
                _setState(() {});
              }
            }
          },
          more: <String, BroadcastRegisterBlock>{
            VacationPlayer_ShowPanelChanged:
                (dynamic value, BroadBlock? callback) {
              if (data['this'].mounted) {
                _setState(() {});
              }
            },
            // Key_ChangeShowCenterButton: (value, callback) {
            //   if (data['this'].mounted) {
            //     data['show'] = value ?? false;
            //     _setState(() {});
            //   }
            // },
            // Key_OnControllerDestroy: (value, callback) {
            //   if (data["this"].mounted) {
            //     data['canShow'] = false;
            //     _setState(() {});
            //   }
            // },
          },
          context: data,
        );
      },
      builder: (BuildContext context, SetStateBlock _setState,
          Map<dynamic, dynamic> data) {
        final FliggyVacationPlayerController controller = widget.controller;
        final bool pause = controller?.isPause() ?? false;
        Widget child;
        // 让全图可点
        if (pause) {
          child = widget.onPauseWidget ??
              Container(
                height: 375,
                width: 375,
                color: Color(0x00ffffff),
                child: Align(
                  child: FRoundImage.network(
                    'https://gw.alicdn.com/imgextra/i4/O1CN01AKLGAZ1vKaMNH0BxT_!!6000000006154-2-tps-200-200.png', // 替换为你的实际图片URL
                    fit: BoxFit.cover,
                    height: 50,
                    width: 50,
                  ),
                ),
              );
        } else {
          child = Container(
            height: 375,
            width: 375,
            color: Color(0x00ffffff),
          );
        }

        /// 显示的场景：
        ///   1. 暂停的时候，需要显示播放按钮
        ///   2. Panel 显示时，需要显示 该按钮
        ///   3. 单独显示 该按钮
        /// 不能显示的场景
        ///   1. 视屏停止后，不能显示
        ///   2. 出错后，需要显示出错，不显示该按钮
        final bool show = (pause || widget.controller.showPanel) &&
            data['canShow'] &&
            controller.status != FPlayerStatus.VideoError;
        return show
            ? Container(
                alignment: Alignment.center,
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    final bool isPause = controller.isPause() ?? false;
                    if (isPause) {
                      controller.play();

                      /// 播放后，开启隐藏计时
                      controller.delayHidePanel();
                    } else {
                      controller.pause();
                    }
                    widget.eventListener
                        ?.call(isPause ? UserEvent.PLAY : UserEvent.PAUSE);
                  },
                  child: child,
                ),
              )
            : Container();
      },
    );
  }
}

///
/// [FPlayer] 的默认操作面板实现。
/// 如果有定制需求，可以参考该实现。
/// 通常，操作面板应该通过  [StatefulWidgetBuilder] 函数来构造，已便获取可用信息。
///
/// 进度条的宽度会自动根据 [fontSize]、[padding]、[actions] 等动态计算，开发者无需关心
///
// ignore: must_be_immutable
class DefaultSliderPanelWidget extends StatefulWidget {
  /// 控制器。
  FliggyVacationPlayerController? controller;

  /// [FPlayer] 的宽。即 [FPlayer.width]。
  double? playerWidth;

  /// [FPlayer] 的高。即 [FPlayer.height]。
  double? playerHeight;

  /// double sliderWidth;

  /// 进度条高度。默认 3.0
  final double sliderHeight;

  /// 是否启用次级进度条。
  /// 次级进度条会在操作面板隐藏后出现在 [FPlayer] 的底部，长度充满 [FPlayer]。
  final bool subSliderEnable;

  /// 次级进度条高度。默认 3.0
  final double subSliderHeight;

  /// 进度条进度部分颜色
  final Color activeColor;

  /// 进度条背景色
  final Color inactiveColor;

  /// 次级进度条的进度部分颜色
  final Color? subSliderActiveColor;

  /// 次级进度条背景色
  final Color? subSliderInactiveColor;

  /// 进度条上的滑块宽
  final double thumbWidth;

  /// 进度条上的滑块高
  final double thumbHeight;

  /// 进度条上的滑块颜色
  final Color thumbColor;

  /// 当前面板是否应该显示。
  /// 若 [subSliderEnable] 为 true。当该值为 false 时，会显示次级进度条
  bool _showPanel = false;

  /// 面板的字体颜色
  final Color? fontColor;

  /// 面板的字体大小
  final double? fontSize;

  /// 面板的背景颜色
  final Color? backgroundColor;

  /// 面板内间距
  final EdgeInsets? padding;

  /// 操作按钮。
  /// 操作按钮会按顺序出现在操作面板右侧，与进度条一起垂直居中排列
  final List<Widget>? actions;

  DefaultSliderPanelWidget({
    Key? key,
    this.sliderHeight = 3.0,
    this.thumbWidth = 10.0,
    this.thumbHeight = 10.0,
    this.activeColor = const Color(0xffffc900),
    this.inactiveColor = const Color(0xffe0e0e0),
    this.thumbColor = const Color(0xFFFFFFFF),
    this.fontColor = const Color(0xFFFFFFFF),
    this.backgroundColor,
    this.subSliderHeight = 3.0,
    this.subSliderEnable = true,
    this.fontSize = 12.0,
    this.padding,
    this.subSliderActiveColor,
    this.subSliderInactiveColor,
    this.actions,
  }) : super(key: key);

  @override
  _DefaultSliderPanelWidgetState createState() =>
      _DefaultSliderPanelWidgetState();
}

class _DefaultSliderPanelWidgetState extends State<DefaultSliderPanelWidget> {
  double get curPosition => widget.controller?.position ?? 0.0;

  double get duration => widget.controller?.duration ?? 0.0;

  double get progress {
    return duration == 0.0 ? 0.0 : curPosition / duration;
  }

  VoidCallback? listener;
  bool onTouch = false;

  // IFPlayerStatus? curStatus;
  double dx = 0.0;
  double cacheCurPosition = 0;
  double sliderWidth = 0.0;

  GlobalKey key = GlobalKey();

  @override
  void initState() {
    listener = () {
      if (!onTouch) {
        if (mounted && curPosition != cacheCurPosition) {
          cacheCurPosition = curPosition;
          setState(() {
            dx = sliderWidth * progress;
          });
        }
      }
    };
    widget.controller?.addProgressListener(listener!);
    super.initState();
  }

  @override
  void dispose() {
    if (listener != null) {
      widget.controller?.removeProgressListener(listener!);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    WidgetsBinding.instance.addPostFrameCallback((Duration timeStamp) {
      final double temp =
          ((key.currentContext?.findRenderObject()?.constraints ??
                  BoxConstraints()) as BoxConstraints)
              .maxWidth;
      if (sliderWidth != null &&
          sliderWidth != temp &&
          temp != null &&
          temp != double.infinity) {
        setState(() {
          sliderWidth = temp;
          dx = sliderWidth * progress;
        });
      }
    });
    final List<Widget> rightPart = <Widget>[];
    rightPart.addAll(<Widget>[
      const SizedBox(width: 9.0),
      // 右侧总时间
      Text(
        formatTime(duration),
        style: TextStyle(
            color: widget.fontColor,
            fontSize: widget.fontSize,
            height: 1.0,
            decoration: TextDecoration.none),
      ),
    ]);
    if (widget.actions != null) {
      rightPart.add(const SizedBox(width: 9.0));
      rightPart.addAll(widget.actions!);
    }
    return widget._showPanel
        ? Container(
            width: widget.playerWidth,
            color: widget.backgroundColor?.withOpacity(0.5),
            padding: widget.padding,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                      // 左侧当前时间
                      Text(
                        formatTime(curPosition),
                        style: TextStyle(
                            color: widget.fontColor,
                            fontSize: widget.fontSize,
                            decoration: TextDecoration.none,
                            height: 1.0),
                      ),
                      const SizedBox(width: 9),
                      // 中间滑动进度条
                      Expanded(
                        key: key,
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTapDown: (_) {
                            onTouch = true;
                          },
//                          onVerticalDragUpdate: (_){
////                            print("onVerticalDragUpdate");
//                          },
//                          onHorizontalDragUpdate: (_){
////                            print("onHorizontalDragUpdate");
//                          },
                          onHorizontalDragUpdate: (DragUpdateDetails details) {
                            onTouch = true;
                            setState(() {
                              dx = min(
                                  max(dx + details.delta.dx, 0.0), sliderWidth);
                            });
                          },
                          onHorizontalDragEnd: (_) {
                            onTouch = false;
                            if (widget.controller?._hasLoaded() ?? false) {
                              seek();
                            }
                          },
                          child: SizedBox(
                            height: widget.thumbHeight + 9.0 + 9.0,
                            child: Stack(
                              alignment: Alignment.center,
                              clipBehavior: Clip.none,
                              children: <Widget>[
                                Container(
                                  height: widget.sliderHeight,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.all(
                                        Radius.circular(
                                            widget.sliderHeight / 2.0)),
                                    color: widget.inactiveColor,
                                  ),
                                ),
                                Positioned(
                                  left: 0.0,
                                  child: Container(
                                    width: dx,
                                    height: widget.sliderHeight,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(
                                              widget.sliderHeight / 2.0)),
                                      color: widget.activeColor,
                                    ),
                                  ),
                                ),
                                Positioned(
                                  left: dx - widget.thumbWidth / 2.0,
                                  child: Container(
                                    width: widget.thumbWidth,
                                    height: widget.thumbHeight,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(
                                              widget.thumbHeight / 2.0)),
                                      color: widget.thumbColor,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      ...rightPart
              ],
            ),
          )
        : (widget.subSliderEnable
            ? SizedBox(
                width: widget.playerWidth,
                child: Stack(
                  alignment: Alignment.bottomLeft,
                  children: <Widget>[
                    Container(
                      width: widget.playerWidth,
                      height: widget.subSliderHeight,
                      color:
                          widget.subSliderInactiveColor ?? widget.inactiveColor,
                    ),
                    Container(
                      width: (widget.playerWidth ?? 0) * progress,
                      height: widget.subSliderHeight,
                      color: widget.subSliderActiveColor ?? widget.activeColor,
                    ),
                  ],
                ),
              )
            : SizedBox());
  }

  void seek() {
    final int position = (dx / sliderWidth * duration).toInt();
    widget.controller?.seekTo(position);
  }

  String formatTime(double seconds) {
    if (Platform.isIOS) {
      final int totalSeconds = seconds.round();
      final int minutes = totalSeconds ~/ 60;
      final int remainingSeconds = totalSeconds % 60;

      final String min = minutes.toString().padLeft(2, '0');
      final String sec = remainingSeconds.toString().padLeft(2, '0');

      return '$min:$sec';
    } else {
      final double milliseconds = seconds;
      String min = (milliseconds ~/ (1000 * 60)).toString();
      String second = (milliseconds % (1000 * 60) ~/ 1000).toString();
      if (min.length < 2) {
        min = '0$min';
      }
      if (second.length < 2) {
        second = '0$second';
      }
      return '$min:$second';
    }
  }
}

/// 圆角。
///
/// corner
class FPlayerCorner {
  final double leftTopCorner;
  final double rightTopCorner;
  final double rightBottomCorner;
  final double leftBottomCorner;

  /// 指定每一个圆角的大小
  ///
  /// Specify the size of each rounded corner
  const FPlayerCorner({
    this.leftTopCorner = 0,
    this.rightTopCorner = 0,
    this.rightBottomCorner = 0,
    this.leftBottomCorner = 0,
  });

  /// 设置所有圆角为一个大小
  ///
  /// Set all rounded corners to one size
  FPlayerCorner.all(double radius)
      : leftTopCorner = radius,
        rightTopCorner = radius,
        rightBottomCorner = radius,
        leftBottomCorner = radius;
}

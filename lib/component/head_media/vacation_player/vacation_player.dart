import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'vacation_fplayer.dart';
import 'vacation_innerplayer.dart';

enum UserEvent { PAUSE, PLAY }

class FliggyVacationPlayer extends StatefulWidget {
  /// 控制器
  final FliggyVacationPlayerController controller;

  /// 宽
  final double width;

  /// 高
  final double height;

  /// 视频 URL
  final String url;

  /// 是否允许循环播放。默认 false。
  final bool needLoop;

  /// 首次是否静音。默认 true。
  bool muted = true;

  final String? coverUrl;

  bool hideControl = false;

  bool pauseButtonEnable = true;

//  VoidCallback listener;

  final Function(UserEvent)? eventListener;

  bool disposeRelease = true;

  bool autoPlay = false;

  final BoxFit? fitMode;

  double progressBottomMargin = 0;

  Widget? pauseWidget;
  Widget? coverWidget;

  Color? backgroundColor;

  Widget? customFullscreen;

  int hideControlTime = 0;

  Widget? onPlayWidget;

  bool subSliderEnable = false;

  String? scene;

  /// 视频播放完成后否展示coverImg
  bool loadShowCoverImg = false;

  FliggyVacationPlayer({
    Key? key,
    required this.controller,
    required this.url,
    this.coverUrl,
    this.fitMode,
    this.progressBottomMargin = 0,
    this.width = 0,
    this.height = 0,
    this.needLoop = false,
    this.muted = true,
    this.hideControl = false,
    this.eventListener,
    this.pauseButtonEnable = true,
    this.disposeRelease = true,
    this.autoPlay = false,
    this.pauseWidget,
    this.coverWidget,
    this.customFullscreen,
    this.backgroundColor,
    this.hideControlTime = 3,
    this.onPlayWidget,
    this.subSliderEnable = false,
    this.scene,
    this.loadShowCoverImg = false,
  })  : assert(controller != null && url != null),
        super(key: key);

  @override
  _VideoState createState() {
    return _VideoState();
  }
}

class _VideoState extends State<FliggyVacationPlayer> {
  bool fullscreen = false;
  double rotate = 0.0;

  double width = 360.0;
  double height = 300.0;

  double maxWidth = 0;
  double maxHeight = 0;

  OverlayEntry? mOverlayEntry;

  @override
  void initState() {
    super.initState();
    width = widget.width;
    height = widget.height;

    if (widget.muted != null) {
      widget.controller.muted = widget.muted;
    }

    widget.controller.addBackListener(() {
      if (mOverlayEntry != null) {
        print('_onWillPopRemove');
        mOverlayEntry?.remove();
        mOverlayEntry = null;
      }
    });
  }

  @override
  void didUpdateWidget(FliggyVacationPlayer oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  /// 添加播放进度监听
  void addFullScreenListener(Function(bool) listener) {}

  OverlayEntry getOverlayPlayer() {
    maxWidth = MediaQuery.of(context).size.width;
    maxHeight = MediaQuery.of(context).size.height;

    double topPadding = MediaQuery.of(context).padding.top;
    print('topPadding: $topPadding');
    if (topPadding <= 24) {
      topPadding = 0;
    }

    final OverlayEntry overlayEntry = OverlayEntry(
        opaque: true,
        maintainState: false,
        builder: (BuildContext context) {
          return UnconstrainedBox(
            child: RotatedBox(
              quarterTurns: 1,
              child: FliggyVacationPlayerInner(
                controller: widget.controller,
                pauseWidget: widget.pauseWidget,
                url: widget.url,
                scene: widget.scene,
                fitMode: widget.fitMode ?? BoxFit.fill,
                muted: widget.controller.muted,
                coverUrl: widget.coverUrl,
                width: maxHeight - topPadding,
                height: maxWidth,
                autoPlay: widget.autoPlay,
                hideControlTime: widget.hideControlTime ?? 3,
                needLoop: widget.needLoop,
                hideControl: widget.hideControl,
                disposeRelease: false,
                pauseButtonEnable: widget.pauseButtonEnable,
                bottomMargin: 0,
                onPlayWidget: widget.onPlayWidget,
                loadShowCoverImg: widget.loadShowCoverImg,
                listener: (bool state) {
                  print('full screen: $state');
                  if (state) {
                    mOverlayEntry = getOverlayPlayer();
                    Overlay.of(context).insert(mOverlayEntry!);
                  } else {
                    if (mOverlayEntry != null) {
                      mOverlayEntry!.remove();
                      mOverlayEntry = null;
                      // widget.controller.fullscreen = false;
                    }
                    SystemChrome.restoreSystemUIOverlays();
                  }
                },
              ),
            ),
          );
        });
    return overlayEntry;
  }

  @override
  void dispose() {
    if (mOverlayEntry != null) {
      mOverlayEntry!.remove();
      mOverlayEntry = null;
      // widget.controller.fullscreen = false;
    }
    widget.controller?.removeBackListener();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FliggyVacationPlayerInner(
        controller: widget.controller,
        pauseWidget: widget.pauseWidget,
        bottomMargin: widget.progressBottomMargin,
        fitMode: widget.fitMode ?? BoxFit.fill,
        url: widget.url,
        scene: widget.scene,
        muted: widget.controller.muted,
        coverUrl: widget.coverUrl,
        width: widget.width,
        height: widget.height,
        coverWidget: widget.coverWidget,
        needLoop: widget.needLoop,
        autoPlay: widget.autoPlay,
        eventListener: widget.eventListener,
        hideControl: widget.hideControl,
        disposeRelease: widget.disposeRelease,
        pauseButtonEnable: widget.pauseButtonEnable,
        backgroundColor: widget.backgroundColor,
        customFullscreen: widget.customFullscreen,
        hideControlTime: widget.hideControlTime ?? 3,
        onPlayWidget: widget.onPlayWidget,
        subSliderEnable: widget.subSliderEnable,
        loadShowCoverImg: widget.loadShowCoverImg,
        listener: (bool state) {
          // print("full screen: $state");
          if (state) {
            const SystemUiOverlayStyle systemUiOverlayStyle =
                SystemUiOverlayStyle(statusBarColor: Color(0x00FFFFFF));
//            SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
//            SystemChrome.setEnabledSystemUIOverlays([SystemUiOverlay.bottom]);
            SystemChrome.setEnabledSystemUIOverlays(
                <SystemUiOverlay>[SystemUiOverlay.bottom]);
            mOverlayEntry = getOverlayPlayer();
            Overlay.of(context).insert(mOverlayEntry!);
          } else {
            if (mOverlayEntry != null) {
              mOverlayEntry!.remove();
              mOverlayEntry = null;
              // widget.controller.fullscreen = false;
            }
          }
        });
  }
}

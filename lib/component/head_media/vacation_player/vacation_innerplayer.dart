import 'dart:async';
import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';

import 'vacation_fplayer.dart';
import 'vacation_player.dart';

class FliggyVacationPlayerInner extends StatefulWidget {
  /// 控制器
  final FliggyVacationPlayerController controller;

  /// 宽
  final double width;

  /// 高
  final double height;

  /// 视频 URL
  final String url;

  /// 是否允许循环播放。默认 false。
  final bool needLoop;

  /// 首次是否静音。默认 true。
  bool muted = true;

  String? coverUrl;

  bool hideControl = false;

  bool pauseButtonEnable = true;

//  VoidCallback listener;

  Function(bool)? listener;

  Function(UserEvent)? eventListener;

  bool disposeRelease = true;

  bool autoPlay = false;

  BoxFit fitMode;

  double? bottomMargin;

  Widget? pauseWidget;

  Widget? coverWidget;

  Color? backgroundColor;

  Widget? customFullscreen;

  int hideControlTime = 0;

  Widget? onPlayWidget;

  bool? subSliderEnable;

  String? scene;

  /// 视频播放完成后是否展示coverImg
  bool loadShowCoverImg = false;

  FliggyVacationPlayerInner({
    Key? key,
    required this.controller,
    required this.url,
    this.coverUrl,
    this.width = 100.0,
    this.height = 100.0,
    this.needLoop = false,
    this.muted = true,
    this.hideControl = false,
    this.listener,
    this.pauseButtonEnable = true,
    this.eventListener,
    this.disposeRelease = true,
    this.autoPlay = false,
    this.fitMode = BoxFit.fill,
    this.bottomMargin = 0,
    this.pauseWidget,
    this.coverWidget,
    this.backgroundColor,
    this.customFullscreen,
    this.hideControlTime = 0,
    this.onPlayWidget,
    this.subSliderEnable,
    this.scene,
    this.loadShowCoverImg = false,
  }) : super(key: key);

  @override
  _VideoState createState() {
    return _VideoState();
  }
}

class _VideoState extends State<FliggyVacationPlayerInner> {
  double rotate = 0.0;

  double width = 360.0;
  double height = 300.0;

  double? maxWidth;
  double? maxHeight;

  Timer? hideControlTime;

  bool hideProgress = true;

  bool hidePauseButton = false;

  bool muted = false;

  FliggyVacationPlayerController? mPlayerControl;

  bool pauseButtonEnable = true;

  VoidCallback? endListener;

  @override
  void initState() {
    super.initState();
    width = widget.width;
    height = widget.height;
    final MediaQueryData mediaQueryData = MediaQueryData.fromWindow(window);
    maxWidth = mediaQueryData.size.width * mediaQueryData.devicePixelRatio;
    maxHeight = mediaQueryData.size.height * mediaQueryData.devicePixelRatio;
    hideProgress = widget.hideControl;
    muted = widget.muted;
    mPlayerControl = widget.controller;
    if (widget.pauseButtonEnable != null) {
      pauseButtonEnable = widget.pauseButtonEnable;
    }
    // _timeHide();
    widget.controller.addStatusListener(endListener = () {
      print('controllerState[$hashCode]: ${widget.controller.status}');
      if (widget.controller.status == FPlayerStatus.VideoComplete) {
        /// 播放完成，显示播放按钮
        if (pauseButtonEnable) {
          widget.controller.showPauseButton(true);
        }
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    if (endListener != null) {
      widget.controller.removeStatusListener(endListener!);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      child: FPlayer(
        // pauseButtonEnable: !hidePauseButton ||
        //     (widget.controller.status != FPlayerStatus.Playing),
        pauseButtonEnable: widget.pauseButtonEnable,
        controller: widget.controller,
        hidePanelTime: (widget.hideControlTime ?? 0) * 1000,
        url: widget.url,
        scene: widget.scene ?? '',
        width: width,
        height: height,
        autoPlay: widget.autoPlay,
        eventListener: widget.eventListener,
        onPauseWidget: widget.pauseWidget,
        muted: widget.muted,
        fitMode: widget.fitMode,
        needLoop: widget.needLoop,
        disposeRelease: widget.disposeRelease,
        bottomMargin: widget.bottomMargin ?? 0,
        onPlayWidget: widget.onPlayWidget,
        loadShowCoverImg: widget.loadShowCoverImg,
        coverWidget: widget.coverWidget ??
            FRoundImage.network(
              widget.coverUrl ?? '',
              fit: BoxFit.cover,
              width: width,
              height: height,
            ),
        // loadingWidget: const CupertinoActivityIndicator(
        //   radius: 20.0,
        // ),
        panelBuilder: hideProgress
            ? null
            : (_, SetStateBlock _setState) {
                // 进度条
                return DefaultSliderPanelWidget(
                  subSliderEnable: widget.subSliderEnable ?? true,
                  backgroundColor: widget.backgroundColor ?? const Color(0xFF0F131A),
                  activeColor: const Color(0xffFF401A),
                  inactiveColor: const Color(0xffCCCCCC),
                  actions: <Widget>[
                    SizedBox(width: 5.0),
                    /// 静音按钮
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          widget.muted = !widget.muted;
                          // if (currIndex < videos.length) {
                          //   videos[currIndex].fPlayerController?.muted = isMuted;
                          // }
                        });
                      },
                      child: SizedBox(
                        width: 26,
                        height: 26,
                        child: Image.network(
                          !widget.muted ? 'https://gw.alicdn.com/imgextra/i2/O1CN01tMkutW1ss4G87D0w4_!!6000000005821-2-tps-120-120.png'
                              :'https://gw.alicdn.com/imgextra/i2/O1CN01Fw37rw26XpNNw7g2x_!!6000000007672-2-tps-120-120.png',
                          width: 17,
                        ),
                      ),
                    ),
                  ],
                  padding: const EdgeInsets.only(left: 24, right: 12),
                );
              },
      ),
      // onPointerDown: (PointerDownEvent event) => _timeHide(),
    );
  }
}

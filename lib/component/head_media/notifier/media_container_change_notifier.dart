
import 'dart:convert';

import 'package:fbridge/fbridge.dart';
import 'package:fjson_exports/fjson_exports.dart';
import '../../../custom_widget/dialog_webview.dart';
import '../../../data/item_detail_model.dart';
import '../../../render/component/component_change_notifier.dart';
import '../../../render/component/component_context.dart';
import '../../../utils/safe_access.dart';
import 'package:flutter/material.dart';
import 'package:fliggy_router/fliggy_router.dart';

import '../model/head_media/head_media_group_model.dart';
import '../model/head_media/head_media_model.dart';
import '../model/head_media/head_media_process_model.dart';
import '../model/price/price_model.dart';
import 'media_container_controller.dart';
import 'dart:io';
class HeadMediaProcessChangeNotifier extends ComponentChangeNotifier {
  HeadMediaProcessChangeNotifier(ComponentContext context) : super(context);

  /// 打开预览页面
  // 桥有一个 bug,传 index 并不会找 photos 里 index 是指定的那个,必须得手动找是第几张图片
  void openImgBrowse (BuildContext context, String url) {
    final List<Map<String, dynamic>> browseList = <Map<String, dynamic>>[];
    int index = 0;
    int catchIndex = 0;
    final FliggyVacationMediaModel? model = itemDetailModel.headMediaModel;
    if (model == null) {
      return;
    }
    for (int i = 0; i < model.mediaData.length; i++) {
      final FliggyVacationHeadMediaModel mediaModel = model.mediaData[i];
      if (mediaModel is FliggyVacationHeadImageModel) {
        browseList.add(<String, dynamic>{'url' : mediaModel.imgUrl});
        if (url == mediaModel.imgUrl) {
          index = catchIndex;
        } else {
          catchIndex ++;
        }
      }
    }

    if (Platform.isIOS) {
      FliggyNavigatorApi.getInstance().push(context,
          'page://flutter_view/fliggy_item_detail/image_preview_animal_page?_fli_background_transparent=true&_fli_inner_router_=true&flutter_path=/fliggy_item_detail/image_preview_page',
          params: <String, dynamic>{
            'photos': jsonEncode(browseList),
            'initIndex': index.toString(),
            'ratio': 1,
            // '_fli_inner_router':'android'
          },
          anim: Anim.none,
          animeFunc: (BuildContext context,
              Animation<double> animation,
              Animation<double> secondaryAnimation,
              Widget child) {
            final Animation<double> fadeInAnimation = Tween<double>(
              begin: 1.0,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
            ));
            return FadeTransition(
              opacity: fadeInAnimation,
              child: child,
            );
          }
          )
          .then((dynamic value) {
        scrollerTOIndex(safeNonNullInt(value['index']));
      });
    } else {
      FliggyNavigatorApi.getInstance().push(context,
          'page://flutter_view/fliggy_item_detail/image_preview_animal_page?_fli_background_transparent=true&_fli_inner_router_=true&flutter_path=/fliggy_item_detail/image_preview_page',
          params: <String, dynamic>{
            'photos': jsonEncode(browseList),
            'initIndex': index.toString(),
            'ratio': 1,
            // '_fli_inner_router':'android'
          },
          anim: Anim.fade,
          animeFunc: (BuildContext context,
              Animation<double> animation,
              Animation<double> secondaryAnimation,
              Widget child) {
            final Animation<double> fadeInAnimation = Tween<double>(
              begin: 1.0,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
            ));
            return FadeTransition(
              opacity: fadeInAnimation,
              child: child,
            );
          }
      )
          .then((dynamic value) {
        scrollerTOIndex(safeNonNullInt(value['index']));
      });
    }
  }

  void scrollerTOIndex (int index) {
    // 这里给的是图片列表里的index，但是要跳转的是整个列表的index，尤其要注意前面有视频和其他tab的情况
    final FliggyVacationHeadMediaModel? mediaModel = itemDetailModel.headMediaModel?.mediaData[0];
    if (mediaModel?.mediaType == HeadMediaType.video) {
      index ++;
    }
    itemDetailModel.headMediaModel?.headMediaPageController.animateToPage(index, duration: Duration(microseconds: 200), curve: Curves.easeInOut);
  }


  // 打开 F3F4 事件
  void openF3F4Pop(BuildContext context) {
    final String url =
        'https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/coupon?itemId=${itemDetailEngine.itemId}';
    if (url.isNotEmpty) {
      final DialogWebView dialogWebView =
      DialogWebView(context: context, url: url,itemDetailEngine: itemDetailEngine);
      dialogWebView.showPop();
    }
  }

  /// 预售的跳转预售规则
  void jumpRuleTextLink(BuildContext context, String url) {
    // onTap="@handleFevent{'jumpUrlEvent',@concat{@data{fields.spm}, '.jumpRule.1'},'pre_sell_rule',@const{},@const{},@subdata{ruleTextLink}}"

    FliggyNavigatorApi.getInstance().push(context, url, anim: Anim.slide);
  }

  ///TODO brandInfo 的点击事件
  void shoPop(BuildContext context){

  }

  /// 价格的点击事件-活动说明
  void showPop(BuildContext context,String url){
    if(url.isNotEmpty){
      final DialogWebView dialogWebView = DialogWebView(context: context, url: url,itemDetailEngine: itemDetailEngine);
      dialogWebView.showPop();
    }
  }
}
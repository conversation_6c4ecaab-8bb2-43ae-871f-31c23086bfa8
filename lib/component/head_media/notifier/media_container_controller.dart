import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fphoto_view/preload_page_view.dart';

import '../../../custom_widget/count_down/count_down_timer_manager.dart';
import '../../../custom_widget/count_down/count_down_timer_model.dart';
import '../../../data/net/request_type.dart';
import '../../../utils/safe_access.dart';
import '../model/head_media/head_media_group_model.dart';
import '../model/head_media/head_media_model.dart';
import '../model/head_media/head_media_process_model.dart';
import '../model/price/price_model.dart';
import 'media_container_change_notifier.dart';
import 'package:provider/provider.dart';

/// 头图所有数据保存,在页面初始化时创建
class FliggyVacationMediaModel {
  FliggyVacationMediaModel();

  ///媒体容器默认高度
   ValueNotifier<double> mediaDefaultHeight = ValueNotifier<double>(250);

   ValueNotifier<double> mediaDefaultWidth = ValueNotifier<double>(250);

  double viewportFraction = 250/375;
  /// 滚动控制器
  PreloadPageController headMediaPageController = PreloadPageController(initialPage: 2);

  /// 当前组名
  String currentGroupName = '';

  /// 当前在整个媒体列表的位置,因为是以整个媒体列表进行展示的
  ValueNotifier<int> allMediasPos = ValueNotifier<int>(0);

  /// 当前tag位置
  ValueNotifier<int> tagPos = ValueNotifier<int>(0);

  /// 指示器信息
  ValueNotifier<GroupInfo> groupInfo = ValueNotifier<GroupInfo>(GroupInfo(location: 0, name: '', totalNum: -1,));

  /// 指示器是否展示
  ValueNotifier<bool> groupEnable = ValueNotifier<bool>(true);

  // 商详和黑灯页共享的数据,主要包括横划组件部分
  List<FliggyVacationHeadMediaModel> mediaData = <FliggyVacationHeadMediaModel>[];

  /// 价格展示
  late FliggyVacationPriceModel priceModel;

  /// 头图主图用于渲染的数据,让数据和 UI 分离
  HeadMediaProcessModel headMediaProcessModel = HeadMediaProcessModel();

  /// 倒计时
  CountDownTimerModel? countDownTimerModel;

  factory FliggyVacationMediaModel.fromJson(Map<String, dynamic> globalData) {
    final FliggyVacationMediaModel mediaModel = FliggyVacationMediaModel();
    // 初始化头图
    mediaModel.initHeadMediaFromJson(globalData);

    // 初始化价格
    mediaModel.priceModel = FliggyVacationPriceModel.fromJson(globalData);

    // 初始化资源
    mediaModel.initialize();

    return mediaModel;
  }
  /// 初始化头图
  void initHeadMediaFromJson(Map<String, dynamic> globalData) {
    // 初始化整个组
    Map<String, dynamic> frameGroups = <String, dynamic>{};
    if (globalData['headMedia'] is Map) {
      frameGroups = SafeAccess.safeParseMap(globalData['headMedia']);
    } else {
      frameGroups = SafeAccess.safeParseMap(globalData['frameGroup']);
    }

    headMediaProcessModel = HeadMediaProcessModel.fromVacationJson(
      // dataModel 里是所有数据
        frameGroups,
        globalData);

    // 初始化媒体列表
    mediaData = <FliggyVacationHeadMediaModel>[];
    for (final FliggyVacationHeadMediaGroupModel groupModel
        in headMediaProcessModel.frameGroups) {
      mediaData.addAll(groupModel.mediaList);
    }

    // 初始化初始 tag
    currentGroupName = mediaInGroupName(allMediasPos.value).name;
    // 初始化监听
    allMediasPos.addListener(() {
      tagPos.value = headMediaProcessModel.tagList
          .indexOf(mediaInGroupName(allMediasPos.value).name);
    });

  }

  /// 初始化资源
  void initialize() {
    headMediaPageController = PreloadPageController();
    allMediasPos.value = 0;// 初始在第二张
    tagPos.value = 0;//  = ValueNotifier<int>(0);
  }

  /// 初始化倒计时,由 widget 进行初始化,强要求 context
  CountDownTimerModel initCountDown(BuildContext context) {
    if (countDownTimerModel != null) {
      return countDownTimerModel!;
    }
    // 初始化倒计时
    countDownTimerModel = CountDownTimerModel(
      id: 'headMedia',
      currentTime: 1721899714000,
      futureTime: 1721899714005,
      onFinished: () {
        try {
          if (context.mounted) {
            final HeadMediaProcessChangeNotifier changeNotifier = Provider.of<HeadMediaProcessChangeNotifier>(context,listen: false);
            changeNotifier.itemDetailEngine.doRequest(RequestType.first);
            if (countDownTimerModel!.remainingTime.value! <= 0) {
              CountdownTimerManager().unregisterTimer(countDownTimerModel!.id);
              changeNotifier.itemDetailEngine.pageCache.remove('headMediaCountDown');
            }
          }

        } catch (e) {

        }

      },
    );

    return countDownTimerModel!;
  }

  /// 当前位置在哪个组
  GroupInfo mediaInGroupName(int mediaPos) {
    // 补齐位置和长度的差
    mediaPos++;
    for (final FliggyVacationHeadMediaGroupModel group
        in headMediaProcessModel.frameGroups) {
      if (mediaPos > group.mediaList.length) {
        mediaPos = mediaPos - group.mediaList.length;
      } else {
        if (group.showIndicator) {
          groupInfo.value = GroupInfo(location: mediaPos, totalNum: group.mediaList.length, name: group.groupName);
          return groupInfo.value;
        } else {
          groupInfo.value = GroupInfo(location: mediaPos, name: group.groupName, totalNum: -1,);
          return groupInfo.value;
        }
      }
    }

    return GroupInfo(location: 0, name: '', totalNum: 0,);
  }

  /// 销毁单例
  void dispose() {
    headMediaPageController.dispose();
    countDownTimerModel?.unregisterTimer();
  }
}

class GroupInfo {

  /// 当前位置
  late final int location;

  /// 当前总数
  late final int totalNum;

  /// 组名称
  late final String name;


  GroupInfo({required this.location, required this.totalNum, required this.name});
}


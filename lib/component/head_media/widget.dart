import 'package:fbroadcast/fbroadcast.dart';

import 'head_media_container/head_media_container_overlay.dart';
import 'widget/progress_indicator.dart';

import '../../custom_widget/count_down/count_down_widget.dart';
import '../component_key_constant.dart';
import '../titlebar/model.dart';
import 'notifier/media_container_change_notifier.dart';
import 'widget/tag/tag_child_widget.dart';

import 'notifier/media_container_controller.dart';
import 'notifier/media_container_data_notifier.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'head_media_container/head_media_container.dart';

/// 整个头图的管理类
class FliggyVacationHeadMediaWidget extends StatelessWidget {
  FliggyVacationHeadMediaWidget({Key? key}) : super(key: key);

  final ValueNotifier<int> currentIndex = ValueNotifier<int>(0);
  late HeadMediaProcessChangeNotifier changeNotifier;
  late FliggyVacationMediaModel? mediaModel;
  late BuildContext mContext;

  @override
  Widget build(BuildContext context) {
    changeNotifier = Provider.of<HeadMediaProcessChangeNotifier>(context);
    mContext = context;
    final FliggyVacationMediaModel? mediaModel =
        changeNotifier.itemDetailModel.headMediaModel;
    if (mediaModel == null) {
      return Container();
    }
    FBroadcast.instance(changeNotifier.uniqueId).registerSingle('overlayPage',
        (dynamic arg, _) {
      if (changeNotifier.itemDetailEngine.isOverlay) {
        final double moveDate = arg['moveDate'] ?? 0;
        final bool isOverlayToTop = arg['isOverlayToTop'] ?? false;
        if (isOverlayToTop) {
          if (mediaModel.mediaDefaultWidth.value == 375 &&
              mediaModel.mediaDefaultHeight.value == 375) {
            return;
          }
          mediaModel.mediaDefaultWidth.value = 375;
          mediaModel.mediaDefaultHeight.value = 375;
        } else {
          mediaModel.mediaDefaultWidth.value += moveDate;
          mediaModel.mediaDefaultHeight.value += moveDate;
        }

        if (mediaModel.mediaDefaultWidth.value > 375) {
          mediaModel.mediaDefaultWidth.value = 375;
        }
        if (mediaModel.mediaDefaultWidth.value < 250) {
          mediaModel.mediaDefaultWidth.value = 250;
        }
        if (mediaModel.mediaDefaultHeight.value > 375) {
          mediaModel.mediaDefaultHeight.value = 375;
        }
        if (mediaModel.mediaDefaultHeight.value < 250) {
          mediaModel.mediaDefaultHeight.value = 250;
        }

        mediaModel.viewportFraction = mediaModel.mediaDefaultWidth.value / 375;
        changeNotifier.notifyListeners();
      }
    });

    // 初始化倒计时
    // CountDownTimerModel countDownTimerModel;
    // if (changeNotifier.itemDetailEngine.pageCache.containsKey('headMediaCountDown')) {
    //   countDownTimerModel = changeNotifier.itemDetailEngine.pageCache['headMediaCountDown'];
    // } else {
    //   countDownTimerModel = mediaModel!.initCountDown(context);
    //   changeNotifier.itemDetailEngine.pageCache['headMediaCountDown'] = countDownTimerModel;
    // }

    return changeNotifier.itemDetailEngine.isOverlay
        ? _buildOverlayWidget(mediaModel, context)
        : SizedBox(
            // 唯一的 key
            key: changeNotifier.itemDetailModel.titleBarModel
                ?.tabGlobalKey[ComponentKeyConstant.headMedia],
            child: Stack(children: <Widget>[
              Column(
                children: <Widget>[
                  // 主图
                  SizedBox(
                    height:
                        FliggyVacationMediaNotifier.mediaDefaultHeight.value,
                    child: const FliggyVacationHeadMediaContainer(),
                  ),

                  // 价格
                  mediaModel!.priceModel.priceWidget(context)
                ],
              ),

              // 倒计时
              if (mediaModel!.countDownTimerModel != null)
                Container(
                  alignment: Alignment.bottomCenter,
                  padding: EdgeInsets.only(
                      top:
                          FliggyVacationMediaNotifier.mediaDefaultHeight.value +
                              25),
                  child: CountDownWidget(
                    timerModel: mediaModel!.countDownTimerModel!,
                  ),
                ),

              // 帧组在帧内指示器
              Container(
                  alignment: Alignment.bottomCenter,
                  padding: EdgeInsets.only(
                      top:
                          FliggyVacationMediaNotifier.mediaDefaultHeight.value -
                              27),
                  child: ValueListenableBuilder<bool>(
                      valueListenable: mediaModel!.groupEnable,
                      builder: (BuildContext context, bool groupEnable,
                          Widget? child) {
                        if (!groupEnable) {
                          return const SizedBox.shrink();
                        }
                        return ValueListenableBuilder<GroupInfo>(
                            valueListenable: mediaModel!.groupInfo,
                            builder: (BuildContext context, GroupInfo groupInfo,
                                Widget? child) {
                              if (groupInfo.totalNum < 2) {
                                return const SizedBox.shrink();
                              }
                              return HeadProgressIndicator(
                                total: groupInfo.totalNum,
                                current: groupInfo.location,
                              ); //Text('${groupInfo.location}/${groupInfo.totalNum}');
                            }); //Text('${groupInfo.location}/${groupInfo.totalNum}');
                      })),

              // 锚点
              if (mediaModel!.headMediaProcessModel.tagList.length > 1)
                Container(
                    alignment: Alignment.bottomCenter,
                    padding: EdgeInsets.only(
                        top: FliggyVacationMediaNotifier
                                .mediaDefaultHeight.value +
                            6),
                    child: ValueListenableBuilder<int>(
                        valueListenable: mediaModel!.allMediasPos,
                        builder: (BuildContext context, int currentIndex,
                            Widget? child) {
                          return HeadMediaTagWidget(
                            tagNameList:
                                mediaModel!.headMediaProcessModel.tagList,
                            currentIndex: mediaModel!.tagPos,
                            mediaList:
                                mediaModel!.headMediaProcessModel.frameGroups,
                            itemClick: _titleItemClickCall,
                          );
                        })),
            ]));
  }

  Widget _buildOverlayWidget(
      FliggyVacationMediaModel model, BuildContext context) {
    return SizedBox(
        // 唯一的 key
        key: changeNotifier.itemDetailModel.titleBarModel
            ?.tabGlobalKey[ComponentKeyConstant.headMedia],
        child: Stack(children: <Widget>[
          Column(
            children: <Widget>[
              // 主图
              SizedBox(
                  height: model.mediaDefaultHeight.value,
                  child: const FliggyVacationHeadMediaContainerOverlay()),

              // 价格
              model.priceModel.priceWidget(context)
            ],
          ),

          // 倒计时
          if (model.countDownTimerModel != null)
            Container(
              alignment: Alignment.bottomCenter,
              padding:
                  EdgeInsets.only(top: model.mediaDefaultHeight.value + 25),
              child: CountDownWidget(
                timerModel: model.countDownTimerModel!,
              ),
            ),

          // 帧组在帧内指示器
          Container(
              alignment: Alignment.bottomCenter,
              padding:
                  EdgeInsets.only(top: model.mediaDefaultHeight.value - 27),
              child: ValueListenableBuilder<bool>(
                  valueListenable: model.groupEnable,
                  builder:
                      (BuildContext context, bool groupEnable, Widget? child) {
                    if (!groupEnable) {
                      return const SizedBox.shrink();
                    }
                    return ValueListenableBuilder<GroupInfo>(
                        valueListenable: model.groupInfo,
                        builder: (BuildContext context, GroupInfo groupInfo,
                            Widget? child) {
                          if (groupInfo.totalNum < 2) {
                            return const SizedBox.shrink();
                          }
                          return HeadProgressIndicator(
                            total: groupInfo.totalNum,
                            current: groupInfo.location,
                          ); //Text('${groupInfo.location}/${groupInfo.totalNum}');
                        }); //Text('${groupInfo.location}/${groupInfo.totalNum}');
                  })),
        ]));
  }

  /// tag 的点击事件,这个 pos 是第几个 tag
  void _titleItemClickCall(int pos, FliggyVacationMediaModel mediaModel) {
    // 点击后:1 找到位置 2 跳过去
    mediaModel!.tagPos.value = pos;

    int index = 0;
    for (int i = 0; i < pos; i++) {
      index +=
          mediaModel!.headMediaProcessModel.frameGroups[i].mediaList.length;
    }

    if (index >= mediaModel!.mediaData.length) {
      index = mediaModel!.mediaData.length;
    }

    final String id =
        mediaModel!.headMediaProcessModel.frameGroups[pos].id ?? '';
    final String spmCD = 'banner.tab_$id';
    changeNotifier
        .ctrlClicked(mContext, spmCD, 'bannerTab', <String, String>{});
    if (mediaModel!.headMediaPageController.hasClients) {
      mediaModel!.headMediaPageController.animateToPage(index,
          duration: const Duration(milliseconds: 200), curve: Curves.easeInOut);
    }
  }
}

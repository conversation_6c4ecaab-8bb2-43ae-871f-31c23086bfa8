import 'package:flutter/material.dart';

/// 指示器
class HeadProgressIndicator extends StatelessWidget {
  final int total;
  final int current;

  const HeadProgressIndicator({
    Key? key,
    required this.total,
    required this.current,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return
      Stack(
        children: <Widget>[
          Align(
            // 这里用 position 不管用
            alignment: Alignment.bottomCenter,
            child: Container(
                height: 27,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: <Color>[
                      // 渐变颜色数组
                      Color(0x00000000), // 开始颜色
                      Color(0x0F000000), // 结束颜色
                    ],
                  ),
                )),
          ),
          Container(
            margin: EdgeInsets.only(top: 20,left: 15),
            width: 345,
            height: 2,
            child: Row(
              children: List<Widget>.generate(total, (int index) {
                return Expanded(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 3),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(1),
                      color: index < current ? Color(0xFFFFFFFF) : Color(0xFFFFFFFF).withOpacity(0.4),
                    ),

                  ),
                );
              }),
            ),
          )
        ],
      )
      ;
  }
}

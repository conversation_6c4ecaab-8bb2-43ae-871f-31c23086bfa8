
import 'dart:ui';

import 'package:fliggy_router/fliggy_router.dart';
import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';
import 'package:provider/provider.dart';

import '../../../../../utils/TextConfig.dart';
import '../../../../../utils/safe_access.dart';
import '../../../model/head_media/head_media_rate_model.dart';
import '../../../notifier/media_container_change_notifier.dart';
import 'radar_chart.dart';


class radarWidget extends StatelessWidget {
  const radarWidget({Key? key, required this.rateModel, required this.detailScore}) : super(key: key);


  final FliggyVacationHeadRateModel rateModel;

  final List<Map<String, dynamic>> detailScore;
  @override
  Widget build(BuildContext context) {
    final HeadMediaProcessChangeNotifier changeNotifier = Provider.of<HeadMediaProcessChangeNotifier>(context);
    final List<ChartModel> radarList = detailScore.map((
        Map<String, dynamic> e) {
      final String title = e['type'] + ' ' + e['score'] + '分';
      final String? desc = e['desc'];
      final double descDouble = SafeAccess.safeParseDouble(e['score']);
      return ChartModel(x: '', title: title, desc: desc, y: descDouble);
    }).toList();
    // TODO: implement build
    return SizedBox(height: 375, width: 375, //color: Colors.blue,
      child:
      Stack(
        children: <Widget>[
          // 高斯模糊背景图
          ImageFiltered(
            imageFilter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: FRoundImage.network(
              rateModel.backageImg, height: 375, width: 375,),
          ),

          // 白框
          Container(
            width: 357.00,
            height: 273.00,
            margin: EdgeInsets.only(top: 93, left: 9),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.00),
                color: Color(0xCCFFFFFF)),

            child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  // 标题
                  Container(
                      width: 339.00,
                      margin: EdgeInsets.only(left: 9,right: 9, bottom: 12),
                      child: Text('用户印象',
                          textAlign: TextAlign.left,
                          style: TextStyle(
                              color: Color.fromARGB(255, 15, 19, 26),
                              fontSize: 18.00,
                              fontWeight: FontWeightExt.bold)
                      )
                  ),

                  Container(
                    decoration: BoxDecoration(
                        color: const Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.circular(3)
                    ),
                    width: 339,
                    height: 180,
                    child: Center(
                      child: SizedBox(
                        // width: MediaQuery.of(context).size.width*0.5,
                        // height: MediaQuery.of(context).size.width*0.5,
                        child: RadarChart(
                          list: radarList,
                          maxValue: 5, //这里最大值可以设置数据里面的最大值
                          radarColor: const Color(0xFFCCCCFF), // 线的颜色
                          dataColor: const Color(0XFF9999FF), // 内层数据颜色
                        ),
                      ),
                    ),
                  ),
                  if (rateModel.jumpUrl.isNotEmpty)
                    Container(
                      height: 37,
                      width: 339,
                      decoration: BoxDecoration(
                          color: const Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.circular(3)
                      ),
                      child: Column(
                        children: <Widget>[
                          // 虚线
                          CustomPaint(
                            size: Size(350, 1),
                            // Specify the width and height as required
                            painter: DashedLinePainter(),
                          ),
                          GestureDetector(
                            onTap: () {
                              changeNotifier.ctrlClicked(context, 'banner.more_rate', 'bannerMoreRate', <String, String>{});
                              FliggyNavigatorApi.getInstance()
                                  .push(context, rateModel.jumpUrl, anim: Anim.slide);
                            },
                            child: Container(
                              height: 36,
                              color: Color(0x00000000),// 很有趣,不设置颜色, 空白区域不能点,设置一个即使半透明,也可以点了
                              padding: EdgeInsets.symmetric(horizontal: 9,
                                  vertical: 12),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: <Widget>[
                                  Text('以上印象来自用户真实评价,查看评价',
                                    style: TextStyle(
                                        fontSize: 12,
                                        color: Color(0xFF0F131A),
                                        height: 1
                                    ),),
                                  // Spacer(),
                                  FRoundImage.network('https://gw.alicdn.com/imgextra/i3/O1CN01QcKl6R221zaePPdAr_!!6000000007061-2-tps-22-36.png',height: 9,width: 5.5,)
                                ],
                              ),
                            ),
                          )

                        ],
                      ),
                    )
                ]
            ),

          )
        ],
      ),);
  }
}


// 画虚线
class DashedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..color = const Color(0xFFD8D8D8)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke
    ;

    const double dashWidth = 5.0;
    const double dashSpace = 5.0;
    double startX = 0.0;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, 0.0),
        Offset(startX + dashWidth, 0.0),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';

/// 雷达图的视图
class ChartModel {
  String x; //横坐标名称
  String title;// 主标题
  String? desc;// 描述
  double y; //纵坐标值
  bool selected;

  ChartModel({required this.x, required this.title, required this.y, this.selected = false, this.desc});
}


class RadarChart extends StatelessWidget {
  final List<ChartModel> list;
  final double maxValue;
  final Color radarColor;
  final Color dataColor;
  final int numSides;

  const RadarChart({
    Key? key,
    required this.list,
    this.maxValue = 5,
    this.radarColor = const Color(0xFF919499),
    this.dataColor = const Color(0xFF919499),
    this.numSides = 4,  // 默认六边形
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: Size(200, 200),  // 你可以调整这个值以拟合你的屏幕大小
      painter: RadarChartPainter(
        numLayers: 5,
        numSides: numSides,
        list: list,
        maxValue: maxValue,
        radarColor: radarColor,
        dataColor: dataColor,
      ),
    );
  }
}



class RadarChartPainter extends CustomPainter {
  final int numLayers;
  final int numSides;
  final List<ChartModel> list;
  final double maxValue;
  final Color radarColor;
  final Color dataColor;
  final int _offsetDy = 40;
  final int _offsetDx = 5;

  RadarChartPainter({
    required this.numLayers,
    required this.numSides,
    required this.list,
    required this.maxValue,
    required this.radarColor,
    required this.dataColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Offset center = Offset(size.width / 2, size.height / 2);
    final double radius = 50; // 半径,控制大小
    final List<Offset> dataPoints = <Offset>[];
    final Paint radarPaint = Paint()
      ..color = radarColor
      ..style = PaintingStyle.stroke;
    // 背景
    final Paint bgPaint = Paint()
      ..color = const Color(0xFFFFFFFF).withOpacity(.4)
      ..style = PaintingStyle.fill;

    // 画网格背景颜色
    for (int layer = 1; layer <= numLayers; layer++) {
      final double layerRadius = radius * (layer / numLayers);
      final Path radarPath = Path();
      for (int i = 0; i < numSides; i++) {
        final double angle = (2 * pi / numSides) * i - (pi / 2);
        final double x = center.dx + layerRadius * cos(angle);
        final double y = center.dy + layerRadius * sin(angle);
        final Offset point = Offset(x, y);
        if (i == 0) {
          radarPath.moveTo(point.dx, point.dy);
        } else {
          radarPath.lineTo(point.dx, point.dy);
        }
      }
      radarPath.close();
      if (layer == numLayers) {
        canvas.drawPath(radarPath, bgPaint);
      }
      canvas.drawPath(radarPath, radarPaint);
    }

    // 画从中心到各顶点的连线
    for (int i = 0; i < numSides; i++) {
      final double angle = (2 * pi / numSides) * i - pi;
      double x = center.dx + radius * cos(angle);
      double y = center.dy + radius * sin(angle);
      canvas.drawLine(center, Offset(x, y), radarPaint);

      // 绘制主标题
      const TextStyle textStyle = TextStyle(fontSize: 10, color: Color(0xFF6666FF), );
      final TextSpan textSpan = TextSpan(text: list[i % numSides].title, style: textStyle);
      final TextPainter textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();

      final double w = textPainter.width;
      Offset off;

      if (x < center.dx ) {
        // 左半部分
        x = x - w * 0.8 ;
      } else if (x == center.dx) {
        if (y == center.dy + radius) {
          // 正下方
          x = x + w * 0.5 ;
        } else if (y == center.dy - radius) {
          // 正上方
          x = x - w * 0.5 ;
        }
      }

      if (y < center.dy) {
        // 上半部分
        y = y- _offsetDy;
      } else if (y == center.dy) {
        y = y- _offsetDy * 0.5;
      }
      off = Offset(x, y);
      if (i == 0) {
        off = Offset(x - w * 0.5, y);
      } else if (i == 1 || i == 2) {
        off = Offset(x + _offsetDx, y);
      } else if (i == numSides - 2 || i == numSides - 1) {
        off = Offset(x - w - _offsetDx, y);
      } else {
        off = Offset(x - w * 0.5, y);
      }
      textPainter.paint(canvas, off);
      // 绘制副标题
      if (list[i % numSides].desc != null && list[i % numSides].desc!.isNotEmpty) {
        const TextStyle subTextStyle = TextStyle(fontSize: 10, color: Color(0xFF919499), height: 1.3);
        final TextSpan subTextSpan = TextSpan(text: list[i % numSides].desc, style: subTextStyle);
        final TextPainter subTextPainter = TextPainter(
          text: subTextSpan,
          textDirection: TextDirection.ltr,
          maxLines: 2,
        );
        subTextPainter.layout(maxWidth: 80);
        final Offset subTextoff = Offset(off.dx, off.dy + 15);

        subTextPainter.paint(canvas, subTextoff);
      }
    }

    // 画数据区域
    final Paint dataPaint = Paint()
      ..color = dataColor
      ..style = PaintingStyle.stroke;

    final Paint dataFillPaint = Paint()
      ..color = dataColor.withOpacity(.3)
      ..style = PaintingStyle.fill;

    final Path dataPath = Path();
    for (int i = 0; i < numSides; i++) {
      final double angle = (2 * pi / numSides) * i - (pi / 2);
      final double value = list[i % numSides].y;  // 取数据
      final double normalizedValue = value / maxValue;
      final double dataRadius = radius * normalizedValue;
      final double x = center.dx + dataRadius * cos(angle);
      final double y = center.dy + dataRadius * sin(angle);
      final Offset point = Offset(x, y);
      if (i == 0) {
        dataPath.moveTo(point.dx, point.dy);
      } else {
        dataPath.lineTo(point.dx, point.dy);
      }
      dataPoints.add(point);
    }
    dataPath.close();
    canvas.drawPath(dataPath, dataFillPaint);
    canvas.drawPath(dataPath, dataPaint);

    // 画数据点
    final Paint dataPointPaint = Paint()..color = dataColor;
    for (final Offset point in dataPoints) {
      canvas.drawCircle(point, 2.5, dataPointPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}


//
// library dash_painter;
//
// import 'dart:ui';

// [step] the length of solid line 每段实线长
// [span] the space of each solid line  每段空格线长
// [pointCount] the point count of dash line  点划线的点数
// [pointWidth] the point width of dash line  点划线的点划长

class DashPainter {
  const DashPainter({
    this.step = 0,
    this.span = 0,
    this.pointCount = 0,
    this.pointWidth,
  });

  final double step;
  final double span;
  final int pointCount;
  final double? pointWidth;

  void paint(Canvas canvas, Path path, Paint paint) {
    final PathMetrics pms = path.computeMetrics();
    final double pointLineLength = pointWidth ?? paint.strokeWidth;
    final double partLength =
        step + span * (pointCount + 1) + pointCount * pointLineLength;

    for (final PathMetric pm in pms) {
      final int count = pm.length ~/ partLength;
      for (int i = 0; i < count; i++) {
        canvas.drawPath(
          pm.extractPath(partLength * i, partLength * i + step), paint,);
        for (int j = 1; j <= pointCount; j++) {
          final double start =
              partLength * i + step + span * j + pointLineLength * (j - 1);
          canvas.drawPath(
            pm.extractPath(start, start + pointLineLength),
            paint,
          );
        }
      }
      final double tail = pm.length % partLength;
      canvas.drawPath(pm.extractPath(pm.length - tail, pm.length), paint);
    }
  }
}


import 'dart:ui';
import 'package:provider/provider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';

import '../../../../custom_widget/preview_pic/preview_pic.dart';
import '../../../../data/router_net/item_detail_preload_helper.dart';
import '../../../../utils/track_utils.dart';
import '../../model/head_media/head_media_model.dart';
import '../../notifier/media_container_change_notifier.dart';
import 'package:fjson_exports/fjson_exports.dart';

class FliggyVacationHeadImageWidget extends StatelessWidget {
  const FliggyVacationHeadImageWidget(
      {Key? key,
      required this.imgModel,
      required this.width,
      required this.height})
      : super(key: key);

  final FliggyVacationHeadImageModel imgModel;
  final double height;
  final double width;

  @override
  Widget build(BuildContext context) {
    // 这个changeNotifier只在点击时用,但是不能点击时才获取,会报没有 listeren
    final HeadMediaProcessChangeNotifier changeNotifier =
        Provider.of<HeadMediaProcessChangeNotifier>(context);
    final String imageUrl = imgModel.imgUrl;
    final String? cacheUrl =
        PreLoadHelper.headPicCache[changeNotifier.itemDetailEngine.itemId];
    if (imgModel.index == 0 && cacheUrl != null) {
      return GestureDetector(
        child: FRoundImage.network(
          imageUrl,
          key:GlobalKey(),
          height: height,
          width: width,
          placeholder: FRoundImage.network(
            cacheUrl,
            width: 375,
            height: 375,
          ),
          fit: BoxFit.fitWidth,
          transitionAnimatedEnable: true,
          //placeholderUrl.isEmpty,//true,
          transitionDuration: const Duration(milliseconds: 200),
        ),
        onTap: () {
          changeNotifier.openImgBrowse(context, imageUrl);
        },
      );
    }
    if(imgModel.index == 0){
      ///加载过一次后图片就进到缓存了
      PreLoadHelper.headPicCache[changeNotifier.itemDetailEngine.itemId!] = imageUrl;
    }
    return GestureDetector(
      child: FRoundImage.network(
        imageUrl,
        height: height,
        width: width,
        placeholder: Container(
          color: const Color(0xFFFFFFFF),
        ),
        fit: BoxFit.fitWidth,
        transitionAnimatedEnable: true,
        //placeholderUrl.isEmpty,//true,
        transitionDuration: const Duration(milliseconds: 200),
      ),
      onTap: () {
        changeNotifier.openImgBrowse(context, imageUrl);
      },
    );
  }
}

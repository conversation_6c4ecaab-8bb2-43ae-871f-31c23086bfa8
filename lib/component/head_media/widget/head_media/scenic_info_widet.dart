//@dart=2.12
import 'dart:ui';
import 'package:provider/provider.dart';
import 'package:fbridge/fbridge.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';
import 'package:fliggy_router/fliggy_router.dart';

import '../../../../custom_widget/dialog_webview.dart';
import '../../../../fliggy_item_detail.dart';
import '../../../../utils/TextConfig.dart';
import '../../component.dart';
import '../../model/head_media/head_media_model.dart';
import '../../model/head_media/head_media_scenic_info_model.dart';
import '../../notifier/media_container_change_notifier.dart';

class FliggyVacationHeadScenicInfoWidget extends StatelessWidget {
  const FliggyVacationHeadScenicInfoWidget({
    Key? key,
    required this.scenicInfoModel,
  }) : super(key: key);

  final FliggyVacationHeadScenicInfoModel scenicInfoModel;

  // String url;
  // int index;
  @override
  Widget build(BuildContext context) {
    // 这个changeNotifier只在点击时用,但是不能点击时才获取,会报没有 listeren
    final HeadMediaProcessChangeNotifier changeNotifier =
        Provider.of<HeadMediaProcessChangeNotifier>(context);
    final String imageUrl = scenicInfoModel.backageImg;
    return Stack(
      children: <Widget>[
        // 主图
        GestureDetector(
          child: FRoundImage.network(
            imageUrl,
            height: 375,
            width: 375,
            fit: BoxFit.cover,
          ),
          onTap: () {
            // todo:这个点击事件需要改,要把这张图加上
            if (scenicInfoModel.showLightOff != null &&
                scenicInfoModel.showLightOff!) {
              // FBridgeApi.newInstance(context).toast('点了一下');
              changeNotifier.openImgBrowse(context, scenicInfoModel.backageImg);
            }
          },
        ),
        scenicWidget(context, changeNotifier)
      ],
    );
  }

  Widget scenicWidget(
      BuildContext context, HeadMediaProcessChangeNotifier changeNotifier) {
    return Align(
      alignment: Alignment.bottomCenter,
      // 限制组件高度只在子组件高度上
      child: IntrinsicHeight(
          child: Container(
              width: 357.00,
              margin: const EdgeInsets.only(bottom: 17),
              child: ClipRRect(
                  borderRadius: BorderRadius.circular(6.00),
                  child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 0.0, sigmaY: 00.0),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 12),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: <Widget>[
                              Text(scenicInfoModel.nameInfo,
                                  textAlign: TextAlign.left,
                                  style: TextStyle(
                                      color: Color(0xFF0F131A),
                                      fontSize: 18.00,
                                      fontWeight: FontWeightExt.bold)),

                              // 简介
                              if (scenicInfoModel.poiUsefulTips != null)
                                GestureDetector(
                                  onTap: () {
                                    if (scenicInfoModel
                                            .poiUsefulTips?.jumpUrl !=
                                        null) {
                                      FliggyNavigatorApi.getInstance().push(
                                          context,
                                          scenicInfoModel
                                              .poiUsefulTips!.jumpUrl!, anim: Anim.slide);
                                    }
                                  },
                                  child: Container(
                                      width: 333.00,
                                      margin: const EdgeInsets.fromLTRB(
                                          0, 8.00, 0, 0),
                                      child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: <Widget>[
                                            Container(
                                              constraints: const BoxConstraints(
                                                  maxWidth: 278),
                                              child: Text(
                                                TextOverflowUtil.toCharacterBreakStr(scenicInfoModel
                                                    .poiUsefulTips!.text),
                                                textAlign: TextAlign.left,
                                                style: const TextStyle(
                                                    color: Color.fromARGB(
                                                        255, 15, 19, 26),
                                                    fontSize: 14.00,
                                                    height: 1),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                            SizedBox(
                                                height: 16.00,
                                                width: 35.50,
                                                child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: <Widget>[
                                                      Text(
                                                          scenicInfoModel
                                                              .poiUsefulTips!
                                                              .title,
                                                          textAlign:
                                                              TextAlign.left,
                                                          style: TextStyle(
                                                              color: const Color(
                                                                  0xFF0F131A),
                                                              fontWeight: FontWeightExt.bold,
                                                              fontSize: 13.00,
                                                              height: 1)),
                                                      FRoundImage.network(
                                                          'https://img.alicdn.com/imgextra/i1/O1CN01QOcHuA1RpKTM2eXEj_!!6000000002160-2-tps-14-24.png',
                                                          fit: BoxFit.fill,
                                                          width: 3.50,
                                                          height: 6.00)
                                                    ]))
                                          ])),
                                ),

                              // 地图
                              if (scenicInfoModel.locationCard != null)
                                GestureDetector(
                                  onTap: () {
                                    if (scenicInfoModel.locationCard?.jumpUrl !=
                                        null) {
                                      FliggyNavigatorApi.getInstance().push(
                                          context,
                                          scenicInfoModel
                                              .locationCard!.jumpUrl!, anim: Anim.slide);
                                    }
                                  },
                                  child: Container(
                                      width: 333.00,
                                      margin: const EdgeInsets.fromLTRB(
                                          0, 7.00, 0, 0),
                                      child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: <Widget>[
                                            Container(
                                                constraints:
                                                    const BoxConstraints(
                                                        maxWidth: 278),
                                                child: Text(
                                                  scenicInfoModel
                                                      .locationCard!.text,
                                                  textAlign: TextAlign.left,
                                                  style: const TextStyle(
                                                      color: Color(0xFF0F131A),
                                                      fontSize: 14.00,
                                                      height: 1),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                )),
                                            SizedBox(
                                                // height: 16.00,
                                                width: 35.50,
                                                child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: <Widget>[
                                                      Text(
                                                          scenicInfoModel
                                                              .locationCard!
                                                              .title,
                                                          textAlign:
                                                              TextAlign.left,
                                                          style: TextStyle(
                                                              color: const Color(
                                                                  0xFF0F131A),
                                                              fontSize: 13.00,
                                                              fontWeight: FontWeightExt.bold,
                                                              height: 1)),
                                                      FRoundImage.network(
                                                          'https://img.alicdn.com/imgextra/i2/O1CN01d1S7ky1WXXA3iLuow_!!6000000002798-2-tps-14-24.png',
                                                          fit: BoxFit.fill,
                                                          width: 3.50,
                                                          height: 6.00)
                                                    ]))
                                          ])),
                                ),
                              // 公告
                              if (scenicInfoModel.poiAnnouncement != null)
                                GestureDetector(
                                  onTap: () {
                                    if (scenicInfoModel
                                            .poiAnnouncement?.jumpUrl !=
                                        null) {
                                      final DialogWebView dialogWebView =
                                          DialogWebView(
                                              context: context,
                                              url: scenicInfoModel
                                                  .poiAnnouncement!.jumpUrl!,
                                              itemDetailEngine: changeNotifier
                                                  .itemDetailEngine);
                                      dialogWebView.showPop();
                                    }
                                  },
                                  child: Container(
                                      width: 333.00,
                                      margin: const EdgeInsets.fromLTRB(
                                          0, 7.00, 0, 0),
                                      child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: <Widget>[
                                            Container(
                                                constraints:
                                                    const BoxConstraints(
                                                        maxWidth: 278),
                                                child: Text(
                                                  scenicInfoModel
                                                      .poiAnnouncement!.text,
                                                  textAlign: TextAlign.left,
                                                  style: const TextStyle(
                                                      color: Color.fromARGB(
                                                          255, 15, 19, 26),
                                                      fontSize: 14.00,
                                                      height: 1),
                                                  maxLines: 1,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                )),
                                            SizedBox(
                                                height: 16.00,
                                                width: 35.50,
                                                child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: <Widget>[
                                                      Text(
                                                        scenicInfoModel
                                                            .poiAnnouncement!
                                                            .title,
                                                        textAlign:
                                                            TextAlign.left,
                                                        style: TextStyle(
                                                            color: const Color(
                                                                0xFF0F131A),
                                                            fontWeight: FontWeightExt.bold,
                                                            fontSize: 13.00,
                                                            height: 1),
                                                        maxLines: 1,
                                                      ),
                                                      FRoundImage.network(
                                                          'https://img.alicdn.com/imgextra/i3/O1CN01bTd8qt1SGLqCgTLo6_!!6000000002219-2-tps-14-24.png',
                                                          fit: BoxFit.fill,
                                                          width: 3.50,
                                                          height: 6.00)
                                                    ]))
                                          ])),
                                )
                            ]),
                      ))),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6.00),
                  color: Color(0xCCFFFFFF)))),
    );
  }
}

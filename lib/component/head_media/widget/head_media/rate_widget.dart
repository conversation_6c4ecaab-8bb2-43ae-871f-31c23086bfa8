import 'dart:ui';

// import 'dash_painter.dart';
// import 'model/charts_model.dart';
import '../../../../utils/safe_access.dart';

import '../../model/head_media/head_media_rate_model.dart';
import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';
import 'chart_widget/radar_chart.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:ficonfont/ficonfont.dart' as FIcon;

import 'chart_widget/radar_widget.dart';

class FliggyHeadRateWidget extends StatelessWidget {
  FliggyHeadRateWidget({Key? key, required this.rateModel,})
      : super(key: key);


  final FliggyVacationHeadRateModel rateModel;



  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> detailScore = SafeAccess
        .convertJsonStringToList(rateModel.detailScore);

    if (detailScore.length > 2) {
      // 大于 2 展示雷达图
      return radarWidget(rateModel: rateModel, detailScore: detailScore, );
    } else {
      return Container(
        // 理论上不展示,在这里做一个兜底
        color: const Color(0xFFFFFFFF),
      );
    }

  }
}


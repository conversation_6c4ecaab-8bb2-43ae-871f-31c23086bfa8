import 'package:fbridge/fbridge.dart';
import 'package:flutter/material.dart';

import '../../../../data/router_net/item_detail_preload_helper.dart';
import '../../../../utils/WIFI_config.dart';
import '../../../../utils/track_utils.dart';
import '../../model/head_media/head_media_model.dart';
import '../../notifier/media_container_change_notifier.dart';
import '../../notifier/media_container_controller.dart';
import '../../vacation_player/vacation_fplayer.dart';
import '../../vacation_player/vacation_player.dart';
import 'package:fround_image/fround_image.dart';
import 'package:provider/provider.dart';
import 'package:fjson_exports/fjson_exports.dart';
import 'package:fliggy_router/fliggy_router.dart';

class FliggyVacationHeadVideoWidget extends StatefulWidget {
  FliggyVacationHeadVideoWidget({
    Key? key,
    required this.videoModel,
    required this.height,
    required this.width,
  }) : super(key: key);
  FliggyVacationHeadVideoModel videoModel;
  double height;
  double width;

  @override
  State<FliggyVacationHeadVideoWidget> createState() =>
      _FliggyVacationHeadVideoWidgetState();
}

class _FliggyVacationHeadVideoWidgetState
    extends State<FliggyVacationHeadVideoWidget> {
  FliggyVacationPlayerController playCtl = FliggyVacationPlayerController();
  final ValueNotifier<bool> _isVideoVisible = ValueNotifier<bool>(false);
  bool autoPlay = true;
  late HeadMediaProcessChangeNotifier changeNotifier;

  @override
  void initState() {
    super.initState();
    //头部视频暂停
    FliggyNavigatorApi.getInstance().addContainerLifeCycleObserver(
        context: context,
        observer: (FliggyContainerLifeCycle state) {
          if (state == FliggyContainerLifeCycle.Background ||
              state == FliggyContainerLifeCycle.Disappear) {
            playCtl.pause();
          } else if (state == FliggyContainerLifeCycle.ReAppear) {
            playCtl.play();
          }
        });
  }

  @override
  Widget build(BuildContext context) {
    changeNotifier = Provider.of<HeadMediaProcessChangeNotifier>(context);
    final FliggyVacationMediaModel? mediaModel =
        changeNotifier.itemDetailModel.headMediaModel;
    mediaModel?.allMediasPos.addListener(() {
      if (!playCtl.isPause()) {
        if (mediaModel?.allMediasPos.value != widget.videoModel.index) {
          playCtl.stop();
        }
      }
    });
    final String thumbImageUrl = widget.videoModel.thumbnailUrl;
    ///加载过一次后图片就进到缓存了
    PreLoadHelper.headPicCache[changeNotifier.itemDetailEngine.itemId!] = thumbImageUrl;
    // final String? cacheUrl =
    //     PreLoadHelper.headPicCache[changeNotifier.itemDetailEngine.itemId];
    // if (cacheUrl != null) {
    //   thumbImageUrl = cacheUrl;
    // }

    /// 如果有浮层，视频不自动播放
    if (mediaModel?.headMediaProcessModel.mainContainerInfo != null) {
      autoPlay = false;
    }

    if (widget.videoModel.index == 0 && autoPlay) {
      if (!changeNotifier.dataModel.containsKey('isCache')) {
        Future<void>.delayed(const Duration(seconds: 2), () {
          getWifiState(context).then((bool isWIFI) {
            if (!isWIFI) {
              return;
            }
            if (mounted && !(widget?.videoModel?.hasPlayed ?? false)) {
              /// 判断是否是国内还是国外
              FBridgeApi.newInstance(context)
                  .callSafe('client_info')
                  .then((Map<dynamic, dynamic> value) {
                if (value.containsKey('regionStatus')) {
                  if (safeNonNullString(value['regionStatus']) == '1') {
                    return;
                  }
                }
                widget?.videoModel?.hasPlayed = true;
                playCtl = FliggyVacationPlayerController();
                final FliggyVacationMediaModel? mediaModel =
                    changeNotifier?.itemDetailModel?.headMediaModel;
                // 第一次默认播放居然监听不到这个事件,有点菜
                if (mounted) {
                  _isVideoVisible.value = true;
                  playCtl.play();
                }
                mediaModel?.groupEnable?.value = false;
              });
            }
          });
        });
      }
    }

    return GestureDetector(
      onTap: _onTap,
      child: ValueListenableBuilder<bool>(
          valueListenable: _isVideoVisible,
          builder: (BuildContext context, bool _, Widget? child) {
            if (_ && autoPlay) {
              return FliggyVacationPlayer(
                pauseButtonEnable: true,
                controller: playCtl!,
                // coverWidget: Container(),
                url: widget.videoModel.videoUrl,
                coverUrl: thumbImageUrl,
                // pauseWidget: _buildPauseWidget(),
                height: widget.height,
                width: widget.width,
                // width: widget.calWidth,
                // height: widget.calWidth * 9 / 16 + 10,
                autoPlay: autoPlay,
                disposeRelease: true,
                fitMode: BoxFit.contain,
                hideControlTime: 2,
                // subSliderEnable: false,
                // onPlayWidget: _buildPlayWidget(),
                scene: '',
                // hideControl: false,
                loadShowCoverImg: true,
                muted: true,
                eventListener: (UserEvent event) {
                  print(event);
                  String spmCD = '';
                  switch (event) {
                    case UserEvent.PLAY:
                      mediaModel?.groupEnable.value = false;
                      spmCD = 'banner.videoplay';
                      break;
                    case UserEvent.PAUSE:
                      //mediaModel?.groupEnable.value = true;
                      spmCD = 'banner.videopause';
                      break;
                  }
                  changeNotifier.ctrlClicked(
                      context, spmCD, 'bannerVideo', <String, String>{});
                },
              );
            } else {
              return Stack(children: <Widget>[
                FRoundImage.network(
                  thumbImageUrl,
                  fit: BoxFit.fitWidth,
                  height: 375,
                  width: 375,
                  placeholder: Container(
                    color: const Color(0xFFFFFFFF),
                  ),
                  transitionAnimatedEnable: true,
                  transitionDuration: const Duration(milliseconds: 200),
                ),
                // 假的播放按钮
                Center(
                  child: FRoundImage.network(
                    'https://gw.alicdn.com/imgextra/i4/O1CN01AKLGAZ1vKaMNH0BxT_!!6000000006154-2-tps-200-200.png',
                    fit: BoxFit.cover,
                    height: 50,
                    width: 50,
                  ),
                ),
              ]);
            }
          }),
    );
    // return videoWdg;
  }

  void _onTap() {
    // 首次播放，会进入此回调
    changeNotifier.ctrlClicked(
        context, 'banner.videoplay', 'bannerVideo', <String, String>{});
    widget.videoModel.hasPlayed = true;
    playCtl = FliggyVacationPlayerController();
    _isVideoVisible.value = true;
    autoPlay = true;
    playCtl.play();

    final FliggyVacationMediaModel? mediaModel =
        changeNotifier.itemDetailModel.headMediaModel;
    mediaModel?.groupEnable.value = false;
  }

  @override
  void dispose() {
    playCtl.stop();
    // widget.currentIndex.removeListener(selectListener);
    // EventBus().off(HotelDetailBusEvents.eventContainerScrolled, scrollEvent);
    super.dispose();
  }
}

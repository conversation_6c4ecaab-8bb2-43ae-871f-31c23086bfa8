import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';
import 'package:provider/provider.dart';
import '../../../../utils/TextConfig.dart';
import '../../../component_key_constant.dart';
import '../../component.dart';
import '../../model/head_media/head_media_sellPoint_model.dart';
import '../../notifier/media_container_change_notifier.dart';

class FliggyHeadSellPointWidget extends StatelessWidget {
  const FliggyHeadSellPointWidget({Key? key, required this.sellPointModel})
      : super(key: key);


  final FliggyVacationHeadSellPointModel sellPointModel;

  @override
  Widget build(BuildContext context) {
    final HeadMediaProcessChangeNotifier changeNotifier = Provider.of<HeadMediaProcessChangeNotifier>(context);
    // TODO: implement build
    return SizedBox(height: 375, width: 375, //color: Colors.blue,
      child:
      Stack(
        children: <Widget>[
          // 高斯模糊背景图
          ImageFiltered(
            imageFilter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: FRoundImage.network(
              sellPointModel.backageImg, height: 375, width: 375,),
          ),

          Container(
            width: 357.00,
            height: 273.00,
            margin: const EdgeInsets.only(top: 93, left: 9),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.00),
                color: const Color(0xCCFFFFFF)),

            child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  // 标题
                  Container(
                      width: 339.00,
                      margin: EdgeInsets.symmetric(horizontal: 9),
                      child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Text(sellPointModel.title,
                                textAlign: TextAlign.left,
                                style: TextStyle(
                                    color: const Color.fromARGB(255, 15, 19, 26),
                                    fontSize: 18.00,
                                    fontWeight: FontWeightExt.bold,
                                  height: 1
                                )
                            ),
                            Spacer(),
                            GestureDetector(
                              onTap: () {
                                changeNotifier.ctrlClicked(context, 'banner.more_point', 'bannerMorePoint', <String, String>{});
                                changeNotifier.scrollToNode(ComponentKeyConstant.richContent);
                              },
                              child: Row(
                                children: <Widget>[
                                  const Text('查看详情',
                                      textAlign: TextAlign.left,
                                      style: TextStyle(color: Color.fromARGB(
                                          255, 15, 19, 26),
                                          fontSize: 12.00,
                                      height: 1)
                                  ),
                                  Padding(padding: EdgeInsets.only(left: 6), child: FRoundImage.network(
                                      'https://img.alicdn.com/imgextra/i3/O1CN017E1i5y1rH8uzzsZAP_!!6000000005605-2-tps-22-36.png',
                                      width: 5.50,
                                      height: 9.00),)

                                ],
                              ),
                            )

                          ]
                      )
                  ),

                  Container(
                      width: 339.00,
                      margin: const EdgeInsets.fromLTRB(0, 12.00, 0, 0),
                      child: Stack(
                        // crossAxisAlignment: CrossAxisAlignment.start,
                        // mainAxisAlignment: MainAxisAlignment.start,
                          children: <Widget>[
                            FRoundImage.network(
                                sellPointModel.imgUrl,
                                width: 339.00,
                                height: 216.00),
                            Positioned(
                                child: Container(
                                    width: 339.00,
                                    child: Column(
                                        crossAxisAlignment: CrossAxisAlignment
                                            .start,
                                        children: <Widget>[SizedBox(
                                            width: 322.00,
                                            child:  Text(sellPointModel.title,
                                                    textAlign: TextAlign
                                                        .left,
                                                    style: const TextStyle(
                                                        color: Color
                                                            .fromARGB(
                                                            255, 255, 255,
                                                            255),
                                                        fontSize: 15.00),
                                                    maxLines: 2,
                                                    overflow: TextOverflow.ellipsis,
                                                  ),

                                        ),
                                          if (sellPointModel.desc != null)
                                            SizedBox(
                                              width: 310,
                                              child:Text(
                                                    sellPointModel.desc!,
                                                    textAlign: TextAlign.left,
                                                    style: const TextStyle(
                                                        color: Color.fromARGB(
                                                            255, 255, 255, 255),
                                                        fontSize: 10.00),
                                                  maxLines: 2,
                                                  overflow: TextOverflow.ellipsis,
                                                ),

                                            )

                                        ]
                                    ),
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(
                                            6.00))
                                ),
                                left: 9.00,
                                bottom: 23.00,
                                width: 339.00
                            )
                          ]
                      )
                  )
                ]
            ),

          )
        ],
      ),);
  }

}
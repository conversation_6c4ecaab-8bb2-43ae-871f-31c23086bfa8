import 'dart:ui';

import 'package:fjson_exports/fjson_exports.dart';
import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';
import 'package:provider/provider.dart';

import '../../../../utils/TextConfig.dart';
import '../../model/head_media/head_media_shop_model.dart';
import 'package:ficonfont/ficonfont.dart' as FIcon;
import 'package:fliggy_router/fliggy_router.dart';

import '../../notifier/media_container_change_notifier.dart';

/// 在商详里的店铺卡片 UI
class FliggyHeadMediaShopWidget extends StatelessWidget {
  const FliggyHeadMediaShopWidget({Key? key, required this.shopModel})
      : super(key: key);

  final FliggyVacationHeadShopModel shopModel;

  @override
  Widget build(BuildContext context) {
    final HeadMediaProcessChangeNotifier changeNotifier = Provider.of<HeadMediaProcessChangeNotifier>(context);
    // TODO: implement build
    return SizedBox(
      height: 375,
      width: 375,
      //color: Colors.blue,
      child: Stack(
        children: <Widget>[
          // 高斯模糊背景图
          ImageFiltered(
            imageFilter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
            child: FRoundImage.network(
              shopModel.backageImg,
              height: 375,
              width: 375,
            ),
          ),

          // 半透明主题
          Container(
            width: 357.00,
            height: 273.00,
            margin: EdgeInsets.only(top: 93, left: 9),
            padding: EdgeInsets.only(left: 9),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.00),
                color: const Color(0xCCFFFFFF)),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                // crossAxisAlignment: CrossAxisAlignment.center,
                // mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  // 标题
                  Container(
                      width: 339.00,
                      margin: const EdgeInsets.symmetric(vertical: 16),
                      alignment: Alignment.topLeft,
                      child: Text('商家服务',
                          textAlign: TextAlign.left,
                          style: TextStyle(
                              color: Color(0xFF0F131A),
                              fontSize: 18.00,
                              fontWeight: FontWeightExt.bold))),

                  // 商家名称+标签
                  Row(
                    children: <Widget>[
                      Text(shopModel.shopName,
                          textAlign: TextAlign.left,
                          style: TextStyle(
                              color: const Color(0xFF0F131A),
                              fontSize: 15.00,
                              fontWeight: FontWeightExt.bold,
                              height: 1)),
                      if (shopModel.shopTags != null)
                        ...shopModel.shopTags!.map((dynamic e) {
                          if (e is Map) {
                            if (e.containsKey('img')) {
                              return FRoundImage.network(e['img']);
                            } else if (e.containsKey('text')) {
                              final String text = e['text'];
                              if (text.isEmpty) {
                                return SizedBox.shrink();
                              }
                              return Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 3, vertical: 1),
                                margin: EdgeInsets.only(left: 6),
                                decoration: BoxDecoration(
                                    color: const Color(0xFF6666FF),
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(3))),
                                child: Text(
                                  e['text'],
                                  style: const TextStyle(
                                      color: Color(0xFFFFFFFF),
                                      fontSize: 10,
                                      height: 1.1),
                                ),
                              );
                            }
                          }
                          return const SizedBox.shrink();
                        }).toList(),
                    ],
                  ),

                  // 商家标签 todo: 这个找不到品了,不透出了,后面看看咋回事,这块没测过
                  if (shopModel.tags != null)
                    SizedBox(
                      child: tagesWidget(context, shopModel, changeNotifier),
                    ),

                  // 商家资质
                  if (shopModel.qualityDesc != null)
                    Padding(padding: EdgeInsets.only(top: 12),child: Row(
                      children: <Widget>[
                        FRoundImage.network(
                          'https://gw.alicdn.com/imgextra/i3/O1CN01DTL13d1f96Z5lMeSf_!!6000000003963-2-tps-33-36.png',
                          height: 11,
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 3),
                          child: Text(shopModel.qualityDesc!,
                              textAlign: TextAlign.left,
                              style: const TextStyle(
                                  color: Color(0xFF0F131A),
                                  fontSize: 10.00,
                                  height: 1)),
                        )
                      ],
                    ),),

                  Expanded(
                      child: Container(
                    // height: 131,
                    width: 339,

                    margin: const EdgeInsets.only(top: 12, bottom: 9),
                    decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment(0, 0.3), // 这里的0.3表示渐变结束于容器高度的30%处
                          colors: <Color>[
                            Color(0xFFEBEBFF),
                            Color(0xFFFFFFFF),
                          ],
                        ),
                      borderRadius: BorderRadius.all(Radius.circular(6.0))
                    ),
                    child: scoreDescWidget(shopModel),
                  ))
                ]),
          )
        ],
      ),
    );
  }

  Widget tagesWidget(
      BuildContext context, FliggyVacationHeadShopModel shopModel, HeadMediaProcessChangeNotifier changeNotifier) {
    return Row(
      children: <Widget>[
        if (shopModel.scoreJumpUrl != null)
          GestureDetector(
            onTap: () {
              changeNotifier.ctrlClicked(context, 'banner.more_shop_rate', 'bannerMoreShopRate', <String, String>{});
              FliggyNavigatorApi.getInstance()
                  .push(context, shopModel.scoreJumpUrl!);
            },
            child: Row(
              children: <Widget>[
                Text(
                  '更多店铺评价',
                  style: const TextStyle(
                      color: Color(0xFF0F131A), fontSize: 12, height: 1),
                ),
                FIcon.Ficon(0xe730, 15, Color(0xFF0F131A))
              ],
            ),
          )
      ],
    );
  }

  // 下评价方框
  Widget scoreDescWidget(FliggyVacationHeadShopModel shopModel) {
    final Map<String, dynamic> scoreDescMap =
        safeNonNullMap(shopModel.scoreDesc, (dynamic e) => e);

    final List<dynamic> scoreDesc =
        safeNonNullList(scoreDescMap['textList'], (dynamic e) => e);

    final String? title = safeString(scoreDescMap['title']);

    // 背景图
    final String backgroundImg = shopModel.bizType != null
        ? 'https://gw.alicdn.com/imgextra/i2/O1CN01u2jypo21Dc0k4xmIM_!!6000000006951-2-tps-678-183.png'
        : '';

    return Stack(
      children: <Widget>[
        if (backgroundImg.isNotEmpty)
          FRoundImage.network(
            backgroundImg,
            height: 91,
            width: 339,
          ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[

            // 金牌商家
            if (title != null && shopModel.bizType != null)
              Padding(
                padding: EdgeInsets.only(top: 9, left: 12),
                child: RichText(
                    text: TextSpan(children: <TextSpan>[
                  TextSpan(
                      text: '${shopModel.bizType}:',
                      style: const TextStyle(
                        color: Color(0xFF805540),
                        fontSize: 12,
                      )),
                  TextSpan(
                      text: title,
                      style: const TextStyle(
                          color: Color(0xFF0F131A), fontSize: 12)),
                ])),
              ),

            // 评分
            Expanded(
              child: Container(
                child: Center(
                    child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: scoreDesc.map((dynamic e) {
                    if (e is Map) {
                      final String score = e['score'];
                      final String title = e['title'];
                      final String desc = safeNonNullString(e['desc']);
                      return SizedBox(
                        height: desc.isNotEmpty ? 60 : 46,
                        child: Column(
                          children: <Widget>[
                            Text(
                              score,
                              style: TextStyle(
                                  color: shopModel.bizType != null
                                      ? const Color(0xFF805540)
                                      : const Color(0xFF6666FF),
                                  fontSize: 21,
                                  fontWeight: FontWeightExt.bold),
                            ),
                            Text(
                              title,
                              style: const TextStyle(
                                color: Color(0xFF0F131A),
                                fontSize: 11,
                              ),
                            ),
                            if (desc.isNotEmpty)
                              Text(
                                desc,
                                style: const TextStyle(
                                  color: Color(0x800F131A),
                                  fontSize: 11,
                                ),
                              )
                          ],
                        ),
                      );
                    }
                    return Container();
                  }).toList(),
                )),
              ),
            )
          ],
        )
      ],
    );
  }
}


import 'dart:io';

import 'package:flutter/material.dart';

import '../../../component.dart';
import '../../../model/price/daily_price_model.dart';
import 'package:provider/provider.dart';

import '../../../notifier/media_container_change_notifier.dart';
import '../../../notifier/media_container_controller.dart';
import '../commonWidget/brand_info_widget.dart';
import '../commonWidget/coupon_Widget.dart';
import '../commonWidget/head_title_other_widget.dart';
import '../commonWidget/main_title.dart';
/// 日常价组件

class FliggyVacationDailePriceWidget extends StatelessWidget {
final FliggyDailyPriceModel priceModel;

  const FliggyVacationDailePriceWidget({Key? key, required this.priceModel}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final HeadMediaProcessChangeNotifier headMediaProcessChangeNotifier = Provider.of<
        HeadMediaProcessChangeNotifier>(context);
    final FliggyVacationMediaModel? mediaModel = headMediaProcessChangeNotifier.itemDetailModel.headMediaModel;


    return Column(
      children: <Widget>[
        Container(
          // 如果有锚点 把位置空出来
          padding: EdgeInsets.only(top: mediaModel!.headMediaProcessModel.tagList.length > 1 ? 36 : 9),
          color: const Color(0xFFF2F3F4),

          child: Align(
            child: Container(
              decoration: const BoxDecoration(
                  color: Color(0xFFFFFFFF),
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(6), topRight: Radius.circular(6))
              ),
              width: 357,
              padding: EdgeInsets.only(left: 12, right: 12, top: 12),

              child:Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  if (priceModel.exchangePrice != null)
                    priceModel.exchangePrice!.getWidget(),
                  if (priceModel.firstLinePrice != null)
                    priceModel.firstLinePrice!.getWidget(),
                  if (priceModel.secondLinePrice != null)
                    Padding(
                      padding: EdgeInsets.only(top: 6),
                      child: priceModel.secondLinePrice!.getWidget(),
                    ),
                  if (priceModel.advancePrice != null)
                    Padding(
                      padding: EdgeInsets.only(top: 6),
                      child: priceModel.advancePrice!.getWidget(),
                    ),
                  if (priceModel.futureCouponPrice != null)
                    priceModel.futureCouponPrice!.getWidget(),

                  if (priceModel.fliggyWholePriceDescs != null)
                    priceModel.fliggyWholePriceDescs!.buildPriceDescList(context),
                  if (priceModel.couponInfoDataModel != null)
                    Padding(
                      padding: EdgeInsets.only(top: Platform.isIOS ? 3 : 0),
                      child: couponWidget(
                          context,
                          priceModel.couponInfoDataModel!,
                          headMediaProcessChangeNotifier,
                          couponHeight: 18),
                    ),
                  if (priceModel.brandInfoDataModel != null ||
                        priceModel.routeData)
                    brandInfoWidget(context, headMediaProcessChangeNotifier,
                        priceModel.brandInfoDataModel,
                        routeDate: priceModel.routeData),
                  if (headMediaProcessChangeNotifier.itemDetailModel.normalTitleDataModel != null)
                    buildTitle(context, headMediaProcessChangeNotifier.itemDetailModel.normalTitleDataModel!, headMediaProcessChangeNotifier)
                ],
              ) ,
            ),
          )
        ),
        HeadTitleOtherWidget(headMediaProcessChangeNotifier.itemDetailEngine),
      ],
    );
  }
}

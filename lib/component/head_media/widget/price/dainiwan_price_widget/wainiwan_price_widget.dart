

import 'package:ficonfont/ficonfont.dart';
import 'package:fjson_exports/fjson_exports.dart';
import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';
import 'package:provider/provider.dart';

import '../../../../../custom_widget/null_widget.dart';
import '../../../../../utils/common_config.dart';
import '../../../../../utils/common_util.dart';
import '../../../../brand_info/model.dart';
import '../../../../coupon_info/model.dart';
import '../../../../new_custom_coupon/model.dart';
import '../../../../normal_title/model.dart';
import '../../../component.dart';
import '../../../model/price/dainiwan_price_model.dart';
import '../../../notifier/media_container_change_notifier.dart';
import '../commonWidget/brand_info_widget.dart';
import '../commonWidget/coupon_Widget.dart';
import '../commonWidget/custom_coupon_widget.dart';
import '../commonWidget/fliggy_whole_price_descs_widget.dart';
import '../commonWidget/head_title_other_widget.dart';
import '../commonWidget/main_title.dart';

/// 带你玩大促氛围
class FliggyVacationDainiwanPriceWidget extends StatelessWidget {
  final FliggyDainiwanPriceModel dainiwanPriceModel;

  const FliggyVacationDainiwanPriceWidget(
      {Key? key, required this.dainiwanPriceModel})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final HeadMediaProcessChangeNotifier headMediaProcessChangeNotifier =
    Provider.of<HeadMediaProcessChangeNotifier>(context);

    return promotionBodyWidget(context, headMediaProcessChangeNotifier);
  }

  // 整个区块
  Widget promotionBodyWidget(BuildContext context,
      HeadMediaProcessChangeNotifier headMediaProcessChangeNotifier) {
    return Stack(
      children: <Widget>[
        // 背景图 + 阴影
        if (dainiwanPriceModel.newBigPromotion['bgIcon2'] != null)
          SizedBox(
            height: 149,
            width: 375,
            child: FRoundImage.network(
              dainiwanPriceModel.newBigPromotion['bgIcon2'],
              height: 149,
              width: 375,
            ),
          ),
        if (dainiwanPriceModel.newBigPromotion['bgIcon2'] != null)
          Container(
            margin: EdgeInsets.only(top: 80),
            // 还有一个 dart 模式,先没做
            height: 69,
            width: 375,
            child: FRoundImage.network(
              'https://gw.alicdn.com/tfs/TB1z_S0vBv0gK0jSZKbXXbK2FXa-750-137.png',
              height: 69,
              width: 375,
            ),
          ),

        // 主体
        Column(
          children: <Widget>[
            priceBody(context, headMediaProcessChangeNotifier),
            HeadTitleOtherWidget(headMediaProcessChangeNotifier.itemDetailEngine),
          ],
        ),
      ],
    );
  }

  // 价格部分主体
  Widget priceBody(BuildContext context,
      HeadMediaProcessChangeNotifier headMediaProcessChangeNotifier) {
    return Container(
      // width: 369,
      padding: EdgeInsets.only(left: pageMarginLeft, top: 40, right: pageMarginRight),
      child: Column(
        children: <Widget>[
          Row(
            children: <Widget>[
              // 左侧价格
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  if (dainiwanPriceModel.exchangePrice != null)
                    dainiwanPriceModel.exchangePrice!.getWidget(),
                  if (dainiwanPriceModel.firstLinePrice != null)
                    dainiwanPriceModel.firstLinePrice!.getWidget(),
                  if (dainiwanPriceModel.secondLinePrice != null)
                    dainiwanPriceModel.secondLinePrice!.getWidget(),
                  if (dainiwanPriceModel.advancePrice != null)
                    dainiwanPriceModel.advancePrice!.getWidget(),
                  if (dainiwanPriceModel.futureCouponPrice != null)
                    dainiwanPriceModel.futureCouponPrice!.getWidget(),
                ],
              ),
              Spacer(),
              if (dainiwanPriceModel.newBigPromotion['logo2'] != null)
                FRoundImage.network(
                  dainiwanPriceModel.newBigPromotion['logo2'],
                  height: 70,
                  width: 111,
                )
              // 右侧倒计时或者标签
            ],
          ),

          // 预售规则/价格说明
          if (dainiwanPriceModel.fliggyWholePriceDescs != null)
            dainiwanPriceModel.fliggyWholePriceDescs!.buildPriceDescList(context),

          // 券
          if (dainiwanPriceModel.couponInfoDataModel != null || dainiwanPriceModel.newCustomCouponDataModel != null)
            Container(
              clipBehavior: Clip.hardEdge,
              decoration: const BoxDecoration(
                  color: Color(0xFFFFFFFF),
                  borderRadius: BorderRadius.all(Radius.circular(6))
              ),
              margin: EdgeInsets.only(bottom: 9),
              child: Column(
                children: <Widget>[
                  if (dainiwanPriceModel.couponInfoDataModel != null)
                    couponWidget(
                        context, dainiwanPriceModel.couponInfoDataModel!,
                        headMediaProcessChangeNotifier),
                  if (dainiwanPriceModel.newCustomCouponDataModel != null)
                    newCustomCouponWidget(context,
                        dainiwanPriceModel!.newCustomCouponDataModel!)
                ],
              )
              ,
            ),

          // 一些因为氛围需要放在大促氛围下的内容
          Container(// 这一层没有的话组件间会有一根淡淡的线,实在找不到在哪,先包一层
            decoration: const BoxDecoration(
                color: Color(0xFFFFFFFF),
                borderRadius: BorderRadius.only(topLeft: Radius.circular(6), topRight: Radius.circular(6))
            ),
            child: Column(
              children: <Widget>[
                if (dainiwanPriceModel.brandInfoDataModel != null)
                  brandInfoWidget(context, headMediaProcessChangeNotifier,
                      dainiwanPriceModel.brandInfoDataModel),
                if (headMediaProcessChangeNotifier.itemDetailModel.normalTitleDataModel != null)
                  buildTitle(context, headMediaProcessChangeNotifier.itemDetailModel.normalTitleDataModel!, headMediaProcessChangeNotifier)
              ],
            ),
          ),

        ],
      ),
    );
  }

}


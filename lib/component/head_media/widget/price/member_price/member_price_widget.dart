import 'dart:io';

import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';
import 'package:provider/provider.dart';

import '../../../../../utils/TextConfig.dart';
import '../../../../../utils/common_config.dart';
import '../../../model/price/member_price_model.dart';
import '../../../notifier/media_container_change_notifier.dart';
import '../commonWidget/brand_info_widget.dart';
import '../commonWidget/coupon_Widget.dart';
import '../commonWidget/custom_coupon_widget.dart';
import '../commonWidget/fliggy_whole_price_descs_widget.dart';
import '../commonWidget/head_title_other_widget.dart';
import '../commonWidget/main_title.dart';

/// 会员价格

class FliggyVacationMemberPriceWidget extends StatelessWidget {
  final FliggyMemberPriceModel memberPriceModel;

  const FliggyVacationMemberPriceWidget(
      {Key? key, required this.memberPriceModel})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final HeadMediaProcessChangeNotifier headMediaProcessChangeNotifier =
        Provider.of<HeadMediaProcessChangeNotifier>(context);

    return promotionBodyWidget(context, headMediaProcessChangeNotifier);
  }

  // 整个区块
  Widget promotionBodyWidget(BuildContext context,
      HeadMediaProcessChangeNotifier headMediaProcessChangeNotifier) {
    return Stack(
      children: <Widget>[
        // 背景图 + 阴影
        SizedBox(
          height: 149,
          width: 375,
          child: FRoundImage.network(
            memberPriceModel.bgImage ??
                'https://gw.alicdn.com/tfs/TB1PAunXepyVu4jSZFhXXbBpVXa-1500-600.png_450x10000.jpg',
            height: 149,
            width: 375,
          ),
        ),
        Container(
          margin: EdgeInsets.only(top: 90),
          // 还有一个 dart 模式,先没做
          height: 69,
          width: 375,
          child: FRoundImage.network(
            'https://gw.alicdn.com/tfs/TB1z_S0vBv0gK0jSZKbXXbK2FXa-750-137.png',
            height: 69,
            width: 375,
          ),
        ),
        // 主体
        Column(
          children: <Widget>[
            priceBody(context, headMediaProcessChangeNotifier),
            // 这一层没有的话组件间会有一根淡淡的线,实在找不到在哪,先包一层
            Container(padding: EdgeInsets.only(
                left: pageMarginLeft,
                right: pageMarginRight),child: Divider(height: 0,thickness: 0,color: Color(0xffffffff),)),
            HeadTitleOtherWidget(headMediaProcessChangeNotifier.itemDetailEngine),
          ],
        ),
      ],
    );
  }

  // 价格部分主体
  Widget priceBody(BuildContext context,
      HeadMediaProcessChangeNotifier headMediaProcessChangeNotifier) {
    return Container(

      padding: EdgeInsets.only(
          left: pageMarginLeft, top: headMediaProcessChangeNotifier.itemDetailModel.headMediaModel!.headMediaProcessModel.tagList.length > 1 ? 40 : 12,
          right: pageMarginRight),
      child: Column(
        children: <Widget>[
          Row(
            children: <Widget>[
              // 左侧价格
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  if (memberPriceModel.exchangePrice != null)
                    memberPriceModel.exchangePrice!.getWidget(),
                  if (memberPriceModel.firstLinePrice != null)
                    memberPriceModel.firstLinePrice!.getWidget(),
                  if (memberPriceModel.secondLinePrice != null)
                    memberPriceModel.secondLinePrice!.getWidget(),
                  if (memberPriceModel.advancePrice != null)
                    memberPriceModel.advancePrice!.getWidget(),
                  if (memberPriceModel.futureCouponPrice != null)
                    memberPriceModel.futureCouponPrice!.getWidget()
                ],
              ),
              const Spacer(),

              if (memberPriceModel.f3F4MemberPic != null)
                rightLogoWithPic()
              else
                rightLogoWithoutPic(),

              // 右侧倒计时或者标签
            ],
          ),

          // 预售规则/价格说明
          if (memberPriceModel.fliggyWholePriceDescs != null)
            Container(
              margin: const EdgeInsets.only(top: 20),
              padding:
                  const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 9),
              decoration: BoxDecoration(
                  color: Color(0xFFFFFFFF),
                  borderRadius: BorderRadius.circular(6)),
              child: memberPriceModel.fliggyWholePriceDescs!
                  .buildPriceDescList(context),
            ),

          // 一些因为氛围需要放在大促氛围下的内容
          Container(

            // 这一层没有的话组件间会有一根淡淡的线,实在找不到在哪,先包一层
            clipBehavior: Clip.hardEdge,
            decoration: const BoxDecoration(
                color: Color(0xFFFFFFFF),
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(6), topRight: Radius.circular(6))),
            margin: EdgeInsets.only(top: memberPriceModel.fliggyWholePriceDescs != null ? 9 : 20),
            padding: const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 0),
            child: Column(
              children: <Widget>[
                if (memberPriceModel.couponInfoDataModel != null)
                  Padding(
                    padding: EdgeInsets.only(top: 9),
                    child: couponWidget(
                        context,
                        memberPriceModel.couponInfoDataModel!,
                        headMediaProcessChangeNotifier,
                        couponHeight: 18),
                  ),
                if (memberPriceModel.newCustomCouponDataModel != null)
                  newCustomCouponWidget(
                      context, memberPriceModel!.newCustomCouponDataModel!),
                // 品牌心智
                if (memberPriceModel.brandInfoDataModel != null ||
                    memberPriceModel.routeData)
                  brandInfoWidget(context, headMediaProcessChangeNotifier,
                      memberPriceModel.brandInfoDataModel),
                if (headMediaProcessChangeNotifier
                        .itemDetailModel.normalTitleDataModel !=
                    null)
                  buildTitle(
                      context,
                      headMediaProcessChangeNotifier
                          .itemDetailModel.normalTitleDataModel!,
                      headMediaProcessChangeNotifier)
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 价格右侧,有 pic 时展示逻辑
  Widget rightLogoWithPic() {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          if (memberPriceModel.f3F4MemberPic != null &&
              memberPriceModel.f3F4MemberPic!.isNotEmpty)
            Image.network(
              memberPriceModel.f3F4MemberPic!,
              height: 13,
              fit: BoxFit.fitHeight,
            ),
          if (memberPriceModel.f3F4MemberCopyWriting != null &&
              memberPriceModel.f3F4MemberCopyWriting!.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: 6),
              child: Text(
                memberPriceModel.f3F4MemberCopyWriting!,
                style: TextStyle(
                  fontSize: memberPriceModel.name != null ? 10 : 11,
                  fontWeight: FontWeightExt.bold,
                  color: Color(0xFFE9C18B),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          if (memberPriceModel.name != null &&
              memberPriceModel.name!.isNotEmpty)
            Text(
              'x',
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeightExt.bold,
                color: Color(0xFFE9C18B),
              ),
            ),
          if (memberPriceModel.name != null &&
              memberPriceModel.name!.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: 1),
              child: Text(
                memberPriceModel.name!,
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeightExt.bold,
                  color: Color(0xFFE9C18B),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
        ],
      ),
    );
  }

  Widget rightLogoWithoutPic() {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          if (memberPriceModel.sellerName != null &&
              memberPriceModel.sellerName!.isNotEmpty)
            Padding(
              padding: const EdgeInsets.only(top: 2),
              child: Text(
                memberPriceModel.sellerName!,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeightExt.bold,
                  color: const Color(0xFFE9C18B),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          Padding(
            padding: const EdgeInsets.only(top: 1),
            child: Text(
              memberPriceModel.defaultVipStr,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeightExt.bold,
                color: const Color(0xFFE9C18B),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

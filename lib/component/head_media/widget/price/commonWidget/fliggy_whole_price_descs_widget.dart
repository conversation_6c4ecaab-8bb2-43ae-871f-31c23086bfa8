import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';
import 'package:fliggy_router/fliggy_router.dart';
import '../../../../../custom_widget/detail_arrow.dart';
import '../../../../../utils/safe_access.dart';

/// 预售规则
class FliggyWholePriceDescs {
  FliggyWholePriceDescs();
  List<FliggyWholePriceDesc>? fliggyWholePriceDescs;

  factory FliggyWholePriceDescs.fromJson(List<dynamic> json) {
    final FliggyWholePriceDescs fliggyWholePriceDescs = FliggyWholePriceDescs();
    fliggyWholePriceDescs.fliggyWholePriceDescs = <FliggyWholePriceDesc>[];
    for (final dynamic element in json) {
      fliggyWholePriceDescs.fliggyWholePriceDescs!.add(FliggyWholePriceDesc.fromJson(element));
    }
    return fliggyWholePriceDescs;
  }

  Widget buildPriceDescList(BuildContext context) {
    if (fliggyWholePriceDescs == null || fliggyWholePriceDescs!.isEmpty) {
      return SizedBox.shrink();
    }

    return Container(
      // width: 336,
      margin: EdgeInsets.only(top: 3,bottom: 5),
      child: Column(
        children: fliggyWholePriceDescs!.map((FliggyWholePriceDesc item) => _buildPriceDescItem(context, item)).toList(),
      ),
    );
  }

  Widget _buildPriceDescItem(BuildContext context, FliggyWholePriceDesc item) {
    return Container(
      // width: item.ruleText != null ? 336 : null,
      margin: EdgeInsets.only(top: 3),
      padding: item.bgColor != null ? EdgeInsets.symmetric(horizontal: 3,vertical: 9) : null,
      decoration: item.bgColor != null ? BoxDecoration(
        color: SafeAccess.hexColor(item.bgColor),
        borderRadius: BorderRadius.circular(3),
      ) : null,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          if (item.icon != null)
            Container(
              width: 13,
              height: 13,
              margin: EdgeInsets.only(left: 7.5),
              child: FRoundImage.network(item.icon!),
            ),
          if (item.text != null)
            Expanded(
              child: Container(
                margin: EdgeInsets.only(
                  left: (item.icon != null || item.bgColor != null) ? 6 : 0,
                  right: item.bgColor != null ? 3 : 0,
                ),
                child: Text(
                  item.text!,
                  style: TextStyle(
                    fontSize: 13,
                    color: SafeAccess.hexColor(item.textColor ?? '#0F131A'), // Assuming default color if not specified
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          if (item.ruleText != null)
            GestureDetector(
              onTap: () {
                if (item.ruleTextLink != null) {
                  FliggyNavigatorApi.getInstance().push(context, item.ruleTextLink!);
                }
              },
              child: Row(
                children: <Widget>[
                  Text(
                    item.ruleText!,
                    style: TextStyle(
                      fontSize: 13,
                      color: SafeAccess.hexColor(item.ruleTextColor ?? '#0F131A'),
                    ),
                  ),
                  rightArrowBig,
                ],
              ),
            ),
        ],
      ),
    );
  }
}

class FliggyWholePriceDesc {
  FliggyWholePriceDesc();
  /// 背景色
  String? bgColor;

  /// 左边图标
  String? icon;

  /// 左边文案
  String? text;

  String? textColor;

  /// 规则文本
  String? ruleText;

  /// 规则文本颜色
  String? ruleTextColor;

  /// 规则文本链接
  String? ruleTextLink;

  factory FliggyWholePriceDesc.fromJson(Map<dynamic, dynamic> json) {
    final FliggyWholePriceDesc fliggyWholePriceDesc = FliggyWholePriceDesc();
    fliggyWholePriceDesc.bgColor = json['bgColor'];
    fliggyWholePriceDesc.icon = json['icon'];
    fliggyWholePriceDesc.text = json['text'];
    fliggyWholePriceDesc.textColor = json['textColor'];
    fliggyWholePriceDesc.ruleText = json['ruleText'];
    fliggyWholePriceDesc.ruleTextColor = json['ruleTextColor'];
    fliggyWholePriceDesc.ruleTextLink = json['ruleTextLink'];

    return fliggyWholePriceDesc;
  }
}

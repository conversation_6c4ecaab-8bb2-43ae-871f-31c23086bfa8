import 'package:flutter/material.dart';

import '../../../../../custom_widget/detail_arrow.dart';
import '../../../../../custom_widget/null_widget.dart';
import '../../../../../track/exposure_container.dart';
import '../../../../brand_info/model.dart';
import '../../../notifier/media_container_change_notifier.dart';

/// 品牌标
Widget brandInfoWidget(
    BuildContext context,
    HeadMediaProcessChangeNotifier? headMediaProcessChangeNotifier,
    BrandInfoDataModel? brandInfoDataModel,
    {bool? routeDate}) {
  if (routeDate != null && routeDate) {
    return Container(
      margin: const EdgeInsets.fromLTRB(0, 6, 0, 0),
      height: 18,
      color: const Color(0xFFFFFFFF),
      child: Row(
        children: <Widget>[
          Align(
              child: Container(
            height: 18,
            width: 60,
            decoration: BoxDecoration(
                color: Color(0x60FFE033),
                borderRadius: BorderRadius.circular(3)),
          ))
        ],
      ),
    );
  }

  if (headMediaProcessChangeNotifier == null || brandInfoDataModel == null) {
    return nullWidget;
  }
  const String spmCD = 'basicInfo.soldInfo';
  const String controlName = 'basicInfo_soldInfo';

  headMediaProcessChangeNotifier.ctrlExposure(context, spmCD, null);

  return GestureDetector(
    onTap: () {
      if (brandInfoDataModel.showPop ?? false) {
        headMediaProcessChangeNotifier.ctrlClicked(context, spmCD, controlName, null);
        headMediaProcessChangeNotifier.shoPop(context);
      }
    },
    child: Container(
        padding: const EdgeInsets.fromLTRB(0, 9, 0, 0),
        color: Color(0xFFFFFFFF),
        // height: 18.00,
        child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              if (brandInfoDataModel.icon != '')
                Padding(
                  padding: const EdgeInsets.only(bottom: 2),
                  child: Image.network(
                      brandInfoDataModel.icon!,
                      height: 18.00),
                ),
              Expanded(
                child: Container(
                    child: Text(brandInfoDataModel.description!,
                        textAlign: TextAlign.left,
                        style: const TextStyle(
                            color: Color(0xFF919499),
                            fontSize: 11.00,
                            height: 1)),
                    margin: const EdgeInsets.fromLTRB(6.00, 0, 0, 0)),
              ),
              // 右侧已售
              Text(brandInfoDataModel.sold!,
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                      color: Color(0xFF919499), fontSize: 11.00, height: 1)),
              if (brandInfoDataModel.showPop ?? false)
                Container(
                  margin: const EdgeInsets.fromLTRB(6, 0, 0, 0),
                  child: rightArrowSmall,
                ),
            ])),
  );

}

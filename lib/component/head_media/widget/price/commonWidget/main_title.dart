
import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';

import '../../../../../custom_widget/detail_arrow.dart';
import '../../../../../utils/TextConfig.dart';
import '../../../../normal_title/model.dart';
import '../../../component.dart';
import '../../../notifier/media_container_change_notifier.dart';
import 'package:flutter/cupertino.dart';

/// 主标题
Widget buildTitle(
    BuildContext context,
    NormalTitleDataModel normalTitleDataModel,
    HeadMediaProcessChangeNotifier changeNotifier) {
  bool _calculateNeedsExpansion() {
    final TextSpan textSpan = TextSpan(text: normalTitleDataModel.itemTitle, style: TextStyle(
      // height: 1.5,
        fontWeight: FontWeightExt.bold,
        color: const Color.fromARGB(255, 15, 19, 26),
        fontSize: 15.00));
    final TextPainter textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      maxLines: 2,
    );
    textPainter.layout(maxWidth: MediaQuery.of(context).size.width);
    return textPainter.didExceedMaxLines;
  }
  final bool needExp = _calculateNeedsExpansion();
  final ValueNotifier<bool> expanded = ValueNotifier<bool>(false);

  return ValueListenableBuilder<bool>(valueListenable: expanded, builder: (BuildContext context, bool expandedvalue, Widget? child) {
    return Container(
      color: const Color(0xFFFFFFFF),
      width: 375,// 不写无法感知整个宽度,会居中
      padding: const EdgeInsets.only(top: 6),
      child: Stack(
        children: <Widget>[
          Container(
            constraints: BoxConstraints(
              maxWidth: needExp ? 310 : 375,
            ),
            child: RichText(
              maxLines: needExp ? expanded.value ? 10 : 2  : 2,
              overflow: TextOverflow.ellipsis,
              text: TextSpan(
                children: <InlineSpan>[
                  if (normalTitleDataModel.titleIconList!.isNotEmpty)
                    WidgetSpan(
                        child: GestureDetector(
                          onTap: () {
                            if (normalTitleDataModel.subBrand != null && normalTitleDataModel.subBrand!.containsKey('popWindowUrl') && normalTitleDataModel.subBrand?['popWindowUrl'] != null ) {
                              changeNotifier.showPop(context, normalTitleDataModel.subBrand?['popWindowUrl']);
                            }
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: normalTitleDataModel.titleIconList!.map((String url) {
                              return Padding(
                                padding: const EdgeInsets.only(right: 3,bottom: 1),
                                child: FRoundImage.network(
                                  url,
                                  height: 15, // 设置固定高度
                                  fit: BoxFit.contain,
                                  urlResizeEnable: false,
                                ),
                              );
                            }).toList(),
                          ),
                        )),

                  // WidgetSpan(child: Container(
                  //   color: Colors.red,
                  //   child: Text(
                  //      normalTitleDataModel.itemTitle!,
                  //     style: const TextStyle(
                  //       // height: 1.5,
                  //         fontWeight: FontWeightExt.bold,
                  //         color: Color.fromARGB(255, 15, 19, 26),
                  //         fontSize: 16.00)
                  // ),)),
                  TextSpan(
                      text: normalTitleDataModel.itemTitle,
                      style: TextStyle(
                        height: 1.5,
                          fontWeight: FontWeightExt.bold,
                          color: const Color.fromARGB(255, 15, 19, 26),
                          fontSize: 15.00)),
                ],
              ),
            ),
          ),

          if (needExp)
            Positioned(
              top: 30,
                right: 0,
                child: GestureDetector(
              onTap: () {
                expanded.value = !expanded.value;
              },
              child: Container(
                // todo:这个地方后面用统一的箭头
                child: expanded.value ? bottomArrowSmall : topArrowSmall,
                color: const Color(0x00FFFFFF),
                height: 10,
                width: 10,
              ),
            ))

        ],
      ),
    );
  }
  );


}
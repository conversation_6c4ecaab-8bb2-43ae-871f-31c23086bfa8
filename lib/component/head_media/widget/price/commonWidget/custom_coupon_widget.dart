
import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';

import '../../../../../utils/TextConfig.dart';
import '../../../../../utils/common_config.dart';
import '../../../../new_custom_coupon/model.dart';

/// 跨店满减券
Widget newCustomCouponWidget(
    BuildContext context, NewCustomCouponDataModel newCustomCouponDataModel) {
  return Container(
      color: const Color(0xFFFFFFFF),
      padding: const EdgeInsets.fromLTRB(paddingLeft, 4, paddingRight, 0),
      child: Container(
          height: 60.00,

          decoration:  (newCustomCouponDataModel.backgroundUrl != null) ? BoxDecoration(

            image: DecorationImage(
                image: NetworkImage(newCustomCouponDataModel.backgroundUrl!),
                fit: BoxFit.fill),
          ) : const BoxDecoration(),
          child: Container(
            padding: const EdgeInsets.fromLTRB(12, 0, 12, 0),
            child: Row(children: <Widget>[
              Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Container(
                        child: Text('¥',
                            textAlign: TextAlign.left,
                            style: TextStyle(
                                color: newCustomCouponDataModel.textColor,
                                fontSize: 15.00)),
                        margin: const EdgeInsets.fromLTRB(0, 8.00, 0, 0)),
                    Text(newCustomCouponDataModel.couponAmount!,
                        textAlign: TextAlign.left,
                        style: TextStyle(
                            fontWeight: FontWeightExt.bold,
                            color: newCustomCouponDataModel.textColor,
                            fontSize: 25.00))
                  ]),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.fromLTRB(8, 0, 0, 0),
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(newCustomCouponDataModel.title!,
                            textAlign: TextAlign.left,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontWeight: FontWeightExt.bold,
                                color: newCustomCouponDataModel.textColor,
                                fontSize: 15.00)),
                        Container(
                            child: Text(newCustomCouponDataModel.conditions!,
                                textAlign: TextAlign.left,
                                style: TextStyle(
                                    color: newCustomCouponDataModel.textColor,
                                    fontSize: 10.00)),
                            margin: const EdgeInsets.fromLTRB(0, 5, 0, 0))
                      ]),
                ),
              ),
              Container(
                margin: const EdgeInsets.fromLTRB(6, 0, 12, 0),
                child: FRoundImage.network(
                  'https://gw.alicdn.com/tfs/TB1oFSUsKH2gK0jSZJnXXaT1FXa-2-198.png',
                  width: 0.50,
                  height: 46.00,
                ),
              ),
              SizedBox(
                width: 80.00,
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Text(newCustomCouponDataModel.buttonDesc!,
                          textAlign: TextAlign.left,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                              fontWeight: FontWeightExt.bold,
                              color: newCustomCouponDataModel.otherTextColor,
                              fontSize: 14.00)),
                      if (newCustomCouponDataModel.subButtonDesc != '')
                        Container(
                            child: Text(
                                newCustomCouponDataModel.subButtonDesc!,
                                textAlign: TextAlign.left,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    color: newCustomCouponDataModel
                                        .otherTextColor,
                                    fontSize: 9.00)),
                            margin: const EdgeInsets.fromLTRB(0, 4, 0, 0)),
                    ]),
              ),
            ]),
          )));
}

import 'package:flutter/material.dart';

import '../../../../../page/item_detail_engine.dart';
import '../../../../../page/page_structure_manger.dart';
import '../../../../../render/component/component_context.dart';
import '../../../../../utils/common_config.dart';
import '../../../../component_key_constant.dart';

/// <AUTHOR>
/// @date Created on 2024/10/22
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

class HeadTitleOtherWidget extends StatelessWidget {
  HeadTitleOtherWidget(this.itemDetailEngine, {Key? key}) : super(key: key);
  final ItemDetailEngine itemDetailEngine;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Container(
          margin: pageMargin,
          child: itemComponentMap[ComponentKeyConstant.billionService]!
              .generateComponent(ComponentContext(
                  ComponentKeyConstant.billionService, itemDetailEngine)),
        ),
        Container(
          margin: pageMargin,
          child: itemComponentMap[ComponentKeyConstant.normalFactor]!
              .generateComponent(ComponentContext(
                  ComponentKeyConstant.normalFactor, itemDetailEngine)),
        ),
        Container(
          margin: pageMargin,
          child: itemComponentMap[ComponentKeyConstant.zeroSaveBuyRule]!
              .generateComponent(ComponentContext(
                  ComponentKeyConstant.zeroSaveBuyRule, itemDetailEngine)),
        ),
        Container(
          margin: pageMargin,
          child: itemComponentMap[ComponentKeyConstant.rankListInfo]!
              .generateComponent(ComponentContext(
                  ComponentKeyConstant.rankListInfo, itemDetailEngine)),
        ),
        Container(
          margin: pageMargin,
          child: itemComponentMap[ComponentKeyConstant.rateAndShare]!
              .generateComponent(ComponentContext(
                  ComponentKeyConstant.rateAndShare, itemDetailEngine)),
        ),
        Container(
          margin: pageMargin,
          child: itemComponentMap[ComponentKeyConstant.rateAndServer]!
              .generateComponent(ComponentContext(
                  ComponentKeyConstant.rateAndServer, itemDetailEngine)),
        ),
        Container(
          margin: pageMargin,
          child: itemComponentMap[ComponentKeyConstant.bottomRadiusDivider]!
              .generateComponent(ComponentContext(
                  ComponentKeyConstant.bottomRadiusDivider, itemDetailEngine)),
        ),
      ],
    );
  }
}

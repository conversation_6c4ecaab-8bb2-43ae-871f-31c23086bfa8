import 'dart:io';

import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';

import '../../../../../custom_widget/detail_arrow.dart';
import '../../../../../custom_widget/null_widget.dart';
import '../../../../../utils/TextConfig.dart';
import '../../../../../utils/common_util.dart';
import '../../../../coupon_info/model.dart';
import '../../../notifier/media_container_change_notifier.dart';

/// 横排券
Widget couponWidget(
    BuildContext context,
    CouponInfoDataModel couponInfoDataModel,
    HeadMediaProcessChangeNotifier changeNotifier,
    {double? couponHeight, bool isBidPromotion = false}) {

  return GestureDetector(
    onTap: () {
      changeNotifier.ctrlClicked(context, 'basicInfo.couponTag', 'couponInfoTag', <String, String>{});
      changeNotifier.showPop(context, couponInfoDataModel.popupUrl!);
    },
    child: Container(
        color: const Color(0xFFFFFFFF),
        height: isBidPromotion ? 36 : 18,

        child: Row(
            children: <Widget>[
          SizedBox(
            // height: 16.00,
            // width: 300,
            child: ListView.builder(
              padding: EdgeInsets.zero,
              scrollDirection: Axis.horizontal,
              // 设置内边距为0
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: couponInfoDataModel.cells?.length??0,
              itemBuilder: (BuildContext context, int index) {
                final Cells? cells = couponInfoDataModel.cells?[index];
                return cells?.icon != null
                    ? _buildCouponIcon(cells)
                    : _buildCouponText(cells);
              },
            ),
          ),
          const Spacer(),


          // 右侧查看按钮
          if (!isBidPromotion)
          Text(couponInfoDataModel.text!,
              textAlign: TextAlign.left,
              style: TextStyle(
                fontWeight: FontWeightExt.bold,
                  color: Color.fromARGB(255, 15, 19, 26), fontSize: 12.00)),
          if (!isBidPromotion)
          Container(
            margin: const EdgeInsets.fromLTRB(6, 0, 0, 0),
              child: rightArrowSmall,
            )
          else if (couponInfoDataModel.couponGetBg != null)
            Container(
              height: 24,
              width: 55,
              decoration: BoxDecoration(
                image: DecorationImage(
                    image: NetworkImage(couponInfoDataModel.couponGetBg!),
                    fit: BoxFit.fill),
              ),
              child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(couponInfoDataModel.text!,
                      textAlign: TextAlign.left,
                      style: TextStyle(
                          fontWeight: isBidPromotion
                              ? FontWeightExt.normal
                              : FontWeightExt.bold,
                          color: couponInfoDataModel.couponGetColor,
                          fontSize: isBidPromotion ? 13.00 : 12)),
                  Padding(padding: EdgeInsets.only(left: 6),child: rightArrowSmallWhite,),
                ],
              )),
            )
        ])
  ),
  );
}

/// 横排券icon
Widget _buildCouponIcon(Cells? cells) {
  return cells == null
      ? nullWidget
      : Align(
          alignment: Alignment.centerLeft,
          child: Container(
            height: 15,
            margin: const EdgeInsets.fromLTRB(0, 0, 3, 0),
            alignment: Alignment.center,
            decoration: BoxDecoration(
                image: cells.bgImg != null ? DecorationImage(
                    image: NetworkImage(cells.bgImg!), fit: BoxFit.fill) : null,
                borderRadius: BorderRadius.circular(2.0)),
            child: Row(
                children: <Widget>[
                  if (cells.icon != null)
                    FRoundImage.network(
                      cells.icon!,
                      height: 16,
                      fit: BoxFit.fitHeight,
                    ),
                  cells.text != ''
                      ? Container(
                          padding: EdgeInsets.fromLTRB(Platform.isIOS ? 1 : 3, Platform.isIOS ? 2 : 1, 0, 0),
                          child: Text(cells.text ?? '',
                              textAlign: TextAlign.left,
                              style: TextStyle(
                                  color: stringToColor(cells.textColor),
                                  fontSize: 12.00,
                                  height: 1)),
                        )
                      : nullWidget,
                ],
              ),
          ),
        );
}

/// 横排券text
Widget _buildCouponText(Cells? cells) {
  return cells == null ||cells.text == null
      ? nullWidget
      : Align(
          alignment: Alignment.centerLeft,
          child: Container(
            height: 16,
            margin: const EdgeInsets.fromLTRB(0, 0, 3, 0),
            padding: const EdgeInsets.fromLTRB(3, 0, 3, 0),
            decoration: BoxDecoration(
                // color: stringToColor(cells.subText != null && cells.subText != ''
                //     ? cells.bgColor ?? '#ffffffff'
                //     : '#ffffffff'),
                border: Border.all(
                    color: stringToColor(cells.textColor).withOpacity(0.5),
                    width: 0.5),
                borderRadius: BorderRadius.circular(2.0)),
            child: Row(
                children: <Widget>[
                  Text(//'啊电话费iu啊都是分开几哈打撒会计法哈达手机卡发哈',
                      cells.text!,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: stringToColor(cells.textColor),
                          fontSize: 12.00,
                          height: 1.2)),
                  if (cells.subText != null)
                    Container(
                        margin: const EdgeInsets.fromLTRB(3, 0, 3, 0),
                        child: FRoundImage.network(
                          'https://gw.alicdn.com/imgextra/i4/O1CN01MroVVN1HTfa2HFpFD_!!6000000000759-2-tps-2-60.png',
                          height: 15,
                        )),
                  if (cells.subText != null)
                    Container(
                      color: const Color(0xFFFFFFFF),
                      child: Text(cells.subText!,
                          textAlign: TextAlign.left,
                          style: TextStyle(
                              color: stringToColor(cells.textColor),
                              fontSize: 12.00,
                              height: 1)),
                    ),
                ],
              ),
          ),
        );
}


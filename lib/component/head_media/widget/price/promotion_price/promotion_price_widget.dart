import 'package:ficonfont/ficonfont.dart';
import 'package:fjson_exports/fjson_exports.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';
import 'package:provider/provider.dart';

import '../../../../../custom_widget/dialog_flutter.dart';
import '../../../../../custom_widget/null_widget.dart';
import '../../../../../utils/TextConfig.dart';
import '../../../../../utils/common_config.dart';
import '../../../../../utils/safe_access.dart';
import '../../../../brand_info/model.dart';
import '../../../../mind_rule/model.dart';
import '../../../../mind_rule/popWidget/mindRulePop.dart';
import '../../../../new_custom_coupon/model.dart';
import '../../../component.dart';
import '../../../model/price/big_promotion_price_model.dart';
import '../../../notifier/media_container_change_notifier.dart';
import '../../../notifier/media_container_controller.dart';
import '../commonWidget/brand_info_widget.dart';
import '../commonWidget/coupon_Widget.dart';
import '../commonWidget/custom_coupon_widget.dart';
import '../commonWidget/fliggy_whole_price_descs_widget.dart';
import '../commonWidget/head_title_other_widget.dart';
import '../commonWidget/main_title.dart';

/// 大促价格

class FliggyVacationPromotionPriceWidget extends StatelessWidget {
  final FliggyBigPromotionPriceModel bigPromotionPriceModel;

  const FliggyVacationPromotionPriceWidget(
      {Key? key, required this.bigPromotionPriceModel})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final HeadMediaProcessChangeNotifier headMediaProcessChangeNotifier =
        Provider.of<HeadMediaProcessChangeNotifier>(context);
    final List<Widget> widgetList = <Widget>[];

    if (bigPromotionPriceModel.partnerAtmosphere != null) {
      // 非 f3f4 会员
      if (bigPromotionPriceModel.name!=null &&
          bigPromotionPriceModel.f3F4MemberPic==null) {
        headMediaProcessChangeNotifier.ctrlExposure(context, 'partnerAtmosphere.name', null);
        widgetList.add(Stack(
          children: <Widget>[
            SizedBox(
              height: 30,
              width: 375,
              child: FRoundImage.network(
                'https://gw.alicdn.com/tfs/TB1qKIUGrr1gK0jSZR0XXbP8XXa-1500-120.png',
                height: 30,
                width: 375,
              ),
            ),
            Container(
              height: 30,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              constraints: const BoxConstraints(maxWidth: 357),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(
                    bigPromotionPriceModel.partnerAtmosphere!['name'] ?? '',
                    style: const TextStyle(
                      color: Color(0xFFf2c191),
                      fontSize: 14,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            )
          ],
        ));
      } else if (bigPromotionPriceModel.f3F4MemberPic!=null) {
        headMediaProcessChangeNotifier.ctrlExposure(context, 'partnerAtmosphere.f3F4Member', null);
        // f3f4会员
        widgetList.add(GestureDetector(
          onTap: () {
            headMediaProcessChangeNotifier.ctrlClicked(context, 'partnerAtmosphere.f3F4Member', 'partnerAtmosphere.f3F4Member', null);
            if(bigPromotionPriceModel.f3F4MemberDescTitle!=null&&bigPromotionPriceModel.f3F4MemberDesc!=null){
             final MindRulePopupModel rulePopupModel = MindRulePopupModel();
              rulePopupModel.explain = bigPromotionPriceModel.f3F4MemberDesc;
              final Widget contentWidget =
              MindRulePopWidget(rulePopupModel);
              final DialogFlutterView dialogFlutterView =
              DialogFlutterView(
                context: context,
                contentWidget: contentWidget,
                popConfig: FlutterPopConfig(
                    popTitle: bigPromotionPriceModel.f3F4MemberDescTitle,
                    popBtnTitle: '确定',
                    popHeight:
                    MediaQuery.of(context).size.height * 1 / 2),
              );
              dialogFlutterView.showPop();
            }else{
              headMediaProcessChangeNotifier.openF3F4Pop(context);
            }

          },
          child: Container(
            child: Stack(
              children: <Widget>[
                // 背景图
                FRoundImage.network(
                  'https://gw.alicdn.com/tfs/TB1qKIUGrr1gK0jSZR0XXbP8XXa-1500-120.png',
                  height: 30,
                  width: 375,
                ),
                Positioned.fill(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    child: Row(
                      children: <Widget>[
                        // 左侧是 f3f4 标
                        FRoundImage.network(bigPromotionPriceModel.f3F4MemberPic!,
                            height: 13),

                        if (bigPromotionPriceModel.name!=null) // 什么逻辑啊,搞不懂,只能抄
                          Container(
                              padding: const EdgeInsets.only(left: 2),
                              child: Text(
                                '·',
                                style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFFe9c18b),
                                    fontWeight: FontWeightExt.bold),
                              )),

                        if (bigPromotionPriceModel.f3F4MemberCopyWriting!=null)
                          Container(
                              padding: const EdgeInsets.only(left: 2),
                              child: Text(
                                bigPromotionPriceModel
                                    .f3F4MemberCopyWriting!,
                                style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFFe9c18b),
                                    fontWeight: FontWeightExt.bold),
                              )),

                        if (bigPromotionPriceModel.name!=null)
                          Container(
                              padding: const EdgeInsets.only(left: 2),
                              child: Text(
                                'x',
                                style: TextStyle(
                                    fontSize: 14,
                                    color: const Color(0xFFe9c18b),
                                    fontWeight: FontWeightExt.bold),
                              )),

                        if (bigPromotionPriceModel.name!=null)
                          Container(
                              padding: const EdgeInsets.only(left: 2),
                              child: Text(
                                bigPromotionPriceModel.name!,
                                style: TextStyle(
                                    fontSize: 14,
                                    color: const Color(0xFFe9c18b),
                                    fontWeight: FontWeightExt.bold),
                              )),
                        if(bigPromotionPriceModel.f3F4MemberDescIcon!=null)
                        Expanded(child: nullWidget),
                        if(bigPromotionPriceModel.f3F4MemberDescIcon!=null)
                          Container(
                            margin: EdgeInsets.only(left: 10),
                            child: FRoundImage.network(bigPromotionPriceModel.f3F4MemberDescIcon!,
                                height: 13,width: 13,),
                          ),
                      ],
                    ),
                  ),
                )
                //
              ],
            ),
          ),
        ));
      }
    }

    // 主要部分样式
    widgetList
        .add(promotionBodyWidget(context, headMediaProcessChangeNotifier));
    return Column(
      children: widgetList,
    );
  }

  // 整个区块
  Widget promotionBodyWidget(BuildContext context,
      HeadMediaProcessChangeNotifier headMediaProcessChangeNotifier) {
    return Stack(
      children: <Widget>[
        // 背景图 + 阴影

        if (bigPromotionPriceModel.newBigPromotion['bgIcon2'] != null)
          FRoundImage.network(
            bigPromotionPriceModel.newBigPromotion['bgIcon2'],
            height: 149,
            width: 375,
          ),
        if (bigPromotionPriceModel.newBigPromotion['bgIcon2'] != null)
          Container(
            margin: EdgeInsets.only(top: 80),
            // 还有一个 dart 模式,先没做
            height: 69,
            width: 375,
            child: FRoundImage.network(
              'https://gw.alicdn.com/tfs/TB1z_S0vBv0gK0jSZKbXXbK2FXa-750-137.png',
              height: 69,
              width: 375,
            ),
          ),

        // 主体
        Column(
          children: <Widget>[
            priceBody(context, headMediaProcessChangeNotifier),
            HeadTitleOtherWidget(headMediaProcessChangeNotifier.itemDetailEngine),
          ],
        ),
      ],
    );
  }

  // 主体
  Widget priceBody(BuildContext context,
      HeadMediaProcessChangeNotifier headMediaProcessChangeNotifier) {
    final FliggyVacationMediaModel? mediaModel = headMediaProcessChangeNotifier.itemDetailModel.headMediaModel;
    return Container(
      // width: 369,
      padding: EdgeInsets.only(left: pageMarginLeft, top: mediaModel!.headMediaProcessModel.tagList.length > 1 ? 28 : 8, right: pageMarginRight),

      child: Column(
        children: <Widget>[
          Row(
            children: <Widget>[
              // 左侧价格
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  if (bigPromotionPriceModel.exchangePrice != null)
                    bigPromotionPriceModel.exchangePrice!.getWidget(),

                  // 上面大的价格
                  if (bigPromotionPriceModel.firstLinePrice != null)
                    bigPromotionPriceModel.firstLinePrice!.getWidget(),

                  if (bigPromotionPriceModel.secondLinePrice != null)
                    Padding(
                      padding: EdgeInsets.only(top: 5),
                      child:
                          bigPromotionPriceModel.secondLinePrice!.getWidget(),
                    ),
                  if (bigPromotionPriceModel.advancePrice != null)
                    bigPromotionPriceModel.advancePrice!.getWidget(),
                  if (bigPromotionPriceModel.futureCouponPrice != null)
                    bigPromotionPriceModel.futureCouponPrice!.getWidget()
                ],
              ),
              Spacer(),

              // 右侧倒计时或者标签
              if (bigPromotionPriceModel.newBigPromotion['logo2'] != null)
                SizedBox(
                  height: 70,
                  width: 111,
                  child: Stack(
                    children: <Widget>[
                      // 图片
                      if (bigPromotionPriceModel.newBigPromotion['logo2'] != null)
                        FRoundImage.network(
                          bigPromotionPriceModel.newBigPromotion['logo2'],
                          height: 70,
                          width: 111,
                        ),
                      SizedBox(
                        height: 70,
                        width: 111,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: <Widget>[
                            if (bigPromotionPriceModel.newBigPromotion['logoTexts'] !=
                                null)
                              Container(
                                constraints: BoxConstraints(maxWidth: 101),
                                padding: EdgeInsets.only(bottom: 3),
                                child: Text(
                                  safeNonNullList(
                                      bigPromotionPriceModel
                                          .newBigPromotion['logoTexts'],
                                      (dynamic e) => e)[0],
                                  //bigPromotionPriceModel.newBigPromotion[logo2]
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: bigPromotionPriceModel
                                                .newBigPromotion['logoTextColor'] !=
                                            null
                                        ? SafeAccess.hexColor(bigPromotionPriceModel
                                            .newBigPromotion['logoTextColor'])
                                        : Color(0xFFFFFFFF),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            if (safeNonNullList(
                                    bigPromotionPriceModel.newBigPromotion['logoTexts'],
                                    (dynamic e) => e).length >
                                1)
                              Container(
                                padding: EdgeInsets.only(bottom: 8),
                                child: Text(
                                  safeNonNullList(
                                      bigPromotionPriceModel
                                          .newBigPromotion['logoTexts'],
                                      (dynamic e) => e)[1],
                                  //bigPromotionPriceModel.newBigPromotion[logo2]
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: bigPromotionPriceModel
                                                .newBigPromotion['logoTextColor'] !=
                                            null
                                        ? SafeAccess.hexColor(bigPromotionPriceModel
                                            .newBigPromotion['logoTextColor'])
                                        : Color(0xFFFFFFFF),
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )

              // 右侧倒计时或者标签
            ],
          ),

          if (bigPromotionPriceModel.futureCouponPrice != null)
            SizedBox(
              height: 10,
            ),
          // 预售规则/价格说明
          if (bigPromotionPriceModel.fliggyWholePriceDescs != null)
            bigPromotionPriceModel.fliggyWholePriceDescs!.buildPriceDescList(context),

          // 券
          if (bigPromotionPriceModel.couponInfoDataModel != null || bigPromotionPriceModel.newCustomCouponDataModel != null)
            Container(
              clipBehavior: Clip.hardEdge,
              decoration: const BoxDecoration(
                  color: Color(0xFFFFFFFF),
                  borderRadius: BorderRadius.all(Radius.circular(6))
              ),
              padding: const EdgeInsets.fromLTRB(paddingLeft, 0, 0, 0),
              margin: EdgeInsets.only(bottom: 9),
              child: Column(
                children: <Widget>[
                  if (bigPromotionPriceModel.couponInfoDataModel != null)
                    couponWidget(
                        context, bigPromotionPriceModel.couponInfoDataModel!,
                        headMediaProcessChangeNotifier,isBidPromotion: true),
                  if (bigPromotionPriceModel.newCustomCouponDataModel != null)
                    newCustomCouponWidget(context,
                        bigPromotionPriceModel!.newCustomCouponDataModel!)
                ],
              ),
            ),
          // 一些因为氛围需要放在大促氛围下的内容
          Container(// 这一层没有的话组件间会有一根淡淡的线,实在找不到在哪,先包一层
            clipBehavior: Clip.hardEdge,
            decoration: const BoxDecoration(
                color: Color(0xFFFFFFFF),
                borderRadius: BorderRadius.only(topLeft: Radius.circular(6), topRight: Radius.circular(6))
            ),
            padding: const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 0),
            child: Column(
              children: <Widget>[
                // 品牌心智
                if (bigPromotionPriceModel.brandInfoDataModel != null || bigPromotionPriceModel.routeData)
                  brandInfoWidget(context, headMediaProcessChangeNotifier,
                      bigPromotionPriceModel.brandInfoDataModel, routeDate: bigPromotionPriceModel.routeData),
                if (headMediaProcessChangeNotifier.itemDetailModel.normalTitleDataModel != null)
                  buildTitle(context, headMediaProcessChangeNotifier.itemDetailModel.normalTitleDataModel!, headMediaProcessChangeNotifier)
              ],
            ),
          ),



        ],
      ),
    );
  }

}

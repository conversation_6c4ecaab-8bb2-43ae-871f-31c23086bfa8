
import 'dart:io';

import '../../model/head_media/head_media_group_model.dart';
import '../../notifier/media_container_controller.dart';
import 'tag_widget_controller.dart';
import 'package:flutter/material.dart';

/// 2024 新版头图的新版 tag,继承自FliggyVacationTagListController, 只负责页面展示
class HeadMediaTagWidget extends FliggyVacationTagListController {
  const HeadMediaTagWidget({
    Key? key,
    required List<String> tagNameList,
    ValueNotifier<int>? currentIndex,
    List<FliggyVacationHeadMediaGroupModel>? mediaList,
    void Function(int, FliggyVacationMediaModel)? itemClick,
    int? totalMediaCount,
    Function? action,
  })  : _tagNameList = tagNameList,
        _itemClick = itemClick,
        _currentIndex = currentIndex,
        _mediaList = mediaList,
        _totalMediaCount = totalMediaCount,
        _action = action,
        super(key: key, tagNameList: tagNameList);

  final List<String> _tagNameList;
  final void Function(int, FliggyVacationMediaModel)? _itemClick;
  final ValueNotifier<int>? _currentIndex;
  final List<FliggyVacationHeadMediaGroupModel>? _mediaList;
  final int? _totalMediaCount;
  final Function? _action;

  @override
  List<String> get tagNameList => _tagNameList;

  @override
  void Function(int, FliggyVacationMediaModel)? get itemClick => _itemClick;

  @override
  ValueNotifier<int>? get currentIndex => _currentIndex;

  @override
  List<FliggyVacationHeadMediaGroupModel>? get mediaList => _mediaList;

  @override
  int? get totalMediaCount => _totalMediaCount;

  @override
  Function? get action => _action;

  @override
  Widget buildChild(String tagName, bool isSelected) {
    return Container(
        height: 20,
        padding: EdgeInsets.only(left: 9, right: 9,top: Platform.isIOS ? 3.0 : 0.0),
        // margin: EdgeInsets.only(),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFFE033) : const Color(0x00000000),
          borderRadius: const BorderRadius.all(Radius.circular(10.0)),
        ),
        alignment: Alignment.bottomCenter,
        child: Center(
            child: Text(
          tagName,
          style: TextStyle(
              color: isSelected
                  ? const Color(0xFF0F131A)
                  : const Color(0xFFFFFFFF),
              fontSize: 11,
              fontWeight: FontWeight.w500,
              height: 1),
        )));
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';

import '../../model/head_media/head_media_group_model.dart';
import '../../notifier/media_container_change_notifier.dart';
import '../../notifier/media_container_controller.dart';

/// tag 的总 controller, 目标是只处理逻辑, 布局在继承他的子 widget 里
class FliggyVacationTagListController extends StatefulWidget {
  const FliggyVacationTagListController(
      {Key? key,
      this.tagNameList,
      this.currentIndex,
      this.mediaList,
      this.itemClick,
      this.totalMediaCount,
      this.action})
      : super(key: key);
  final List<String>? tagNameList;
  final void Function(int, FliggyVacationMediaModel)? itemClick;
  final ValueNotifier<int>? currentIndex;
  final List<FliggyVacationHeadMediaGroupModel>? mediaList;
  final int? totalMediaCount;
  final Function? action;

  @override
  State<FliggyVacationTagListController> createState() =>
      _FliggyVacationTagListControllerState();

  // 定义一个抽象方法，子类需要实现该方法来返回不同的子组件
  Widget buildChild(String tagName, bool isSelected) {
    throw UnimplementedError();
  }
}

class _FliggyVacationTagListControllerState
    extends State<FliggyVacationTagListController> {
  int curItem = -1;

  late final HeadMediaProcessChangeNotifier changeNotifier;

  @override
  Widget build(BuildContext context) {
    final HeadMediaProcessChangeNotifier changeNotifier = Provider.of<HeadMediaProcessChangeNotifier>(context);
    final FliggyVacationMediaModel? mediaModel = changeNotifier.itemDetailModel.headMediaModel;
    if (mediaModel == null) {
      return Container();
    }
    return
      IntrinsicWidth(
      // alignment: Alignment.center,
      child: Container(
          alignment: Alignment.center,
          height: 24,
          // width: 300,
          padding: const EdgeInsets.symmetric(horizontal: 2,vertical: 2),
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(12),color: const Color(0x40000000),),
          child:
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: widget.tagNameList!
                .asMap()
                .entries
                .map((MapEntry<int, String> entry) {
              final int pos = entry.key;
              final String tagName = widget.tagNameList![pos]!;
              final bool isSelected = mediaModel.tagPos.value == pos;
              final Map<String, String?> params = <String, String?>{};
              params['index'] = pos.toString();
              params['text'] = tagName;

              return IntrinsicHeight(
                  child: Align(
                      alignment: Alignment.bottomCenter,
                      child: GestureDetector(
                          onTap: () {
                            curItem = pos;
                            widget.itemClick!(pos, mediaModel);
                          },
                          child: widget.buildChild(tagName, isSelected))));
            }).toList(),
          )
      ),
    );
  }

  /// 检查一下当前选中状态
  void checkSelectIndex() {
    if (curItem != FliggyVacationMediaModel().tagPos.value) {
      curItem = FliggyVacationMediaModel().tagPos.value;
    }
  }
}

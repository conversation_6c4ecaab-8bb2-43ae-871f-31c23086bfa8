import 'dart:io';

import 'package:fbridge/fbridge.dart';
import 'package:flutter/material.dart';
import 'package:fphoto_view/photo_view_gallery.dart';
import 'package:fphoto_view/preload_page_view.dart';
import 'package:provider/provider.dart';
import 'package:fliggy_router/fliggy_router.dart';

import '../../../custom_widget/null_widget.dart';
import '../../../data/router_net/item_detail_preload_helper.dart';
import '../../../page/overlay_page_manager.dart';
import '../../../utils/TextConfig.dart';
import '../component.dart';
import '../model/head_media/head_media_model.dart';
import '../model/head_media/head_media_rate_model.dart';
import '../model/head_media/head_media_scenic_info_model.dart';
import '../model/head_media/head_media_sellPoint_model.dart';
import '../model/head_media/head_media_shop_model.dart';
import '../notifier/media_container_change_notifier.dart';
import '../notifier/media_container_controller.dart';
import '../notifier/media_container_data_notifier.dart';
import '../widget/head_media/img_widget.dart';
import '../widget/head_media/rate_widget.dart';
import '../widget/head_media/scenic_info_widet.dart';
import '../widget/head_media/sell_point_widget.dart';
import '../widget/head_media/shop_widget.dart';
import '../widget/head_media/video_widget.dart';
import 'package:fround_image/fround_image.dart';

/// 商详头图容器,不包括锚点,包括进度条 常驻层
class FliggyVacationHeadMediaContainerOverlay extends StatefulWidget {
  const FliggyVacationHeadMediaContainerOverlay({Key? key}) : super(key: key);

  @override
  State<FliggyVacationHeadMediaContainerOverlay> createState() =>
      _FliggyVacationHeadMediaContainerOverlayState();
}

class _FliggyVacationHeadMediaContainerOverlayState
    extends State<FliggyVacationHeadMediaContainerOverlay> {
  List<FliggyVacationHeadMediaModel> mediaDataList =
      <FliggyVacationHeadMediaModel>[];

  void exposeHeadMedia(
      HeadMediaProcessChangeNotifier changeNotifier, int index) {
    // final FliggyVacationMediaModel? mediaModel =
    //     changeNotifier.itemDetailModel.headMediaModel;
    // if (mediaModel == null) {
    //   return;
    // }
    // final List<FliggyVacationHeadMediaModel> mediaDataArr =
    //     mediaModel.mediaData;
    // if (index >= mediaDataArr.length) {
    //   return;
    // }
    //
    // // 切换头图，曝光图片或者视频
    // final FliggyVacationHeadMediaModel model = mediaDataList[index];
    // String spmCD = 'banner.img';
    // if (model is FliggyVacationHeadVideoModel) {
    //   // 视频
    //   spmCD = 'banner.video';
    // }
    // final GroupInfo info =
    //     changeNotifier.itemDetailModel!.headMediaModel!.groupInfo.value;
    // final Map<String, String> trackParams = <String, String>{};
    // trackParams['tag'] = info.name; // 标签名
    // trackParams['pos'] = '${info.location - 1}'; // 属于该标签下的第几个, 从0开始计数
    // trackParams['index'] = '$index'; // 整个头图中的位置
    //
    // changeNotifier.ctrlExposure(context, spmCD, trackParams);
  }

  @override
  Widget build(BuildContext context) {
    // final HeadMediaProcessChangeNotifier changeNotifier =
    //     Provider.of<HeadMediaProcessChangeNotifier>(context);
    final HeadMediaProcessChangeNotifier changeNotifier =
        Provider.of<HeadMediaProcessChangeNotifier>(context);
    final FliggyVacationMediaModel? mediaModel =
        changeNotifier.itemDetailModel.headMediaModel;
    if (mediaModel == null) {
      return Container();
    }
    mediaModel.headMediaPageController =
        PreloadPageController(initialPage: mediaModel.allMediasPos.value);
    mediaDataList = mediaModel.mediaData;

    // 页面创建时，曝光显示的头图
    exposeHeadMedia(changeNotifier, mediaModel.allMediasPos.value);

    return ClipRRect(
      borderRadius: BorderRadius.only(
          topLeft: Radius.circular(OverlayPageManager.OVERLAY_BORDER_RADIUS),
          topRight: Radius.circular(OverlayPageManager.OVERLAY_BORDER_RADIUS)),
      child: Stack(
        children: <Widget>[
          _horizontalSingleChildScrollView(changeNotifier, context, mediaModel),
          // 头图上半部分蒙层
          Container(
            height: 80,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: <Color>[
                  // 渐变颜色数组
                  Color(0xFF000000), // 开始颜色
                  Color(0x00000000), // 结束颜色
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 横向 SingleChildScrollView
  Widget _horizontalSingleChildScrollView(
      HeadMediaProcessChangeNotifier changeNotifier,
      BuildContext context,
      FliggyVacationMediaModel mediaModel) {
    final List<Widget> listWidgets =
        buildPageViewItemWidgetOverlay(context, mediaModel);
    return NotificationListener(
      onNotification: (ScrollNotification notification) {
        return true;
      },
      child: PageView(
        padEnds: false,
        scrollDirection: Axis.horizontal,
        controller: PageController(
            // initialPage: listWidgets.length > 1 ? 1 : 0,
            viewportFraction: mediaModel.viewportFraction),
        children: listWidgets,
        onPageChanged: (int index) {
          // 切换头图时曝光
          exposeHeadMedia(changeNotifier, index);
          if (!mediaModel.groupEnable.value) {
            mediaModel.groupEnable.value = true;
          }
          // currentIndex.value = index;
          mediaModel.allMediasPos.value = index;
        },
      ),
    );
  }

  List<Widget> buildPageViewItemWidgetOverlay(
      BuildContext context, FliggyVacationMediaModel mediaModel) {
    final List<Widget> listWidget = <Widget>[];
    for (final FliggyVacationHeadMediaModel model in mediaDataList) {
      Widget? renderWidget;

      if (model is FliggyVacationHeadImageModel) {
        renderWidget = RepaintBoundary(
            child: FliggyVacationHeadImageWidget(
          imgModel: model,
          // height: mediaModel.mediaDefaultHeight.value,
          // width: mediaModel.mediaDefaultHeight.value,
          height: 375,
          width: 375,
        ));
      } else if (model is FliggyVacationHeadVideoModel) {
        renderWidget = null;
      } else if (model is FliggyVacationHeadScenicInfoModel) {
        // 景点

        renderWidget = RepaintBoundary(
            child: FliggyVacationHeadScenicInfoWidget(
          scenicInfoModel: model,
        ));
      } else if (model is FliggyVacationHeadSellPointModel) {
        // 商品特色
        renderWidget = RepaintBoundary(
            child: FliggyHeadSellPointWidget(
          sellPointModel: model,
        ));
      } else if (model is FliggyVacationHeadRateModel) {
        // 用户印象
        renderWidget = FliggyHeadRateWidget(
          rateModel: model,
        );
      } else if (model is FliggyVacationHeadShopModel) {
        // 店铺
        renderWidget = FliggyHeadMediaShopWidget(
          shopModel: model,
        );
      } else {
        renderWidget = null;
      }
      if (renderWidget != null) {
        listWidget.add(Align(
            alignment: Alignment.centerLeft,
            child: renderWidget));
      }
    }
    return listWidget;
  }
}

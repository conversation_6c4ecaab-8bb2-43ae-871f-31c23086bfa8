import 'dart:io';

import 'package:fbridge/fbridge.dart';
import 'package:flutter/material.dart';
import 'package:fphoto_view/photo_view_gallery.dart';
import 'package:fphoto_view/preload_page_view.dart';
import 'package:provider/provider.dart';
import 'package:fliggy_router/fliggy_router.dart';

import '../../../custom_widget/null_widget.dart';
import '../../../data/router_net/item_detail_preload_helper.dart';
import '../../../utils/TextConfig.dart';
import '../component.dart';
import '../model/head_media/head_media_model.dart';
import '../model/head_media/head_media_rate_model.dart';
import '../model/head_media/head_media_scenic_info_model.dart';
import '../model/head_media/head_media_sellPoint_model.dart';
import '../model/head_media/head_media_shop_model.dart';
import '../notifier/media_container_change_notifier.dart';
import '../notifier/media_container_controller.dart';
import '../notifier/media_container_data_notifier.dart';
import '../widget/head_media/img_widget.dart';
import '../widget/head_media/rate_widget.dart';
import '../widget/head_media/scenic_info_widet.dart';
import '../widget/head_media/sell_point_widget.dart';
import '../widget/head_media/shop_widget.dart';
import '../widget/head_media/video_widget.dart';
import 'package:fround_image/fround_image.dart';

/// 商详头图容器,不包括锚点,包括进度条 常驻层
class FliggyVacationHeadMediaContainer extends StatefulWidget {
  const FliggyVacationHeadMediaContainer({Key? key}) : super(key: key);

  @override
  State<FliggyVacationHeadMediaContainer> createState() =>
      _FliggyVacationHeadMediaContainerState();
}

class _FliggyVacationHeadMediaContainerState
    extends State<FliggyVacationHeadMediaContainer> {
  List<FliggyVacationHeadMediaModel> mediaDataList =
      <FliggyVacationHeadMediaModel>[];

  void exposeHeadMedia(
      HeadMediaProcessChangeNotifier changeNotifier, int index) {
    // final FliggyVacationMediaModel? mediaModel =
    //     changeNotifier.itemDetailModel.headMediaModel;
    // if (mediaModel == null) {
    //   return;
    // }
    // final List<FliggyVacationHeadMediaModel> mediaDataArr =
    //     mediaModel.mediaData;
    // if (index >= mediaDataArr.length) {
    //   return;
    // }
    //
    // // 切换头图，曝光图片或者视频
    // final FliggyVacationHeadMediaModel model = mediaDataList[index];
    // String spmCD = 'banner.img';
    // if (model is FliggyVacationHeadVideoModel) {
    //   // 视频
    //   spmCD = 'banner.video';
    // }
    // final GroupInfo info =
    //     changeNotifier.itemDetailModel!.headMediaModel!.groupInfo.value;
    // final Map<String, String> trackParams = <String, String>{};
    // trackParams['tag'] = info.name; // 标签名
    // trackParams['pos'] = '${info.location - 1}'; // 属于该标签下的第几个, 从0开始计数
    // trackParams['index'] = '$index'; // 整个头图中的位置
    //
    // changeNotifier.ctrlExposure(context, spmCD, trackParams);
  }

  @override
  Widget build(BuildContext context) {
    // final HeadMediaProcessChangeNotifier changeNotifier =
    //     Provider.of<HeadMediaProcessChangeNotifier>(context);
    final HeadMediaProcessChangeNotifier changeNotifier =
        Provider.of<HeadMediaProcessChangeNotifier>(context);
    final FliggyVacationMediaModel? mediaModel =
        changeNotifier.itemDetailModel.headMediaModel;
    if (mediaModel == null) {
      return Container();
    }
    mediaModel.headMediaPageController =
        PreloadPageController(initialPage: mediaModel.allMediasPos.value);
    mediaDataList = mediaModel.mediaData;

    // 页面创建时，曝光显示的头图
    exposeHeadMedia(changeNotifier, mediaModel.allMediasPos.value);

    return SizedBox(
      height: FliggyVacationMediaNotifier.mediaDefaultHeight.value,
      width: FliggyVacationMediaNotifier.mediaDefaultWidth.value,
      child: Stack(
        children: <Widget>[
          if (changeNotifier.dataModel.containsKey('isCache') &&
              PreLoadHelper.headPicCache
                  .containsKey(changeNotifier.itemDetailEngine.itemId) &&
              PreLoadHelper
                      .headPicCache[changeNotifier.itemDetailEngine.itemId] !=
                  null)
            FRoundImage.network(
              PreLoadHelper
                  .headPicCache[changeNotifier.itemDetailEngine.itemId],
              width: 375,
              height: 375,
            ),
          if (!changeNotifier.dataModel.containsKey('isCache'))
            PhotoViewGallery.builder(
              scrollPhysics: const BouncingScrollPhysics(),
              builder: buildPageViewItemWidget,
              preloadCount: 2,
              onPageChanged: (int index) {
                // 切换头图时曝光
                exposeHeadMedia(changeNotifier, index);
                if (!mediaModel.groupEnable.value) {
                  mediaModel.groupEnable.value = true;
                }
                // currentIndex.value = index;
                mediaModel.allMediasPos.value = index;
              },
              //控制器
              pageController: mediaModel.headMediaPageController,
              itemCount: mediaDataList.length,
            ),
          // 头图上半部分蒙层
          Container(
            height: 80,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: <Color>[
                  // 渐变颜色数组
                  Color(0xFF000000), // 开始颜色
                  Color(0x00000000), // 结束颜色
                ],
              ),
            ),
          ),

          if (mediaModel.headMediaProcessModel.mainContainerInfo != null)
            // 定制层
            Positioned(
              top: Platform.isIOS ? 92 : 82,
              left: 12,
              right: 12,
              child: GestureDetector(
                onTap: () {
                  if (mediaModel.headMediaProcessModel.mainContainerInfo
                          ?.hotelIntroduceVO?.jumpUrl !=
                      null) {
                    FliggyNavigatorApi.getInstance().push(
                        context,
                        mediaModel.headMediaProcessModel.mainContainerInfo
                                ?.hotelIntroduceVO?.jumpUrl ??
                            '');
                  }
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0x99000000),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  padding: const EdgeInsets.only(
                      left: 10, right: 10, top: 9, bottom: 9),
                  // height: 52,
                  width: 150,
                  child: Row(
                    children: <Widget>[
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          if (mediaModel.headMediaProcessModel.mainContainerInfo
                                  ?.hotelIntroduceVO?.single ??
                              false)
                            Text(
                              mediaModel.headMediaProcessModel.mainContainerInfo
                                      ?.hotelIntroduceVO?.title ??
                                  '',
                              style: const TextStyle(
                                color: Color(0xFFFFFFFF),
                                fontSize: 12,
                                height: 1,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          if ((mediaModel
                                      .headMediaProcessModel
                                      .mainContainerInfo
                                      ?.hotelIntroduceVO
                                      ?.single ??
                                  false) &&
                              (mediaModel
                                      .headMediaProcessModel
                                      .mainContainerInfo
                                      ?.hotelIntroduceVO
                                      ?.showSingleDesc
                                      ?.isNotEmpty ??
                                  false))
                            Container(
                              margin: const EdgeInsets.only(top: 3),
                              child: Text(
                                mediaModel
                                        .headMediaProcessModel
                                        .mainContainerInfo
                                        ?.hotelIntroduceVO
                                        ?.showSingleDesc ??
                                    '',
                                style: const TextStyle(
                                  color: Color(0xFFFFFFFF),
                                  fontSize: 11,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          if (!(mediaModel
                                  .headMediaProcessModel
                                  .mainContainerInfo
                                  ?.hotelIntroduceVO
                                  ?.single ??
                              false))
                            Text(
                              mediaModel.headMediaProcessModel.mainContainerInfo
                                      ?.hotelIntroduceVO?.title ??
                                  '',
                              style: const TextStyle(
                                color: Color(0xCCFFFFFF),
                                fontSize: 11,
                                height: 1,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          if (!(mediaModel
                                      .headMediaProcessModel
                                      .mainContainerInfo
                                      ?.hotelIntroduceVO
                                      ?.single ??
                                  false) &&
                              (mediaModel
                                      .headMediaProcessModel
                                      .mainContainerInfo
                                      ?.hotelIntroduceVO
                                      ?.showNotSingleDesc
                                      ?.isNotEmpty ??
                                  false))
                            Container(
                              margin: EdgeInsets.only(top: 5),
                              child: Text(
                                mediaModel
                                        .headMediaProcessModel
                                        .mainContainerInfo
                                        ?.hotelIntroduceVO
                                        ?.showNotSingleDesc ??
                                    '',
                                style: const TextStyle(
                                  color: Color(0xFFfeebd8),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                        ],
                      )),
                      Container(
                        height: 22,
                        decoration: BoxDecoration(
                          color: const Color(0xFFFFFFFF),
                          borderRadius: BorderRadius.circular(11),
                        ),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 5),
                        margin: const EdgeInsets.only(left: 6),
                        child: Text(
                          mediaModel.headMediaProcessModel.mainContainerInfo
                                  ?.hotelIntroduceVO?.buttonName ??
                              '',
                          style: TextStyle(
                            color: const Color(0xFF0F131A),
                            fontSize: 12,
                            fontWeight: FontWeightExt.bold,
                            height: 1.1,
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  PhotoViewGalleryPageOptions buildPageViewItemWidget(
      BuildContext context, int index) {
    Widget renderWidget;

    final FliggyVacationHeadMediaModel model = mediaDataList[index];
    if (model is FliggyVacationHeadImageModel) {
      renderWidget = RepaintBoundary(
          child: FliggyVacationHeadImageWidget(
        imgModel: model,
        height: FliggyVacationMediaNotifier.mediaDefaultHeight.value,
        width: FliggyVacationMediaNotifier.mediaDefaultWidth.value,
      ));
    } else if (model is FliggyVacationHeadVideoModel) {
      renderWidget = RepaintBoundary(
          child: FliggyVacationHeadVideoWidget(
        videoModel: model,
        height: FliggyVacationMediaNotifier.mediaDefaultHeight.value,
        width: FliggyVacationMediaNotifier.mediaDefaultWidth.value,
      ));
    } else if (model is FliggyVacationHeadScenicInfoModel) {
      // 景点

      renderWidget = RepaintBoundary(
          child: FliggyVacationHeadScenicInfoWidget(
        scenicInfoModel: model,
      ));
    } else if (model is FliggyVacationHeadSellPointModel) {
      // 商品特色
      renderWidget = RepaintBoundary(
          child: FliggyHeadSellPointWidget(
        sellPointModel: model,
      ));
    } else if (model is FliggyVacationHeadRateModel) {
      // 用户印象
      renderWidget = FliggyHeadRateWidget(
        rateModel: model,
      );
    } else if (model is FliggyVacationHeadShopModel) {
      // 店铺
      renderWidget = FliggyHeadMediaShopWidget(
        shopModel: model,
      );
    } else {
      renderWidget = Container();
    }

    return PhotoViewGalleryPageOptions.customChild(
      disableGestures: true,
      child: Container(
        child: renderWidget,
      ),
    );
  }
}

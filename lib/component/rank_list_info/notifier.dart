import 'package:flutter/cupertino.dart';

import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import '../../utils/track_utils.dart';
import 'model.dart';

/// <AUTHOR>
/// @date Created on 2024/10/22
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

class RateListInfoChangeNotifier extends ComponentChangeNotifier {
  RateListInfoChangeNotifier(ComponentContext componentContext) : super(componentContext);


  RankListInfoModel? get rankListInfoModel => itemDetailModel.rankListInfoModel;

  @override
  void ctrlExposure(BuildContext context, String spmCD,
      Map<String, dynamic>? trackParams) {
    TrackUtils.exposureTrack(
        context, rankListInfoModel?.spmCD ?? '', trackParams, model: itemDetailModel);
  }

}

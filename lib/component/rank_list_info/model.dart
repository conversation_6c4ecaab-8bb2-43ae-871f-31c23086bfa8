import '../../utils/safe_access.dart';
import 'package:fjson_exports/fjson_exports.dart';
/// <AUTHOR>
/// @date Created on 2024/10/22
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

class RankListInfoModel {
  String? desc;
  bool hide = true;
  String spmCD = '';

  String? jumpH5Url;

  // 埋点参数
  Map<String, dynamic>? trackParams;

  RankListInfoModel.fromJson(Map<String, dynamic> rankListInfo) {
    final Map<String, dynamic> data = safeNonNullMap(rankListInfo['data'], (dynamic e) =>e);
    hide = safeNonNullBool(data['hide']);

    if (hide) {
      spmCD = 'basicInfo.rank_b';
    } else {
      spmCD = 'basicInfo.rank';
    }

    desc = SafeAccess.safeParseString(data['desc']);
    // 跳转链接
    final Map<String, dynamic> jumpInfo = safeNonNullMap(data['jumpInfo'], (dynamic e) =>e);
    jumpH5Url = safeString(jumpInfo['jumpH5Url']);

    // 埋点参数
    trackParams = safeNonNullMap(data['trackParams'], (dynamic e) =>e);
  }
}

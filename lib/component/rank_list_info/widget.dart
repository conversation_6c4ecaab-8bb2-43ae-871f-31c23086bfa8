import 'package:flutter/material.dart';

import 'package:provider/provider.dart';
import '../../custom_widget/detail_arrow.dart';
import '../../custom_widget/null_widget.dart';
import '../../utils/common_config.dart';
import 'model.dart';
import 'notifier.dart';
import 'package:fliggy_router/fliggy_router.dart';
/// <AUTHOR>
/// @date Created on 2024/10/22
/// @email <EMAIL>
/// @company Alibaba Group
/// @description


class RankListInfo extends StatelessWidget {
  const RankListInfo({Key? key}) : super(key: key);


  @override
  Widget build(BuildContext context) {
    final RateListInfoChangeNotifier changeNotifier =
    Provider.of<RateListInfoChangeNotifier>(context);
    final RankListInfoModel? rankListInfoModel =
        changeNotifier.itemDetailModel.rankListInfoModel;

    // 没数据,不曝光
    if (rankListInfoModel == null) {
      return nullWidget;
    }

    // 这个地方产品要求,有数据就曝光,有展示标就展示,曝光为 basicInfo.rank 展示标为 false 就不展示, 曝光标为 basicInfo.rank_b
    changeNotifier.ctrlExposure(context, rankListInfoModel.spmCD, rankListInfoModel!.trackParams);
    if (rankListInfoModel.hide) {
      // 隐藏:不展示,有曝光
      return nullWidget;
    }

    return Container(
      width: 357,
      height:  36,
      color: const Color(0xFFFFFFFF),
      child: GestureDetector(
        onTap: () {
          // Open H5 URL
          if (rankListInfoModel.jumpH5Url!= null) {
            changeNotifier.ctrlClicked(context, rankListInfoModel.spmCD, 'rankListInfoModel', rankListInfoModel.trackParams);
            FliggyNavigatorApi.getInstance().push(context, rankListInfoModel.jumpH5Url!);
          }

        },
        child: Container(
          width: 333,
          height: 30,
          margin: EdgeInsets.only(top: 10, left: 12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            gradient: LinearGradient(
              colors: <Color>[Color(0xFFFEF5EC), Color(0xffffffff)],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
          child: Row(
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.only(left: 9),
                child: Image.network(
                  'https://gw.alicdn.com/imgextra/i2/O1CN01qQdeRO1FUvQYBcTjm_!!6000000000491-2-tps-39-41.png',
                  height: 12,
                  width: 12,
                ),
              ),
              if (rankListInfoModel.desc != null && rankListInfoModel.desc!.isNotEmpty)
                Container(
                  width: 306,
                  margin: EdgeInsets.only(left: 6),
                  child: Row(
                    children: <Widget>[
                      Expanded(
                        child: Text(
                          rankListInfoModel.desc!,
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF805540),
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      rightArrowSmall,
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}


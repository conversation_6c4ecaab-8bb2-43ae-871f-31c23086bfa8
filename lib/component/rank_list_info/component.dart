import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

/// <AUTHOR>
/// @date Created on 2024/10/22
/// @email <EMAIL>
/// @company Alibaba Group
/// @description
///

///@description TODO 组件功能
class RankListInfoComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<RateListInfoChangeNotifier>.value(
      value: RateListInfoChangeNotifier(context),
      child: const RankListInfo(),
    );
  }
}

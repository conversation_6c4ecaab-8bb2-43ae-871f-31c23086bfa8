import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class PackageFestivalListWidget extends StatelessWidget {
  const PackageFestivalListWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final PackageFestivalListChangeNotifier changeNotifier =
        Provider.of<PackageFestivalListChangeNotifier>(context);
    return Container();
  }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class HotelMemberCardV2Widget extends StatelessWidget {
  const HotelMemberCardV2Widget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final HotelMemberCardV2ChangeNotifier changeNotifier =
        Provider.of<HotelMemberCardV2ChangeNotifier>(context);
    return Container();
  }
}
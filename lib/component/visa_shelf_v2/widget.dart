import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class VisaShelfV2Widget extends StatelessWidget {
  const VisaShelfV2Widget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VisaShelfV2ChangeNotifier changeNotifier =
        Provider.of<VisaShelfV2ChangeNotifier>(context);
    return Container();
  }
}
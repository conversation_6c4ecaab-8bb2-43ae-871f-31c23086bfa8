import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description TODO 组件功能
class VisaShelfV2Component extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<VisaShelfV2ChangeNotifier>.value(
      value: VisaShelfV2ChangeNotifier(context),
      child: const VisaShelfV2Widget(),
    );
  }
}
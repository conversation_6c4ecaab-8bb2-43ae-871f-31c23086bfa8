import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class PurchaseNotesWidget extends StatelessWidget {
  const PurchaseNotesWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final PurchaseNotesChangeNotifier changeNotifier =
        Provider.of<PurchaseNotesChangeNotifier>(context);
    return Container();
  }
}
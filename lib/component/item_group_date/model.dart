import '../../utils/safe_access.dart';
import 'package:fjson_exports/fjson_exports.dart';
class ItemGroupDateModel {
  ItemGroupDateModel();

  String? iconUrl;
  String? packageDesc;
  String? defaultDesc;

  bool? dynamicPack;
  String? moreText;
  String? packageSkuPropPid;
  String? text;
  List<TuanPriceModel>? tuanPrice;

  factory ItemGroupDateModel.fromJson(Map<String, dynamic> json) {
    final ItemGroupDateModel model = ItemGroupDateModel();

    model.iconUrl = safeString(json['iconUrl']);
    model.packageDesc = safeString(json['packageDesc']);
    model.defaultDesc = safeString(json['defaultDesc']);
    if (json['tuanPrice'] != null) {
      model.tuanPrice = <TuanPriceModel>[];
      json['tuanPrice'].forEach((v) {
        model.tuanPrice!.add(TuanPriceModel.fromJson(v));
      });
    }

    return model;
  }


}


class TuanPriceModel {
  TuanPriceModel();


  String? title;
  String? monthCn;
  String? day;
  String? finalPrice;
  Map<String,dynamic>? eventParams;

  factory TuanPriceModel.fromJson(Map<String, dynamic> json) {
    final TuanPriceModel model = TuanPriceModel();
    model.title = safeString(json['title']);
    model.monthCn = safeString(json['monthCn']);
    model.day = safeString(json['day']);
    model.finalPrice = safeString(json['finalPrice']);
    model.eventParams = json['event']?['openSkuEvent']?[0]?['params'];
    return model;
  }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../utils/common_config.dart';
import 'model.dart';
import 'notifier.dart';
import 'package:cached_network_image/cached_network_image.dart';

class ItemGroupDateWidget extends StatelessWidget {
  const ItemGroupDateWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ItemGroupDateChangeNotifier changeNotifier =
        Provider.of<ItemGroupDateChangeNotifier>(context);

    if (changeNotifier.itemDetailModel.itemGroupDateModel == null) {
      return SizedBox.shrink();
    }

    return buildItemGroupDateWidget(
        changeNotifier, changeNotifier.itemDetailModel.itemGroupDateModel!,
        onMoreTap: () {
      changeNotifier.showMoreStroke();
    });
  }

  Widget buildItemGroupDateWidget(
      ItemGroupDateChangeNotifier changeNotifier, ItemGroupDateModel data,
      {VoidCallback? onMoreTap}) {
    return Container(
      color: const Color(0xFFF0F0F0), // 最外层背景色
      child: Center(
        child: Container(
          margin: const EdgeInsets.only(bottom: itemDivider),
          decoration: BoxDecoration(
            color: Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // 顶部 Header 区域
              _buildHeader(data, onMoreTap),

              // 价格卡片区域
              if (data.tuanPrice != null)
                _buildPriceList(changeNotifier, data.tuanPrice!),
            ],
          ),
        ),
      ),
    );
  }

// Header 区域：图标、标题和“更多套餐”
  Widget _buildHeader(ItemGroupDateModel data, VoidCallback? onMoreTap) {
    return Padding(
      padding: const EdgeInsets.only(top: 12, left: 12, right: 12, bottom: 12),
      child: Row(
        children: <Widget>[
          // 图标
          if (data.iconUrl != null && data.iconUrl!.isNotEmpty)
            CachedNetworkImage(
              imageUrl: data.iconUrl!,
              width: 15,
              height: 15,
              fit: BoxFit.fitHeight,
            ),
          const SizedBox(width: 6),

          // 描述
          Expanded(
            child: Text(
              data.packageDesc ?? data.defaultDesc ?? "",
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333),
              ),
            ),
          ),

          // 更多套餐按钮
          GestureDetector(
            onTap: onMoreTap,
            child: Row(
              children: const [
                Text(
                  '更多套餐',
                  style: TextStyle(
                    fontSize: 13,
                    color: Color(0xFF00A2FF),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 13,
                  color: Color(0xFF00A2FF),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

// 价格卡片区域
  Widget _buildPriceList(ItemGroupDateChangeNotifier changeNotifier,
      List<TuanPriceModel> tuanPrice) {
    return SizedBox(
      height: 111,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: tuanPrice.length,
        padding: EdgeInsets.only(left: 8),
        itemBuilder: (context, index) {
          final item = tuanPrice[index];
          return _buildPriceCard(changeNotifier, item);
        },
      ),
    );
  }

// 单个价格卡片
  Widget _buildPriceCard(
      ItemGroupDateChangeNotifier changeNotifier, TuanPriceModel item) {
    return GestureDetector(
      onTap: () {
        changeNotifier.showMoreStroke(params: item.eventParams);
      },
      child: Container(
        width: 79,
        margin: const EdgeInsets.only(left: 5, bottom: 12),
        decoration: BoxDecoration(
          color: const Color(0xFFFCFCFC),
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: const Color(0xFFF1F1F1), width: 0.5),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            // 顶部标记（如 title 特殊展示）
            if (item.title != null && item.title!.isNotEmpty)
              Container(
                width: 42,
                height: 18,
                alignment: Alignment.center,
                decoration: const BoxDecoration(
                  color: Color(0xFFEDF8FF),
                  borderRadius:
                      BorderRadius.vertical(bottom: Radius.circular(6)),
                ),
                child: Text(
                  item.title!,
                  style:
                      const TextStyle(fontSize: 10, color: Color(0xFF00A2FF)),
                ),
              ),

            const SizedBox(height: 6),

            // 月份
            Text(
              item.monthCn ?? '',
              style: const TextStyle(fontSize: 12, color: Color(0xFF5C5F66)),
            ),

            // 分隔线
            const Divider(
              color: Color(0xFF5C5F66),
              thickness: 0.5,
              height: 8,
              indent: 20,
              endIndent: 20,
            ),

            // 日期
            Text(
              item.day ?? '',
              style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF5C5F66)),
            ),

            const SizedBox(height: 4),

            // 价格
            Text(
              '¥${item.finalPrice ?? ''}',
              style: const TextStyle(fontSize: 12, color: Color(0xFFFF401A)),
            ),
          ],
        ),
      ),
    );
  }
}

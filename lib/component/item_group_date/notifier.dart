import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';

class ItemGroupDateChangeNotifier extends ComponentChangeNotifier {
  ItemGroupDateChangeNotifier(ComponentContext context) : super(context);

  void showMoreStroke({Map<String, dynamic>? params}) {
    itemDetailEngine.skuManager.buyNow(
        date: params?['date'],
        skuType: params?['skuType'],
        propPath: params?['propPath']);
  }
}

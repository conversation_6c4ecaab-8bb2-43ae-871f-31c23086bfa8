


import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../flight_package_manager/flight_package_managerV2.dart';
import 'notifier.dart';

/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description


class FlightShelfV2 extends StatefulWidget {

  @override
  State<FlightShelfV2> createState() => _FlightShelfV2State();
}

class _FlightShelfV2State extends State<FlightShelfV2> {
  @override
  Widget build(BuildContext context) {
    FlightShelfV2ChangeNotifier changeNotifier = Provider.of<FlightShelfV2ChangeNotifier>(context);
    FlightPackageManagerV2? flightPackageManagerV2 = changeNotifier.itemDetailModel.flightPackageManagerV2;
    if (flightPackageManagerV2 == null) {
      return Container();
    }
    return Container(
      child: Column (
        children: <Widget>[
          Row(
            children: <Widget>[
              GestureDetector(
                onTap: () {},
                child: Text('切换'),
              ),
            ],
          ),
          Column(
            children: flightPackageManagerV2!.filterPackageList!.map((e) => Text(e.packageName ?? 'suibian')).toList(),
          )
        ],
      )
      ,
    );
  }
}

import '../flight_package_manager/flight_package_model.dart';

/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

const DEFAULT_SHOW_NUM = 3;
class FlightShelfV2Model {
  FlightShelfV2Model();
  //
  //
  // List<String> depList = [];
  // String depCityCode = '';
  // List<String> arrList = [];
  // String arrCityCode = '';
  // int showCount = DEFAULT_SHOW_NUM;
  // List<String> filterPackageInfos = [];
  //
  // // Assuming these methods return some data similar to ctx.getGlobalData in React
  // late List<FlightPackageModel> packageInfos;
  // Map<String, dynamic> locationSelectorInfo = getGlobalFilterSkuByRoute();
  // Map<String, dynamic> availableCityList = getAvailableDepartureCityList();
  // String locationCityCode = getLBSLocationCityCode();
  //
  // String? businessLine;
  // bool supportLbsFilter = false;
  // String? itemId;
  // // Assuming getItemInfo() and useModel() functions or their equivalents
  // String? curSelectedSkuId = useModel();
  //
  // late Map<String, dynamic> depatureCityMap;
  // late Map<String, dynamic> destinationCityMap;
  //
  factory FlightShelfV2Model.fromJson(Map json) {
    final FlightShelfV2Model model = FlightShelfV2Model();
  //
  //   // 初始化整个package
  //   if (json.containsKey('packageInfos')) {
  //     final List packageInfos = safeNonNullList(json['packageInfos'], (dynamic e) => e);
  //     model.packageInfos = [];
  //     for (dynamic package in packageInfos) {
  //       model.packageInfos.add(FlightPackageModel.fromJson(package));
  //     }
  //   }
    return model;
  }
}
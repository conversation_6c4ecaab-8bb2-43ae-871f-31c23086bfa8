import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../render/component/component.dart';
import '../../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description


///@description titleBar
class FlightShelfV2Component extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<FlightShelfV2ChangeNotifier>.value(
      value: FlightShelfV2ChangeNotifier(context),
      child: FlightShelfV2(),
    );
  }
}
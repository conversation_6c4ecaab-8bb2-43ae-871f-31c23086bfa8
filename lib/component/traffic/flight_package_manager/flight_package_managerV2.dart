
import 'package:fjson_exports/fjson_exports.dart';
import 'flight_package_model.dart';

/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

/// 机票货架管理，涉及od的筛选和联动，原始数据的存放，以及筛选后的数据的存放
class FlightPackageManagerV2 {
  FlightPackageManagerV2();

  /// 原始数据
  List<FlightPackageModel>? packageList;

  /// 筛选后的数据
  List<FlightPackageModel>? filterPackageList;

  /// 初始的出发地码
  Map departureCityList = {};

  /// 初始的目的地码
  Map destCityList = {};

  /// 是否允许筛选
  bool supportItemDetailFilterSkuByRoutes = false;

  /// 不知道啥玩意，先放着，后面看
  String? businessLine;

  /// 当前所在地城市
  String? locationCityCode;
  factory FlightPackageManagerV2.fromJson(Map json) {
    final FlightPackageManagerV2 model = FlightPackageManagerV2();

    // 初始化货架数据
    if (json.containsKey('packageShelf')) {
      final Map packageShelf = safeNonNullMap(json['packageShelf'], (dynamic e) => e);
      final Map packageShelfData = safeNonNullMap(packageShelf['data'], (dynamic e) => e);
      final List packageInfos = safeNonNullList(packageShelfData['packageInfos'], (dynamic e) => e);
      model.packageList = [];
      for (dynamic package in packageInfos) {
        model.packageList!.add(FlightPackageModel.fromJson(package));
      }
      
    }

    if (json.containsKey('availableDepartureCityList')) {
      final Map availableDepartureCityList = safeNonNullMap(json['availableDepartureCityList'], (dynamic e) => e);
      final Map availableDepartureCityListData = safeNonNullMap(availableDepartureCityList['data'], (dynamic e) => e);
      final List departureCityList = safeNonNullList(availableDepartureCityListData['departureCityList'], (dynamic e) => e);
      for (dynamic city in departureCityList) {
        final Map cityMap = safeNonNullMap(city, (dynamic e) => e);
        model.departureCityList[cityMap['cityCode']] = cityMap['cityName'];
      }

      final List destCityList = safeNonNullList(availableDepartureCityListData['destCityList'], (dynamic e) => e);
      for (dynamic city in destCityList) {
        final Map cityMap = safeNonNullMap(city, (dynamic e) => e);
        model.destCityList[cityMap['cityCode']] = cityMap['cityName'];
      }
    }
    
    // 是否允许筛选
    if (json.containsKey('filterSkuByRoute')) {
      final Map filterSkuByRoute = safeNonNullMap(json['filterSkuByRoute'], (dynamic e) => e);
      final Map filterSkuByRouteData = safeNonNullMap(filterSkuByRoute['data'], (dynamic e) => e);
      model.supportItemDetailFilterSkuByRoutes = safeNonNullBool(filterSkuByRouteData['supportFilterSkuByRoute']);
      model.businessLine = safeString(filterSkuByRouteData['businessLine']);
    }
    
    if (json.containsKey('LBSLocationCity')) {
      final Map lbsLocationCity = safeNonNullMap(json['LBSLocationCity'], (dynamic e) => e);
      final Map lbsLocationCityData = safeNonNullMap(lbsLocationCity['data'], (dynamic e) => e);
      model.locationCityCode = safeString(lbsLocationCityData['cityCode']);
    }


    // 初始时没有筛选，筛选的package就是下发的
    model.filterPackageList = model.packageList;
    return model;
  }
}


//
// import '';
// /// <AUTHOR>
// /// @date Created on 2024/12/6
// /// @email <EMAIL>
// /// @company Alibaba Group
// /// @description
//
import '../../../utils/safe_access.dart';

class FlightPackageModel {
  // 没有案例，后面再说
  // List<Null>? advantageTags;
  List<String>? availableDepartureCitys;
  String? buyButtonDesc;
  bool? buyButtonGray;
  bool? buyButtonSupport;
  String? cartButtonDesc;
  bool? cartButtonSupport;
  bool? couponFlag;

  /// 价格数字
  int? finalPrice;

  /// 价格字符串
  String? finalPriceText;
  String? hotRoutes;
  bool? moreHotZone;
  String? packageExplainDesc;
  PackageExplainLink? packageExplainLink;
  PackageInfo? packageInfo;
  String? packageName;
  String? packagePid;
  String? packageVId;
  int? price;
  String? priceText;

  // 没有案例，后面再说
  // List<Null>? productDescList;
  int? roundTripType;
  bool? showBuyButton;
  int? skuId;
  String? skuPvId;
  List<SkuRoutes>? skuRoutes;
  bool? soldOut;
  List<TravelDates>? travelDates;

  FlightPackageModel(
      { // this.advantageTags,
      this.availableDepartureCitys,
      this.buyButtonDesc,
      this.buyButtonGray,
      this.buyButtonSupport,
      this.cartButtonDesc,
      this.cartButtonSupport,
      this.couponFlag,
      this.finalPrice,
      this.finalPriceText,
      this.hotRoutes,
      this.moreHotZone,
      this.packageExplainDesc,
      this.packageExplainLink,
      this.packageInfo,
      this.packageName,
      this.packagePid,
      this.packageVId,
      this.price,
      this.priceText,
      // this.productDescList,
      this.roundTripType,
      this.showBuyButton,
      this.skuId,
      this.skuPvId,
      this.skuRoutes,
      this.soldOut,
      this.travelDates});

  FlightPackageModel.fromJson(Map<String, dynamic> json) {
    // if (json['advantageTags'] != null) {
    //   advantageTags = <Null>[];
    //   SafeAccess.safeParseList(json['advantageTags']).forEach((v) {
    //     advantageTags!.add(new Null.fromJson(
    //         SafeAccess.safeParseMap(v) as Map<String, dynamic>));
    //   });
    // }
    availableDepartureCitys =
        SafeAccess.safeParseList(json['availableDepartureCitys'])
            .cast<String>();
    buyButtonDesc = SafeAccess.safeParseString(json['buyButtonDesc']);
    buyButtonGray = SafeAccess.safeParseBoolean(json['buyButtonGray']);
    buyButtonSupport = SafeAccess.safeParseBoolean(json['buyButtonSupport']);
    cartButtonDesc = SafeAccess.safeParseString(json['cartButtonDesc']);
    cartButtonSupport = SafeAccess.safeParseBoolean(json['cartButtonSupport']);
    couponFlag = SafeAccess.safeParseBoolean(json['couponFlag']);
    finalPrice = SafeAccess.safeParseInt(json['finalPrice']);
    finalPriceText = SafeAccess.safeParseString(json['finalPriceText']);
    hotRoutes = SafeAccess.safeParseString(json['hotRoutes']);
    moreHotZone = SafeAccess.safeParseBoolean(json['moreHotZone']);
    packageExplainDesc = SafeAccess.safeParseString(json['packageExplainDesc']);
    packageExplainLink = json['packageExplainLink'] != null
        ? new PackageExplainLink.fromJson(
            SafeAccess.safeParseMap(json['packageExplainLink'])
                as Map<String, dynamic>)
        : null;
    packageInfo = json['packageInfo'] != null
        ? new PackageInfo.fromJson(SafeAccess.safeParseMap(json['packageInfo'])
            as Map<String, dynamic>)
        : null;
    packageName = SafeAccess.safeParseString(json['packageName']);
    packagePid = SafeAccess.safeParseString(json['packagePid']);
    packageVId = SafeAccess.safeParseString(json['packageVId']);
    price = SafeAccess.safeParseInt(json['price']);
    priceText = SafeAccess.safeParseString(json['priceText']);
    // if (json['productDescList'] != null) {
    //   productDescList = <Null>[];
    //   SafeAccess.safeParseList(json['productDescList']).forEach((v) {
    //     productDescList!.add(new Null.fromJson(
    //         SafeAccess.safeParseMap(v) as Map<String, dynamic>));
    //   });
    // }
    roundTripType = SafeAccess.safeParseInt(json['roundTripType']);
    showBuyButton = SafeAccess.safeParseBoolean(json['showBuyButton']);
    skuId = SafeAccess.safeParseInt(json['skuId']);
    skuPvId = SafeAccess.safeParseString(json['skuPvId']);
    if (json['skuRoutes'] != null) {
      skuRoutes = <SkuRoutes>[];
      SafeAccess.safeParseList(json['skuRoutes']).forEach((v) {
        skuRoutes!.add(new SkuRoutes.fromJson(
            SafeAccess.safeParseMap(v) as Map<String, dynamic>));
      });
    }
    soldOut = SafeAccess.safeParseBoolean(json['soldOut']);
    if (json['travelDates'] != null) {
      travelDates = <TravelDates>[];
      SafeAccess.safeParseList(json['travelDates']).forEach((v) {
        travelDates!.add(new TravelDates.fromJson(
            SafeAccess.safeParseMap(v) as Map<String, dynamic>));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    // if (this.advantageTags != null) {
    //   data['advantageTags'] =
    //       this.advantageTags!.map((v) => v?.toJson()).toList();
    // }
    data['availableDepartureCitys'] = this.availableDepartureCitys;
    data['buyButtonDesc'] = this.buyButtonDesc;
    data['buyButtonGray'] = this.buyButtonGray;
    data['buyButtonSupport'] = this.buyButtonSupport;
    data['cartButtonDesc'] = this.cartButtonDesc;
    data['cartButtonSupport'] = this.cartButtonSupport;
    data['couponFlag'] = this.couponFlag;
    data['finalPrice'] = this.finalPrice;
    data['finalPriceText'] = this.finalPriceText;
    data['hotRoutes'] = this.hotRoutes;
    data['moreHotZone'] = this.moreHotZone;
    data['packageExplainDesc'] = this.packageExplainDesc;
    if (this.packageExplainLink != null) {
      data['packageExplainLink'] = this.packageExplainLink!.toJson();
    }
    if (this.packageInfo != null) {
      data['packageInfo'] = this.packageInfo!.toJson();
    }
    data['packageName'] = this.packageName;
    data['packagePid'] = this.packagePid;
    data['packageVId'] = this.packageVId;
    data['price'] = this.price;
    data['priceText'] = this.priceText;
    // if (this.productDescList != null) {
    //   data['productDescList'] =
    //       this.productDescList!.map((v) => v.toJson()).toList();
    // }
    data['roundTripType'] = this.roundTripType;
    data['showBuyButton'] = this.showBuyButton;
    data['skuId'] = this.skuId;
    data['skuPvId'] = this.skuPvId;
    if (this.skuRoutes != null) {
      data['skuRoutes'] = this.skuRoutes!.map((v) => v.toJson()).toList();
    }
    data['soldOut'] = this.soldOut;
    if (this.travelDates != null) {
      data['travelDates'] = this.travelDates!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PackageExplainLink {
  String? jumpH5Url;
  bool? jumpNative;

  PackageExplainLink({this.jumpH5Url, this.jumpNative});

  PackageExplainLink.fromJson(Map<String, dynamic> json) {
    jumpH5Url = SafeAccess.safeParseString(json['jumpH5Url']);
    jumpNative = SafeAccess.safeParseBoolean(json['jumpNative']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['jumpH5Url'] = this.jumpH5Url;
    data['jumpNative'] = this.jumpNative;
    return data;
  }
}

class PackageInfo {
  AvailableArrGroupByDep? availableArrGroupByDep;
  AvailableDepGroupByArr? availableDepGroupByArr;
  CoreInfo? coreInfo;
  bool? packageCanBuy;
  PackageRuleInfo? packageRuleInfo;
  int? showStatus;
  ToastInfo? toastInfo;
  String? travelDatesDesc;

  PackageInfo(
      {this.availableArrGroupByDep,
      this.availableDepGroupByArr,
      this.coreInfo,
      this.packageCanBuy,
      this.packageRuleInfo,
      this.showStatus,
      this.toastInfo,
      this.travelDatesDesc});

  PackageInfo.fromJson(Map<String, dynamic> json) {
    availableArrGroupByDep = json['availableArrGroupByDep'] != null
        ? new AvailableArrGroupByDep.fromJson(
            SafeAccess.safeParseMap(json['availableArrGroupByDep'])
                as Map<String, dynamic>)
        : null;
    availableDepGroupByArr = json['availableDepGroupByArr'] != null
        ? new AvailableDepGroupByArr.fromJson(
            SafeAccess.safeParseMap(json['availableDepGroupByArr'])
                as Map<String, dynamic>)
        : null;
    coreInfo = json['coreInfo'] != null
        ? new CoreInfo.fromJson(
            SafeAccess.safeParseMap(json['coreInfo']) as Map<String, dynamic>)
        : null;
    packageCanBuy = SafeAccess.safeParseBoolean(json['packageCanBuy']);
    packageRuleInfo = json['packageRuleInfo'] != null
        ? new PackageRuleInfo.fromJson(
            SafeAccess.safeParseMap(json['packageRuleInfo'])
                as Map<String, dynamic>)
        : null;
    showStatus = SafeAccess.safeParseInt(json['showStatus']);
    toastInfo = json['toastInfo'] != null
        ? new ToastInfo.fromJson(
            SafeAccess.safeParseMap(json['toastInfo']) as Map<String, dynamic>)
        : null;
    travelDatesDesc = SafeAccess.safeParseString(json['travelDatesDesc']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.availableArrGroupByDep != null) {
      data['availableArrGroupByDep'] = this.availableArrGroupByDep!.toJson();
    }
    if (this.availableDepGroupByArr != null) {
      data['availableDepGroupByArr'] = this.availableDepGroupByArr!.toJson();
    }
    if (this.coreInfo != null) {
      data['coreInfo'] = this.coreInfo!.toJson();
    }
    data['packageCanBuy'] = this.packageCanBuy;
    if (this.packageRuleInfo != null) {
      data['packageRuleInfo'] = this.packageRuleInfo!.toJson();
    }
    data['showStatus'] = this.showStatus;
    if (this.toastInfo != null) {
      data['toastInfo'] = this.toastInfo!.toJson();
    }
    data['travelDatesDesc'] = this.travelDatesDesc;
    return data;
  }
}

class AvailableArrGroupByDep {
  List<String>? kWL;
  List<String>? tAO;
  List<String>? tNA;
  List<String>? hFE;
  List<String>? nNG;
  List<String>? tYN;
  List<String>? cGO;
  List<String>? sIA;
  List<String>? wUH;
  List<String>? tSN;
  List<String>? kHN;
  List<String>? sHE;
  List<String>? hAK;
  List<String>? bAV;
  List<String>? xMN;
  List<String>? xNN;
  List<String>? dLC;
  List<String>? wEH;
  List<String>? yNT;
  List<String>? iNC;

  AvailableArrGroupByDep(
      {this.kWL,
      this.tAO,
      this.tNA,
      this.hFE,
      this.nNG,
      this.tYN,
      this.cGO,
      this.sIA,
      this.wUH,
      this.tSN,
      this.kHN,
      this.sHE,
      this.hAK,
      this.bAV,
      this.xMN,
      this.xNN,
      this.dLC,
      this.wEH,
      this.yNT,
      this.iNC});

  AvailableArrGroupByDep.fromJson(Map<String, dynamic> json) {
    kWL = SafeAccess.safeParseList(json['KWL']).cast<String>();
    tAO = SafeAccess.safeParseList(json['TAO']).cast<String>();
    tNA = SafeAccess.safeParseList(json['TNA']).cast<String>();
    hFE = SafeAccess.safeParseList(json['HFE']).cast<String>();
    nNG = SafeAccess.safeParseList(json['NNG']).cast<String>();
    tYN = SafeAccess.safeParseList(json['TYN']).cast<String>();
    cGO = SafeAccess.safeParseList(json['CGO']).cast<String>();
    sIA = SafeAccess.safeParseList(json['SIA']).cast<String>();
    wUH = SafeAccess.safeParseList(json['WUH']).cast<String>();
    tSN = SafeAccess.safeParseList(json['TSN']).cast<String>();
    kHN = SafeAccess.safeParseList(json['KHN']).cast<String>();
    sHE = SafeAccess.safeParseList(json['SHE']).cast<String>();
    hAK = SafeAccess.safeParseList(json['HAK']).cast<String>();
    bAV = SafeAccess.safeParseList(json['BAV']).cast<String>();
    xMN = SafeAccess.safeParseList(json['XMN']).cast<String>();
    xNN = SafeAccess.safeParseList(json['XNN']).cast<String>();
    dLC = SafeAccess.safeParseList(json['DLC']).cast<String>();
    wEH = SafeAccess.safeParseList(json['WEH']).cast<String>();
    yNT = SafeAccess.safeParseList(json['YNT']).cast<String>();
    iNC = SafeAccess.safeParseList(json['INC']).cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['KWL'] = this.kWL;
    data['TAO'] = this.tAO;
    data['TNA'] = this.tNA;
    data['HFE'] = this.hFE;
    data['NNG'] = this.nNG;
    data['TYN'] = this.tYN;
    data['CGO'] = this.cGO;
    data['SIA'] = this.sIA;
    data['WUH'] = this.wUH;
    data['TSN'] = this.tSN;
    data['KHN'] = this.kHN;
    data['SHE'] = this.sHE;
    data['HAK'] = this.hAK;
    data['BAV'] = this.bAV;
    data['XMN'] = this.xMN;
    data['XNN'] = this.xNN;
    data['DLC'] = this.dLC;
    data['WEH'] = this.wEH;
    data['YNT'] = this.yNT;
    data['INC'] = this.iNC;
    return data;
  }
}

class AvailableDepGroupByArr {
  List<String>? kWL;
  List<String>? tAO;
  List<String>? tNA;
  List<String>? hFE;
  List<String>? tYN;
  List<String>? cSX;
  List<String>? cGO;
  List<String>? wUH;
  List<String>? zUH;
  List<String>? mDG;
  List<String>? sHE;
  List<String>? hAK;
  List<String>? hET;
  List<String>? bAV;
  List<String>? xMN;
  List<String>? xNN;
  List<String>? dLC;
  List<String>? yNT;
  List<String>? iNC;

  AvailableDepGroupByArr(
      {this.kWL,
      this.tAO,
      this.tNA,
      this.hFE,
      this.tYN,
      this.cSX,
      this.cGO,
      this.wUH,
      this.zUH,
      this.mDG,
      this.sHE,
      this.hAK,
      this.hET,
      this.bAV,
      this.xMN,
      this.xNN,
      this.dLC,
      this.yNT,
      this.iNC});

  AvailableDepGroupByArr.fromJson(Map<String, dynamic> json) {
    kWL = SafeAccess.safeParseList(json['KWL']).cast<String>();
    tAO = SafeAccess.safeParseList(json['TAO']).cast<String>();
    tNA = SafeAccess.safeParseList(json['TNA']).cast<String>();
    hFE = SafeAccess.safeParseList(json['HFE']).cast<String>();
    tYN = SafeAccess.safeParseList(json['TYN']).cast<String>();
    cSX = SafeAccess.safeParseList(json['CSX']).cast<String>();
    cGO = SafeAccess.safeParseList(json['CGO']).cast<String>();
    wUH = SafeAccess.safeParseList(json['WUH']).cast<String>();
    zUH = SafeAccess.safeParseList(json['ZUH']).cast<String>();
    mDG = SafeAccess.safeParseList(json['MDG']).cast<String>();
    sHE = SafeAccess.safeParseList(json['SHE']).cast<String>();
    hAK = SafeAccess.safeParseList(json['HAK']).cast<String>();
    hET = SafeAccess.safeParseList(json['HET']).cast<String>();
    bAV = SafeAccess.safeParseList(json['BAV']).cast<String>();
    xMN = SafeAccess.safeParseList(json['XMN']).cast<String>();
    xNN = SafeAccess.safeParseList(json['XNN']).cast<String>();
    dLC = SafeAccess.safeParseList(json['DLC']).cast<String>();
    yNT = SafeAccess.safeParseList(json['YNT']).cast<String>();
    iNC = SafeAccess.safeParseList(json['INC']).cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['KWL'] = this.kWL;
    data['TAO'] = this.tAO;
    data['TNA'] = this.tNA;
    data['HFE'] = this.hFE;
    data['TYN'] = this.tYN;
    data['CSX'] = this.cSX;
    data['CGO'] = this.cGO;
    data['WUH'] = this.wUH;
    data['ZUH'] = this.zUH;
    data['MDG'] = this.mDG;
    data['SHE'] = this.sHE;
    data['HAK'] = this.hAK;
    data['HET'] = this.hET;
    data['BAV'] = this.bAV;
    data['XMN'] = this.xMN;
    data['XNN'] = this.xNN;
    data['DLC'] = this.dLC;
    data['YNT'] = this.yNT;
    data['INC'] = this.iNC;
    return data;
  }
}

class CoreInfo {
  List<DetailInfo>? detailInfo;
  List<SummaryInfo>? summaryInfo;

  CoreInfo({this.detailInfo, this.summaryInfo});

  CoreInfo.fromJson(Map<String, dynamic> json) {
    if (json['detailInfo'] != null) {
      detailInfo = <DetailInfo>[];
      SafeAccess.safeParseList(json['detailInfo']).forEach((v) {
        detailInfo!.add(new DetailInfo.fromJson(
            SafeAccess.safeParseMap(v) as Map<String, dynamic>));
      });
    }
    if (json['summaryInfo'] != null) {
      summaryInfo = <SummaryInfo>[];
      SafeAccess.safeParseList(json['summaryInfo']).forEach((v) {
        summaryInfo!.add(new SummaryInfo.fromJson(
            SafeAccess.safeParseMap(v) as Map<String, dynamic>));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.detailInfo != null) {
      data['detailInfo'] = this.detailInfo!.map((v) => v.toJson()).toList();
    }
    if (this.summaryInfo != null) {
      data['summaryInfo'] = this.summaryInfo!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DetailInfo {
  String? mainInfo;
  String? subInfo;

  DetailInfo({this.mainInfo, this.subInfo});

  DetailInfo.fromJson(Map<String, dynamic> json) {
    mainInfo = SafeAccess.safeParseString(json['mainInfo']);
    subInfo = SafeAccess.safeParseString(json['subInfo']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['mainInfo'] = this.mainInfo;
    data['subInfo'] = this.subInfo;
    return data;
  }
}

class SummaryInfo {
  String? icon;
  String? title;

  SummaryInfo({this.icon, this.title});

  SummaryInfo.fromJson(Map<String, dynamic> json) {
    icon = SafeAccess.safeParseString(json['icon']);
    title = SafeAccess.safeParseString(json['title']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['icon'] = this.icon;
    data['title'] = this.title;
    return data;
  }
}

class PackageRuleInfo {
  PackageEnjoyableRights? packageEnjoyableRights;
  PackageGraphicDetail? packageGraphicDetail;
  PackageEnjoyableRights? packagePassengerRule;
  PackageEnjoyableRights? packagePurchaseNotes;
  PackageEnjoyableRights? packageRefundAndChangeRule;
  PackageEnjoyableRights? packageUseRule;

  PackageRuleInfo(
      {this.packageEnjoyableRights,
      this.packageGraphicDetail,
      this.packagePassengerRule,
      this.packagePurchaseNotes,
      this.packageRefundAndChangeRule,
      this.packageUseRule});

  PackageRuleInfo.fromJson(Map<String, dynamic> json) {
    packageEnjoyableRights = json['packageEnjoyableRights'] != null
        ? new PackageEnjoyableRights.fromJson(
            SafeAccess.safeParseMap(json['packageEnjoyableRights'])
                as Map<String, dynamic>)
        : null;
    packageGraphicDetail = json['packageGraphicDetail'] != null
        ? new PackageGraphicDetail.fromJson(
            SafeAccess.safeParseMap(json['packageGraphicDetail'])
                as Map<String, dynamic>)
        : null;
    packagePassengerRule = json['packagePassengerRule'] != null
        ? new PackageEnjoyableRights.fromJson(
            SafeAccess.safeParseMap(json['packagePassengerRule'])
                as Map<String, dynamic>)
        : null;
    packagePurchaseNotes = json['packagePurchaseNotes'] != null
        ? new PackageEnjoyableRights.fromJson(
            SafeAccess.safeParseMap(json['packagePurchaseNotes'])
                as Map<String, dynamic>)
        : null;
    packageRefundAndChangeRule = json['packageRefundAndChangeRule'] != null
        ? new PackageEnjoyableRights.fromJson(
            SafeAccess.safeParseMap(json['packageRefundAndChangeRule'])
                as Map<String, dynamic>)
        : null;
    packageUseRule = json['packageUseRule'] != null
        ? new PackageEnjoyableRights.fromJson(
            SafeAccess.safeParseMap(json['packageUseRule'])
                as Map<String, dynamic>)
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.packageEnjoyableRights != null) {
      data['packageEnjoyableRights'] = this.packageEnjoyableRights!.toJson();
    }
    if (this.packageGraphicDetail != null) {
      data['packageGraphicDetail'] = this.packageGraphicDetail!.toJson();
    }
    if (this.packagePassengerRule != null) {
      data['packagePassengerRule'] = this.packagePassengerRule!.toJson();
    }
    if (this.packagePurchaseNotes != null) {
      data['packagePurchaseNotes'] = this.packagePurchaseNotes!.toJson();
    }
    if (this.packageRefundAndChangeRule != null) {
      data['packageRefundAndChangeRule'] =
          this.packageRefundAndChangeRule!.toJson();
    }
    if (this.packageUseRule != null) {
      data['packageUseRule'] = this.packageUseRule!.toJson();
    }
    return data;
  }
}

class PackageEnjoyableRights {
  Data? data;
  String? tag;
  String? title;

  PackageEnjoyableRights({this.data, this.tag, this.title});

  PackageEnjoyableRights.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null
        ? new Data.fromJson(
            SafeAccess.safeParseMap(json['data']) as Map<String, dynamic>)
        : null;
    tag = SafeAccess.safeParseString(json['tag']);
    title = SafeAccess.safeParseString(json['title']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['tag'] = this.tag;
    data['title'] = this.title;
    return data;
  }
}

class Data {
  List<TableRows>? tableRows;

  Data({this.tableRows});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['tableRows'] != null) {
      tableRows = <TableRows>[];
      SafeAccess.safeParseList(json['tableRows']).forEach((v) {
        tableRows!.add(new TableRows.fromJson(
            SafeAccess.safeParseMap(v) as Map<String, dynamic>));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.tableRows != null) {
      data['tableRows'] = this.tableRows!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

// class TableRows {
//   String? icon;
//   String? mainInfo;
//   String? subInfo;
//
//   TableRows({this.icon, this.mainInfo, this.subInfo});
//
//   TableRows.fromJson(Map<String, dynamic> json) {
//     icon = SafeAccess.safeParseString(json['icon']);
//     mainInfo = SafeAccess.safeParseString(json['mainInfo']);
//     subInfo = SafeAccess.safeParseString(json['subInfo']);
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['icon'] = this.icon;
//     data['mainInfo'] = this.mainInfo;
//     data['subInfo'] = this.subInfo;
//     return data;
//   }
// }

class PackageGraphicDetail {
  String? graphicHtml;

  PackageGraphicDetail({this.graphicHtml});

  PackageGraphicDetail.fromJson(Map<String, dynamic> json) {
    graphicHtml = SafeAccess.safeParseString(json['graphicHtml']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['graphicHtml'] = this.graphicHtml;
    return data;
  }
}

class TableRows {
  String? icon;
  String? mainInfo;
  String? subInfo;
  String? image;

  TableRows({this.icon, this.mainInfo, this.subInfo, this.image});

  TableRows.fromJson(Map<String, dynamic> json) {
    icon = SafeAccess.safeParseString(json['icon']);
    mainInfo = SafeAccess.safeParseString(json['mainInfo']);
    subInfo = SafeAccess.safeParseString(json['subInfo']);
    image = SafeAccess.safeParseString(json['image']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['icon'] = this.icon;
    data['mainInfo'] = this.mainInfo;
    data['subInfo'] = this.subInfo;
    data['image'] = this.image;
    return data;
  }
}

class ToastInfo {
  ToastInfo();

  ToastInfo.fromJson(Map<String, dynamic> json) {}

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    return data;
  }
}

class SkuRoutes {
  // 这个数据结构目前用处不清楚，先这么做，更合理的是都拆成list，不过用不上就算了
  String? arrival;
  String? departure;

  SkuRoutes({this.arrival, this.departure});

  SkuRoutes.fromJson(Map<String, dynamic> json) {
    arrival = SafeAccess.safeParseString(json['arrival']);
    departure = SafeAccess.safeParseString(json['departure']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['arrival'] = this.arrival;
    data['departure'] = this.departure;
    return data;
  }
}

class TravelDates {
  String? journeyName;
  List<String>? travelDate;

  TravelDates({this.journeyName, this.travelDate});

  TravelDates.fromJson(Map<String, dynamic> json) {
    journeyName = SafeAccess.safeParseString(json['journeyName']);
    travelDate = SafeAccess.safeParseList(json['travelDate']).cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['journeyName'] = this.journeyName;
    data['travelDate'] = this.travelDate;
    return data;
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 二次预约说明
class AppointInfoComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<AppointInfoChangeNotifier>.value(
      value: AppointInfoChangeNotifier(context),
      child: const AppointInfoWidget(),
    );
  }
}
import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
class AppointInfoChangeNotifier extends ComponentChangeNotifier {
  AppointInfoChangeNotifier(ComponentContext context) : super(context);
  Map<dynamic, dynamic>? moduleData;
  @override
  void fromJson() {
    //mock数据
    // moduleData = mockData['appoint']['data'];
    // return;
    if (dataModel == null || dataModel['appoint'] == null || dataModel['appoint']['data'] == null) {
      return;
    }
    moduleData = dataModel['appoint']['data'];
  }
}

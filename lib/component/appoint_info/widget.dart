import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../custom_widget/common_text_list_line_vertical.dart';
import '../../utils/common_config.dart';
import 'notifier.dart';
import 'package:flutter_common/api/empty.dart';

class AppointInfoWidget extends StatelessWidget {
  const AppointInfoWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AppointInfoChangeNotifier changeNotifier =
        Provider.of<AppointInfoChangeNotifier>(context);
    changeNotifier.fromJson();
    final Map<dynamic, dynamic>? moduleData = changeNotifier.moduleData;
    if (moduleData == null) {
      return empty;
    }
    return Container(
      margin: const EdgeInsets.only(bottom: itemDivider),
      padding: const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 9),
      decoration: BoxDecoration(
          color: Color(0xffffffff),
          borderRadius: BorderRadius.circular(cardBorderRadius)),
      child: CommonTextListLineVertival(
        moduleData['title'],
        moduleData['descList'],
        textKey: 'mainInfo',
        descKey: 'subInfo',
      ),
    );
  }
}

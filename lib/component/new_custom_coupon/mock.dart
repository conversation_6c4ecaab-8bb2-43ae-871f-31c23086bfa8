const String mockData = '''
{
  "newCustomCoupons": {
    "data": {
      "activityId": "99f84618fab84deca72ef67c90ef9308",
      "backgroundUrl": "https://gw.alicdn.com/imgextra/i4/O1CN01LMMhzH1km65T1OiQJ_!!6000000004725-2-tps-1998-360.png",
      "buttonDesc": "立即领取",
      "conditions": "满66可用",
      "couponAmount": "59",
      "couponType": 10,
      "newCustomVO": {
        "event": {
          "getCoupon": [
            {
              "key": "user_track",
              "params": {
                "trackName": "FliggyItemDetailOmegaNormalClick",
                "trackNamePre": "Button-",
                "trackParams": {
                  "spm": "181.7850105.newcustomcoupondetail.0"
                }
              }
            },
            {
              "key": "click_get_coupon",
              "params": {
                "uuid": "b7fa9f36-3398-4b36-8769-a73668394f6e"
              }
            }
          ]
        }
      },
      "status": 1,
      "subActId": 4887,
      "title": "测试新人券",
      "uuid": "b7fa9f36-3398-4b36-8769-a73668394f6e",
      "validDate": "有效期:2023.05.22-2023.06.30 "
    },
    "tag": "newCustomCoupons"
  }
}
''';

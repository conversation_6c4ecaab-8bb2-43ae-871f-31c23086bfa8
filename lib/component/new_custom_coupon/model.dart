import 'dart:ui';

import 'package:flutter/material.dart';

import '../../utils/common_util.dart';
import '../../utils/safe_access.dart';

class NewCustomCouponModel {
  NewCustomCouponDataModel? data;
  String? tag;

  NewCustomCouponModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null
        ? NewCustomCouponDataModel.fromJson(
            SafeAccess.safeParseMap(json['data']))
        : null;
    tag = SafeAccess.safeParseString(json['tag']);
  }
}

class NewCustomCouponDataModel {
  String? activityId;
  String? backgroundUrl;
  String? floatingLayerDesc;

  Color textColor = Color(0xffffffff);

  String? buttonDesc;
  String? subButtonDesc;
  String? conditions;
  String? couponAmount;
  int? couponType;
  NewCustomVO? newCustomVO;
  int? status;
  int? subActId;
  String? title;
  String? uuid;
  String? validDate;

  NewCustomCouponDataModel.fromJson(Map<String, dynamic> json) {
    activityId = SafeAccess.safeParseString(json['activityId']);
    backgroundUrl = SafeAccess.safeParseString(json['backgroundUrl']);
    floatingLayerDesc = SafeAccess.safeParseString(json['floatingLayerDesc']);
    buttonDesc = SafeAccess.safeParseString(json['buttonDesc']);
    subButtonDesc = SafeAccess.safeParseString(json['subButtonDesc']);
    conditions = SafeAccess.safeParseString(json['conditions']);
    couponAmount = SafeAccess.safeParseString(json['couponAmount']);
    couponType = SafeAccess.safeParseInt(json['couponType']);
    newCustomVO = json['newCustomVO'] != null
        ? NewCustomVO.fromJson(SafeAccess.safeParseMap(json['newCustomVO']))
        : null;
    status = SafeAccess.safeParseInt(json['status']);
    subActId = SafeAccess.safeParseInt(json['subActId']);
    title = SafeAccess.safeParseString(json['title']);
    uuid = SafeAccess.safeParseString(json['uuid']);
    validDate = SafeAccess.safeParseString(json['validDate']);
  }

  ///是否可以点击
  bool get isChecked => status == 1;

  Color get otherTextColor => isChecked ? textColor : stringToColor('99ffffff');
}

class NewCustomVO {
  Event? event;

  NewCustomVO.fromJson(Map<String, dynamic> json) {
    event = json['event'] != null
        ? Event.fromJson(SafeAccess.safeParseMap(json['event']))
        : null;
  }
}

class Event {
  List<GetCoupon>? getCoupon;

  Event.fromJson(Map<String, dynamic> json) {
    if (json['getCoupon'] != null) {
      getCoupon = <GetCoupon>[];
      SafeAccess.safeParseList(json['getCoupon']).forEach((dynamic v) {
        getCoupon!.add(GetCoupon.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
  }
}

class GetCoupon {
  String? key;
  Params? params;

  GetCoupon.fromJson(Map<String, dynamic> json) {
    key = SafeAccess.safeParseString(json['key']);
    params = json['params'] != null
        ? Params.fromJson(SafeAccess.safeParseMap(json['params']))
        : null;
  }
}

class Params {
  String? trackName;
  String? trackNamePre;
  TrackParams? trackParams;
  String? uuid;

  Params.fromJson(Map<String, dynamic> json) {
    trackName = SafeAccess.safeParseString(json['trackName']);
    trackNamePre = SafeAccess.safeParseString(json['trackNamePre']);
    trackParams = json['trackParams'] != null
        ? TrackParams.fromJson(SafeAccess.safeParseMap(json['trackParams']))
        : null;
    uuid = SafeAccess.safeParseString(json['uuid']);
  }
}

class TrackParams {
  String? spm;

  TrackParams.fromJson(Map<String, dynamic> json) {
    spm = SafeAccess.safeParseString(json['spm']);
  }
}

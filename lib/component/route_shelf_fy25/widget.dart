import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class RouteShelfFy25Widget extends StatelessWidget {
  const RouteShelfFy25Widget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RouteShelfFy25ChangeNotifier changeNotifier =
        Provider.of<RouteShelfFy25ChangeNotifier>(context);
    return Container();
  }
}
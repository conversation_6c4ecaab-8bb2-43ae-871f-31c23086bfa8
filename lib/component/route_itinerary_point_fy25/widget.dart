import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class RouteItineraryPointFy25Widget extends StatelessWidget {
  const RouteItineraryPointFy25Widget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RouteItineraryPointFy25ChangeNotifier changeNotifier =
        Provider.of<RouteItineraryPointFy25ChangeNotifier>(context);
    return Container();
  }
}
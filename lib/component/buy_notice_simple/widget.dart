import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../custom_widget/detail_arrow.dart';
import '../../utils/TextConfig.dart';
import '../../utils/common_config.dart';
import 'notifier.dart';
import 'package:flutter_common/api/empty.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:fround_image/fround_image.dart';

class BuyNoticeSimpleWidget extends StatelessWidget {
  const BuyNoticeSimpleWidget({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final BuyNoticeSimpleChangeNotifier changeNotifier =
        Provider.of<BuyNoticeSimpleChangeNotifier>(context);
    if (changeNotifier.itemDetailModel.buyCommonNoticeModel == null||changeNotifier.itemDetailModel.buyCommonNoticeModel!.isEmpty) {
      return SizedBox.shrink();
    }
    return Container(
      padding: const EdgeInsets.only( bottom: paddingBottom, left: paddingLeft, right: paddingRight),
      margin: const EdgeInsets.only( bottom: paddingBottom),
      decoration: BoxDecoration(
          color: const Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(cardBorderRadius)),
      child: _buildItems(context, changeNotifier.itemDetailModel.buyCommonNoticeModel!, changeNotifier),
    );
  }

  Widget _buildItems(BuildContext context, List<dynamic> items,
      BuyNoticeSimpleChangeNotifier changeNotifier) {
    final List<Widget> result = <Widget>[];
    if (items.isEmpty) {
      return Container();
    }
    for (int i = 0; i < items.length; i++) {
      final List<Widget> singleNotice = <Widget>[];

      final Map<dynamic, dynamic> item = items[i];
      if (item.isEmpty) {
        continue;
      }
      final String? type = item['type'];
      final String? key = item['key'];
      final String? spmD = item['spmD'];
      final String? title = item['title'];
      final String? desc = item['desc'] is String ? item['desc'] : null;
      final bool? overflow = item['overflow'];
      final bool? rich = item['rich'];
      final bool? multi = item['multi'];
      final bool? disableTitleClick = item['disableTitleClick'];
      final String? openUrl = item['floatUrl'];
      final String? defaultTitle = item['defaultTitle'];
      final Map<dynamic, dynamic>? onClick = item['onClick'];
      if (title == null) {
        continue;
      }
      // final BlockTitle blockTitle = BlockTitle(
      //   title,
      //   onButtonClick: disableTitleClick != null && disableTitleClick == true
      //       ? null
      //       : () {
      //           final String itemId =
      //               changeNotifier.dataModel!['item']['itemId'].toString();
      //           final String spmCD = 'buy_notice.$spmD';
      //           changeNotifier.ctrlClicked(context, spmCD, 'buyNotice', <String, String>{});
      //           changeNotifier.jumpToFloatUrl(context,
      //               url: openUrl,
      //               key: key,
      //               hideTitle: items.length == 1,
      //               itemId: itemId);
      //         },
      // );
      final Widget titleWidget = GestureDetector(
        onTap: disableTitleClick != null && disableTitleClick == true
            ? null
            : () {
          final String itemId =
          changeNotifier.dataModel!['item']['itemId'].toString();
          final String spmCD = 'buy_notice.$spmD';
          changeNotifier.ctrlClicked(context, spmCD, 'buyNotice', <String, String>{});
          changeNotifier.jumpToFloatUrl(context,
              url: openUrl,
              key: key,
              hideTitle: items.length == 1,
              itemId: itemId);
        },
        child: Container(
          color: const Color(0x00FFFFFF),
          padding: EdgeInsets.only(top: 12),
          child: Row(
            children: <Widget>[

              Text(title,style: TextStyle(
                fontWeight: FontWeightExt.bold,
                fontSize: 14
              ),),
              const Spacer(),
              if(disableTitleClick == null || !disableTitleClick)
                rightArrowBig
            ],
          ),
        ),
      );
      singleNotice.add(titleWidget);
      if (desc != null) {
        //单行文本溢出控制：&__overflow 类用来处理单行文本溢出，文本在一行显示，不换行，并在溢出时显示省略号。
        //多行文本溢出控制：&__multi 类用来处理多行文本溢出，将文本限制在最多5行显示，多于5行的文本部分会被省略号取代。
        //富文本溢出控制：&__rich 全部显示。
        final int maxLines;
        if (rich != null && rich == true) {
          maxLines = 999;
          //使用富文本组件
          final Widget descText = _buildRichText(context, desc, onClick);
          singleNotice.add(descText);
        } else {
          if (multi != null && multi == true) {
            maxLines = 5;
          } else if (overflow != null && overflow == true) {
            maxLines = 1;
          } else {
            maxLines = 999;
          }
          final Text descText = Text(
            desc,
            overflow: TextOverflow.ellipsis,
            maxLines: maxLines,
            style: const TextStyle(
                color: Color(0xFF5C5F66), fontSize: 13.0, height: 1.3),
          );
          singleNotice.add(GestureDetector(
              onTap: () {
                final String itemId =
                    changeNotifier.dataModel!['item']['itemId'].toString();
                final String spmCD = 'buy_notice.$spmD';
                changeNotifier.ctrlClicked(context, spmCD, 'buyNotice', <String, String>{});
                changeNotifier.jumpToFloatUrl(context,
                    url: openUrl,
                    key: key,
                    hideTitle: items.length == 1,
                    itemId: itemId);
              },
              child: Container(padding: const EdgeInsets.only(top: 6.0), child: descText)));
        }
      }
      //添加下划线,最后一个不加
      if (i < items.length - 1) {
        singleNotice.add(Container(
        margin: const EdgeInsets.only(top: 12.0),
        height: 0.5,
        color: const Color(0xFFD8D8D8),
      )); 
      }
      result.add(Column(
          children: singleNotice,
          crossAxisAlignment: CrossAxisAlignment.start));
    }
    return Column(
      children: result,
    );
  }

  Widget _buildRichText(
      BuildContext context, String text, Map<dynamic, dynamic>? clickMap) {
    final RegExp regExp = RegExp(r'\{\{(.*?)\}\}');
    final Iterable<RegExpMatch> matches = regExp.allMatches(text);

    final List<TextSpan> spans = <TextSpan>[];
    int start = 0;

    for (final RegExpMatch match in matches) {
      if (match.start > start) {
        spans.add(TextSpan(text: text.substring(start, match.start)));
      }
      final String variableName = match.group(1)!;
      if (variableName != '') {
        spans.add(
          TextSpan(
              text: variableName,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                  color: Color(0xff6666ff), fontSize: 13.0, height: 1.3),
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  final String? tapUrl = clickMap?[variableName];
                  if (tapUrl != null) {
                    FliggyNavigatorApi.getInstance().push(context, tapUrl,anim: Anim.slide);
                  }
                }),
        );
      } else {
        spans.add(TextSpan(
          text: '{{$variableName}}',
          style: const TextStyle(
              color: Color(0xFF5C5F66), fontSize: 13.0, height: 1.3),
        ));
      }
      start = match.end;
    }

    if (start < text.length) {
      spans.add(TextSpan(text: text.substring(start)));
    }

    return Container(
      padding: const EdgeInsets.only(top: 6.0),
      child: RichText(
        text: TextSpan(
          children: spans,
          style: const TextStyle(color: Color(0xFF5C5F66), fontSize: 13.0, height: 1.3), // Default text color
        ),
      ),
    );
  }
}

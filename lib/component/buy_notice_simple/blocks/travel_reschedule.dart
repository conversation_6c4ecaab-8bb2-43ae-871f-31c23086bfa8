import 'notice_base.dart';
class TravelReschedule extends NoticeBase{
  @override
  Map <dynamic, dynamic> getData(Map <dynamic, dynamic> data) {
    final String? travelReschedule = data['travelReschedule']['data']['keyValueList'][0]['valueList'][0];
    if (travelReschedule != null) {
      return <dynamic, dynamic>{
      'key': 'travelReschedule',
      'spmD': 'travel_reschedule',
      'title': data['travelReschedule']?['data']?['title'] ?? '改期说明',
      'overflow': true,
      'desc': travelReschedule,
      'floatUrl': data['travelReschedule']?['data']?['floatUrl']
    }; 
    }
    return <dynamic, dynamic>{};
  }
}
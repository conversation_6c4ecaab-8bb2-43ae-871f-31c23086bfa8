import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class VisaProcedureWidget extends StatelessWidget {
  const VisaProcedureWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VisaProcedureChangeNotifier changeNotifier =
        Provider.of<VisaProcedureChangeNotifier>(context);
    return Container();
  }
}
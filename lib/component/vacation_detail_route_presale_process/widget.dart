import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class VacationDetailRoutePresaleProcessWidget extends StatelessWidget {
  const VacationDetailRoutePresaleProcessWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VacationDetailRoutePresaleProcessChangeNotifier changeNotifier =
        Provider.of<VacationDetailRoutePresaleProcessChangeNotifier>(context);
    return Container();
  }
}
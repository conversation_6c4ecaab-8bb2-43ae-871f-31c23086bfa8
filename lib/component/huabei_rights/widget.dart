import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class HuabeiRightsWidget extends StatelessWidget {
  const HuabeiRightsWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final HuabeiRightsChangeNotifier changeNotifier =
        Provider.of<HuabeiRightsChangeNotifier>(context);
    return Container();
  }
}
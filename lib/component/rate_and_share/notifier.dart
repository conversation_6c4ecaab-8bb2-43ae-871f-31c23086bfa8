import 'package:flutter/cupertino.dart';

import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import 'package:fliggy_router/fliggy_router.dart';
import '../../utils/share_utils.dart';
import 'model.dart';

class RateAndShareChangeNotifier extends ComponentChangeNotifier {
  RateAndShareDataModel? get rateAndShareDataModel =>
      itemDetailModel.rateAndShareDataModel;

  bool get visible =>
      !itemDetailModel.fy25up &&
      rateAndShareDataModel != null &&
      itemDetailModel.itemModel?.itemId != null;

  RateAndShareChangeNotifier(ComponentContext context) : super(context);

  ///评价点击
  void commentClick(BuildContext context) {
    if (rateAndShareDataModel != null &&
        rateAndShareDataModel!.score != null &&
        rateAndShareDataModel!.commentUrl!.isNotEmpty) {
      FliggyNavigatorApi.getInstance()
          .push(context, rateAndShareDataModel!.commentUrl!, anim: Anim.slide);
    }
  }

  ///分享点击
  void shareClick(BuildContext context) {
    ShareUtil.shareClick(context, itemDetailModel);
  }
}

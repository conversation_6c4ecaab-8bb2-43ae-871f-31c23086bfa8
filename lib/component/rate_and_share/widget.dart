import 'package:ficonfont/ficonfont.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../custom_widget/carousel_slider/carousel_slider.dart';
import '../../custom_widget/null_widget.dart';
import '../../utils/common_config.dart';
import '../../utils/common_util.dart';
import 'model.dart';
import 'notifier.dart';

class RateAndShareWidget extends StatelessWidget {
  const RateAndShareWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RateAndShareChangeNotifier changeNotifier =
        Provider.of<RateAndShareChangeNotifier>(context);
    final RateAndShareDataModel? rateAndShareDataModel =
        changeNotifier.rateAndShareDataModel;
    return !changeNotifier.visible || rateAndShareDataModel == null
        ? nullWidget
        : Container(
            color: Color(0xffffffff),
            padding: const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 0),
            child: Column(
              children: <Widget>[
                if (rateAndShareDataModel.shareContent != null)
                  Container(
                    margin: const EdgeInsets.fromLTRB(0, 12, 0, 0),
                    height: 39.00,
                    decoration: const BoxDecoration(
                      image: DecorationImage(
                          image: NetworkImage(
                              'https://gw.alicdn.com/imgextra/i3/O1CN01UjnEnE26g4cm8QAqG_!!6000000007690-2-tps-1332-156.png'),
                          fit: BoxFit.fill),
                    ),
                    child:
                        _buildShareContent(rateAndShareDataModel.shareContent!),
                  ),
                Container(
                    margin: const EdgeInsets.fromLTRB(0, 12, 0, 0),
                    height: 18.00,
                    child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          if (rateAndShareDataModel.comment != null &&
                              rateAndShareDataModel.score != null)
                            _buildItemBtn(
                                ICON_XIHUAN1, rateAndShareDataModel.score!, () {
                              changeNotifier.ctrlClicked(context, 'basicInfo.simple_rate', 'basicInfoScore', <String, String>{});
                              changeNotifier.commentClick(context);
                            }),
                          _buildItemBtn(ICON_PINGLUN1,
                              rateAndShareDataModel.comment ?? '期待您留下宝贵的评价',
                              () {
                            final String spmCD = (rateAndShareDataModel.comment == null) ? 'basicInfo.empty_rate' : 'basicInfo.simple_rate';
                            changeNotifier.ctrlClicked(context, spmCD, 'basicInfoRate', <String, String>{});
                            changeNotifier.commentClick(context);
                          }),
                          _buildItemBtn(ICON_FENXIANGXIAO, '分享', () {
                            changeNotifier.ctrlClicked(context, 'basicInfo.share', 'basicInfoShare', <String, String>{});
                            changeNotifier.shareClick(context);
                          }),
                        ])),
              ],
            ),
          );
  }

  Widget _buildShareContent(ShareContent shareContent) {
    return Container(
      margin: const EdgeInsets.fromLTRB(12, 7, 0, 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            height: 14,
            margin: const EdgeInsets.fromLTRB(0, 1, 0, 0),
            child: CarouselSlider(
              items: shareContent.tagList!.map((TagList text) {
                return Builder(
                  builder: (BuildContext context) {
                    return Text(text.tagName!,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                            color: Color.fromARGB(255, 255, 57, 77),
                            fontSize: 12.0));
                  },
                );
              }).toList(),
              options: CarouselOptions(
                autoPlay: true,
                aspectRatio: 80 / 14,
                viewportFraction: 1,
                scrollDirection: Axis.vertical,
                autoPlayInterval: const Duration(seconds: 2),
                scrollPhysics: NeverScrollableScrollPhysics(),
              ),
            ),
          ),
          Expanded(
            child: Container(
              margin: const EdgeInsets.fromLTRB(9, 0, 9, 0),
              child: Text(shareContent.desc!,
                  textAlign: TextAlign.left,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                      color: Color.fromARGB(255, 15, 19, 26), fontSize: 12.00)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemBtn(int icon, String title, Function action) {
    return Expanded(
        child: GestureDetector(
      onTap: () {
        action.call();
      },
      child:
          Row(mainAxisAlignment: MainAxisAlignment.center, children: <Widget>[
        Ficon(icon, 13.00, const Color.fromARGB(255, 145, 148, 153)),
        Container(
          margin: const EdgeInsets.fromLTRB(6, 0, 0, 0),
          child: Text(title,
              textAlign: TextAlign.left,
              style: const TextStyle(
                  color: Color.fromARGB(255, 145, 148, 153), fontSize: 12.00)),
        )
      ]),
    ));
  }
}

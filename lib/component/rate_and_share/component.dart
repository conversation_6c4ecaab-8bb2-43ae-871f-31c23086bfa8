import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 分享&评价
class RateAndShareComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<RateAndShareChangeNotifier>.value(
      value: RateAndShareChangeNotifier(context),
      child: const RateAndShareWidget(),
    );
  }
}
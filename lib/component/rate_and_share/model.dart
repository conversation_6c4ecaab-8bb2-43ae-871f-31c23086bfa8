import 'package:ficonfont/ficonfont.dart';
import '../../utils/common_util.dart';
import '../../utils/safe_access.dart';
class RateAndShareDataModel {
  String? comment;
  String? commentUrl;
  String? score;
  ShareContent? shareContent;
  ShareInfo? shareInfo;

  RateAndShareDataModel.fromJson(Map<String, dynamic> dataModel) {
   final Map<String,dynamic>?  share = dataModel['share'];
   final Map<String,dynamic>?  json = share?['data'];
    comment = json?['comment'];
    commentUrl = SafeAccess.safeParseString(json?['commentUrl']);
    score = json?['score'];
    shareContent = json?['shareContent'] != null
        ? ShareContent.fromJson(SafeAccess.safeParseMap(json?['shareContent']))
        : null;
    shareInfo = json?['shareInfo'] != null
        ? ShareInfo.fromJson(SafeAccess.safeParseMap(json?['shareInfo']))
        : null;
  }
}

class ShareContent {
  String? desc;
  List<TagList>? tagList;

  ShareContent.fromJson(Map<String, dynamic> json) {
    desc = SafeAccess.safeParseString(json['desc']);
    if (json['tagList'] != null) {
      tagList = <TagList>[];
      SafeAccess.safeParseList(json['tagList']).forEach((dynamic v) {
        tagList!.add(TagList.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
  }
}

class TagList {
  String? tagName;
  TagList.fromJson(Map<String, dynamic> json) {
    tagName = SafeAccess.safeParseString(json['tagName']);
  }
}

class ShareInfo {
  String? imageUrl;
  String? shareIconFont;
  String? textContext;
  String? titleContext;
  String? urlContent;

  ShareInfo.fromJson(Map<String, dynamic> json) {
    imageUrl = SafeAccess.safeParseString(json['image_url']);
    shareIconFont = SafeAccess.safeParseString(json['shareIconFont']);
    textContext = SafeAccess.safeParseString(json['text_context']);
    titleContext = SafeAccess.safeParseString(json['title_context']);
    urlContent = SafeAccess.safeParseString(json['url_content']);
  }
}


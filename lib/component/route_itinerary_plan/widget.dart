import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class RouteItineraryPlanWidget extends StatelessWidget {
  const RouteItineraryPlanWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RouteItineraryPlanChangeNotifier changeNotifier =
        Provider.of<RouteItineraryPlanChangeNotifier>(context);
    return Container();
  }
}
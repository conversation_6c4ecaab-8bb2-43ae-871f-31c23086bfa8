import 'dart:convert';

import 'package:fbridge/fbridge.dart';
import 'package:flutter/cupertino.dart';

import '../../render/component/component_change_notifier.dart';
import 'package:fliggy_router/fliggy_router.dart';
import '../../render/component/component_context.dart';
import 'model.dart';

class RichContentChangeNotifier extends ComponentChangeNotifier {
  RichContentModel? get richContentModel => itemDetailModel.richContentModel;

  RichContentDataModel? get richContentDataModel => richContentModel?.data;

  String? get graphicDetailHtml => richContentModel?.graphicDetailHtml;

  String? get graphicDetailExtHtml => richContentModel?.graphicDetailExtHtml;

  String? get graphicDetailTitle => richContentModel?.graphicDetailTitle;

  String? get graphicDetailExtTitle => richContentModel?.graphicDetailExtTitle;

  String? get decorateActivityTitle => richContentModel?.decorateActivityTitle;

  String? get decorateActivityPic => richContentModel?.decorateActivityPic;

  String? get decorateActivityJumpUrl =>
      richContentModel?.decorateActivityJumpUrl;

  RichContentChangeNotifier(ComponentContext context) : super(context);

  ///跳转店铺活动
  void activityClick(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      FliggyNavigatorApi.getInstance().push(context, url);
    }
  }

  ///查看大图
  void picClick(BuildContext context, String url) { // , double ratio
    final List<Map<String, dynamic>> browseList = <Map<String, dynamic>>[];
    browseList.add(<String, dynamic>{'url': url});

    // FliggyNavigatorApi.getInstance().push(context,
    //     'page://flutter_view/fliggy_item_detail/image_preview_page?_fli_background_transparent=true&flutter_path=/fliggy_item_detail/image_preview_page/_fli_inner_router_=true',
    //     params: <String, dynamic>{'photos': jsonEncode(browseList), 'initIndex': '0','anime_type':3,'ratio': ratio},anim: Anim.none);


    FBridgeApi.newInstance(context).callSafe(
      'image_browse',
      <String, dynamic>{'photos': browseList, 'index': '0','anime_type':3},
    );
  }
}

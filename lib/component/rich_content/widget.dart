import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:provider/provider.dart';
import 'package:fround_image/fround_image.dart';
import '../../custom_widget/block_title.dart';
import '../../custom_widget/null_widget.dart';
import '../../utils/common_config.dart';
import '../../utils/safe_access.dart';
import '../component_key_constant.dart';
import 'model.dart';
import 'notifier.dart';
import 'package:fliggy_router/fliggy_router.dart';
class RichContentWidget extends StatelessWidget {
  const RichContentWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double imageWidth =
        MediaQuery.of(context).size.width - pageMarginLeft - pageMarginRight;
    final RichContentChangeNotifier changeNotifier =
        Provider.of<RichContentChangeNotifier>(context);
    final RichContentDataModel? richContentDataModel =
        changeNotifier.richContentDataModel;
    return richContentDataModel == null &&
            changeNotifier.graphicDetailHtml == null &&
            changeNotifier.graphicDetailExtHtml == null &&
            changeNotifier.decorateActivityPic == null
        ? nullWidget
        : Container(
            margin: const EdgeInsets.only(bottom: itemDivider),
            padding: const EdgeInsets.only(bottom: paddingBottom),
            decoration: BoxDecoration(
                color: Color(0xffffffff),
                borderRadius: BorderRadius.circular(cardBorderRadius)),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  //tab滚动位置记录
                  Container(
                    key: changeNotifier.itemDetailModel.titleBarModel
                        ?.tabGlobalKey[ComponentKeyConstant.richContent],
                  ),
                  if (richContentDataModel != null)
                    _buildPics(context, changeNotifier, richContentDataModel,
                        imageWidth),
                  if (changeNotifier.graphicDetailHtml != null)
                    _buildHtml(
                        context,
                        changeNotifier,
                        changeNotifier.graphicDetailHtml!,
                        changeNotifier.graphicDetailTitle!,
                        imageWidth),
                  if (changeNotifier.graphicDetailExtHtml != null)
                    _buildHtml(
                        context,
                        changeNotifier,
                        changeNotifier.graphicDetailExtHtml!,
                        changeNotifier.graphicDetailExtTitle!,
                        imageWidth),
                  if (changeNotifier.decorateActivityPic != null)
                    _buildActivity(context, changeNotifier),
                ]));
  }


  Widget _buildPics(
      BuildContext context,
      RichContentChangeNotifier changeNotifier,
      RichContentDataModel richContentDataModel,
      double imageWidth) {
    return Column(crossAxisAlignment: CrossAxisAlignment.start, children:
    <Widget>[
      Container(
          padding: const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 0),
          child: BlockTitle(
            '图文介绍',
          )),
      ListView.builder(
        padding: EdgeInsets.zero,
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: richContentDataModel.pics?.length??0,
        itemBuilder: (BuildContext context, int index) {
          final Pics? pics = richContentDataModel.pics?[index];
          final double imageHeight =
              SafeAccess.safeParseDouble(pics?.styles?.size!.width) != 0.0
                  ? SafeAccess.safeParseDouble(pics?.styles?.size!.height) *
                      imageWidth /
                      SafeAccess.safeParseDouble(pics?.styles?.size!.width)
                  : 0.0;

          if (pics == null || pics.picUrl == '') {
            return Container();
          }

          final String spmCD = 'detailDecoration.fliggy_desc_single_image_$index';
          final String controlName = 'fliggy_desc_single_image_$index';

          final Widget widget = imageHeight != 0.0
              ? GestureDetector(
                  onTap: () {
                    changeNotifier.ctrlClicked(context, spmCD, controlName, null);
                    if (pics.gotoPageUrl != null) {
                      // 跳转
                      FliggyNavigatorApi.getInstance().push(context, pics.gotoPageUrl!);
                    } else {
                      // 查看大图
                      changeNotifier.picClick(context, pics.picUrl!);
                    }

                  },
                  child: SizedBox(
                    width: imageWidth,
                    height: imageHeight,
                    child: CachedNetworkImage(imageUrl: pics.picUrl!, fit: BoxFit.cover,fadeInDuration: Duration(milliseconds: 50),),
                  ),
                )
              : CachedNetworkImage(imageUrl: pics.picUrl!,
                  width: imageWidth, fit: BoxFit.fitWidth);

          return widget;
        },
      ),
    ]);
  }

  Widget _buildHtml(
      BuildContext buildContext,
      RichContentChangeNotifier changeNotifier,
      String html,
      String title,
      double imageWidth) {
    //过滤标签里面的空行
    String updatedHtml = html.replaceAll('&nbsp;', '').replaceAll('<br/>', '');
    // 用正则表达式替换 div 的 height。有的div高度设置很大，导致大片留白
     updatedHtml = updatedHtml.replaceAllMapped(
        RegExp(r'(<div[^>]*style="[^"]*?)(height:\s*\d+\.?\d*px;)([^>]*>)'),
            (dynamic match) {
          return '${match[1]}height: auto;${match[3]}';
        });
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Container(
            padding:const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 0),
            child: BlockTitle(
              title,
            )),
        Html(
          data: updatedHtml,
          style: <String, Style>{
            // 'body': Style(
            //   margin: EdgeInsets.zero,
            //   padding: EdgeInsets.zero,
            // ),
            'p': Style(
              margin: EdgeInsets.zero,
              padding: EdgeInsets.zero,
            ),
          },
          customRender: <String, CustomRender>{
            'img': (RenderContext context, Widget child) {

              return GestureDetector(
                onTap: () {
                  changeNotifier.ctrlClicked(buildContext, 'graphicDetail.graphicDetail_click', 'graphicDetail_picClick', null);
                  changeNotifier.picClick(
                      buildContext, context.tree.attributes['src']!);
                },
                child: context.tree.attributes['src'] != null
                    ? CachedNetworkImage(
                  imageUrl: context.tree.attributes['src']!, // 获取图片的'src'属性值
                  fadeInDuration: Duration(milliseconds: 50),
                  // width: imageWidth,
                  // fit: BoxFit.fitWidth,
                  // loadingBuilder: (BuildContext context, Widget child,
                  //     ImageChunkEvent? loadingProgress) {
                  //   if (loadingProgress == null) {
                  //     return child; // 图片已加载完成
                  //   }
                  //   return nullWidget; // 图片正在加载时的占位符
                  // },
                  // errorBuilder: (
                  //     BuildContext context,
                  //     Object error,
                  //     StackTrace? stackTrace,
                  // ) {
                  //   return nullWidget;
                  // },
                )
                    : nullWidget,
              );
            },
          },
        ),
      ],
    );
  }

  Widget _buildActivity(
      BuildContext context, RichContentChangeNotifier changeNotifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Container(
            padding: const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 0),
            child: BlockTitle(changeNotifier.decorateActivityTitle ?? '')),
        GestureDetector(
          onTap: () {
            changeNotifier.activityClick(
                context, changeNotifier.decorateActivityJumpUrl);
          },
          child: CachedNetworkImage(imageUrl: changeNotifier.decorateActivityPic!,
              fadeInDuration: Duration(milliseconds: 50),
              fit: BoxFit.cover),
        ),
      ],
    );
  }
}


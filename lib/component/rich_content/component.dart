import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 货架
class RichContentComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<RichContentChangeNotifier>.value(
      value: RichContentChangeNotifier(context),
      child: const RichContentWidget(),
    );
  }
}
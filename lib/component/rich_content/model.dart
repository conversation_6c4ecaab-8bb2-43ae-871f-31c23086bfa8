import '../../utils/safe_access.dart';

class RichContentModel {
  ///标准化 图片
  RichContentDataModel? data;
  ///非标准化 html标签
  String? graphicDetailHtml;
  ///非标准化 额外补充html标签
  String? graphicDetailExtHtml;
  ///非标准化 html标签标题
  String? graphicDetailTitle;
  ///非标准化 额外补充html标签标题
  String? graphicDetailExtTitle;
  ///店铺活动标题
  String? decorateActivityTitle;
  ///店铺活动
  String? decorateActivityPic;
  ///店铺活动跳转链接
  String? decorateActivityJumpUrl;
  ///店铺装修，图文介绍
  Map<String, dynamic>? decorateActivityMap;

  RichContentModel.fromJson(Map<String, dynamic> dataModel) {
    final Map<String, dynamic>? decorateGraphic = dataModel['decorateGraphic'];
    data = decorateGraphic?['data'] != null
        ? RichContentDataModel.fromJson(
            SafeAccess.safeParseMap(decorateGraphic?['data']))
        : null;
    graphicDetailHtml ??=
        SafeAccess.safeParseMap(dataModel['graphicDetail'])['graphicHtml'];
    graphicDetailExtHtml ??=
        SafeAccess.safeParseMap(dataModel['graphicDetailExt'])['graphicHtml'];
    graphicDetailTitle ??=
        SafeAccess.safeParseMap(dataModel['graphicDetail'])['title'] ?? '图文介绍';
    graphicDetailExtTitle ??=
        SafeAccess.safeParseMap(dataModel['graphicDetailExt'])['title'] ??
            '图文介绍';
    decorateActivityMap ??= SafeAccess.safeParseMap(SafeAccess.safeParseMap(
        dataModel['decorateActivity'])['data'])['model'];
    decorateActivityTitle =
        SafeAccess.safeParseMap(decorateActivityMap?['componentTitle'])['text'];
    decorateActivityPic = decorateActivityMap?['actPicUrl'];
    decorateActivityJumpUrl = decorateActivityMap?['jumpUrl'];
  }
}

class RichContentDataModel {
  List<Pics>? pics;

  RichContentDataModel.fromJson(Map<String, dynamic> json) {
    if (json['pics'] != null) {
      pics = <Pics>[];
      SafeAccess.safeParseList(json['pics']).forEach((dynamic v) {
        pics!.add(Pics.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
  }
}

class Pics {
  FliggyActions? fliggyActions;
  String? index;
  String? key;
  String? picUrl;
  Styles? styles;

  String? gotoPageUrl;

  Pics.fromJson(Map<String, dynamic> json) {
    fliggyActions = json['fliggyActions'] != null
        ? FliggyActions.fromJson(SafeAccess.safeParseMap(json['fliggyActions']))
        : null;
    index = SafeAccess.safeParseString(json['index']);
    key = SafeAccess.safeParseString(json['key']);
    picUrl = SafeAccess.safeParseString(json['picUrl']);
    styles = json['styles'] != null
        ? Styles.fromJson(SafeAccess.safeParseMap(json['styles']))
        : null;

    if (json.containsKey('hotAreaChildren')) {
      final List<dynamic> list = SafeAccess.safeParseList(json['hotAreaChildren']);
      final Map<dynamic, dynamic> map = list[0];
      if (map.containsKey('fliggyActions')) {
        final Map<dynamic, dynamic> fliggyActionsMap = SafeAccess.safeParseMap(map['fliggyActions']);
        if (fliggyActionsMap.containsKey('gotoPage')) {
          gotoPageUrl = SafeAccess.safeParseString(SafeAccess.safeParseMap(fliggyActionsMap['gotoPage'])['params']?['url']);
        }
      }
    }
  }
}

class FliggyActions {
  UtOpenBigPic? utOpenBigPic;

  FliggyActions.fromJson(Map<String, dynamic> json) {
    utOpenBigPic = json['utOpenBigPic'] != null
        ? UtOpenBigPic.fromJson(SafeAccess.safeParseMap(json['utOpenBigPic']))
        : null;
  }
}

class UtOpenBigPic {
  String? iD;
  String? id;
  String? key;
  Params? params;

  UtOpenBigPic.fromJson(Map<String, dynamic> json) {
    iD = SafeAccess.safeParseString(json['ID']);
    id = SafeAccess.safeParseString(json['id']);
    key = SafeAccess.safeParseString(json['key']);
    params = json['params'] != null
        ? Params.fromJson(SafeAccess.safeParseMap(json['params']))
        : null;
  }
}

class Params {
  String? trackName;
  String? trackNamePre;
  TrackParams? trackParams;

  Params.fromJson(Map<String, dynamic> json) {
    trackName = SafeAccess.safeParseString(json['trackName']);
    trackNamePre = SafeAccess.safeParseString(json['trackNamePre']);
    trackParams = json['trackParams'] != null
        ? TrackParams.fromJson(SafeAccess.safeParseMap(json['trackParams']))
        : null;
  }
}

class TrackParams {
  String? spm;

  TrackParams.fromJson(Map<String, dynamic> json) {
    spm = SafeAccess.safeParseString(json['spm']);
  }
}

class Styles {
  Size? size;

  Styles.fromJson(Map<String, dynamic> json) {
    size = json['size'] != null
        ? Size.fromJson(SafeAccess.safeParseMap(json['size']))
        : null;
  }
}

class Size {
  double? height;
  double? width;

  Size.fromJson(Map<String, dynamic> json) {
    height = SafeAccess.safeParseDouble(json['height']);
    width = SafeAccess.safeParseDouble(json['width']);
  }
}

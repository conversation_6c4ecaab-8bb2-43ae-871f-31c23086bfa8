import '../../utils/safe_access.dart';

class BillionServiceModel {
  bool get visible => detailInfo != null;
  DetailInfo? detailInfo;
  String? popWindowUrl;
  String? icon;
  String? text;
  String? backgroundColor;

  BillionServiceModel.fromJson(Map<String, dynamic> dataModel) {
    final Map<String, dynamic> billionService =
        SafeAccess.safeParseMap(dataModel['billionService']);
    final Map<String, dynamic> json =
        SafeAccess.safeParseMap(billionService['data']);
    detailInfo = json['detailInfo'] != null
        ? DetailInfo.fromJson(SafeAccess.safeParseMap(json['detailInfo']))
        : null;
    popWindowUrl = SafeAccess.safeParseString(json['popWindowUrl']);
    icon = SafeAccess.safeParseString(json['icon']);
    text = SafeAccess.safeParseString(json['text']);
    backgroundColor = SafeAccess.safeParseString(json['backgroundColor']);
  }
}

class DetailInfo {
  String? backgroundColor;
  String? backgroundImage;
  String? closeIcon;
  List<DetailList>? detailList;
  String? title;
  String? titleIcon;

  DetailInfo.fromJson(Map<String, dynamic> json) {
    backgroundColor = SafeAccess.safeParseString(json['backgroundColor']);
    backgroundImage = SafeAccess.safeParseString(json['backgroundImage']);
    closeIcon = SafeAccess.safeParseString(json['closeIcon']);
    if (json['detailList'] != null) {
      detailList = <DetailList>[];
      SafeAccess.safeParseList(json['detailList']).forEach((dynamic v) {
        detailList!.add(DetailList.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    title = SafeAccess.safeParseString(json['title']);
    titleIcon = SafeAccess.safeParseString(json['titleIcon']);
  }
}

class DetailList {
  String? desc;
  String? icon;
  String? title;
  List<SubDetailList>? subDetailList;

  DetailList.fromJson(Map<String, dynamic> json) {
    desc = SafeAccess.safeParseString(json['desc']);
    icon = SafeAccess.safeParseString(json['icon']);
    title = SafeAccess.safeParseString(json['title']);
    if (json['subDetailList'] != null) {
      subDetailList = <SubDetailList>[];
      SafeAccess.safeParseList(json['subDetailList']).forEach((dynamic v) {
        subDetailList!.add(SubDetailList.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
  }
}

class SubDetailList {
  String? desc;
  String? title;

  SubDetailList.fromJson(Map<String, dynamic> json) {
    desc = SafeAccess.safeParseString(json['desc']);
    title = SafeAccess.safeParseString(json['title']);
  }
}

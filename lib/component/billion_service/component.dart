import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 十亿保障
class BillionServiceComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<BillionServiceChangeNotifier>.value(
      value: BillionServiceChangeNotifier(context),
      child: const BillionServiceWidget(),
    );
  }
}
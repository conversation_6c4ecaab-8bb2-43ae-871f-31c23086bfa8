import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fround_image/fround_image.dart';
import '../../custom_widget/detail_arrow.dart';
import '../../custom_widget/null_widget.dart';
import '../../utils/common_config.dart';
import 'model.dart';
import 'notifier.dart';

class BillionServiceWidget extends StatelessWidget {
  const BillionServiceWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final BillionServiceChangeNotifier changeNotifier =
        Provider.of<BillionServiceChangeNotifier>(context);
    final BillionServiceModel? billionServiceModel =
        changeNotifier.billionServiceModel;
    return billionServiceModel == null || !billionServiceModel.visible
        ? nullWidget
        : GestureDetector(
            onTap: () {
              changeNotifier.ctrlClicked(context, 'billionService.default', 'billionServiceDefault', <String, String>{});
              changeNotifier.shoPop(context, billionServiceModel.popWindowUrl);
            },
            child: Container(
              padding: const EdgeInsets.fromLTRB(paddingLeft, 9, paddingRight, 0),
              color: Color(0xffffffff),
              child: Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                        colors: <Color>[Color(0xFFfff9de), Color(0xFFffffff)]),
                    color: const Color(0xFFfff9de),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        if (billionServiceModel.icon != '')
                          FRoundImage.network(billionServiceModel.icon!,
                              fit: BoxFit.fitHeight,
                              height: 18.00,
                              urlResizeEnable: false),
                        Expanded(
                          child: Container(
                              child: Text(billionServiceModel.text!,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.left,
                                  style: const TextStyle(
                                    color: Color(0xFF0F131A),
                                    fontSize: 12.00,
                                  )),
                              margin: const EdgeInsets.fromLTRB(3.00, 0, 0, 0)),
                        ),
                        if (billionServiceModel.popWindowUrl != '')
                          Container(
                            margin: const EdgeInsets.fromLTRB(6, 0, 0, 0),
                            child: rightArrowSmall,
                          ),
                      ])),
            ),
          );
  }
}

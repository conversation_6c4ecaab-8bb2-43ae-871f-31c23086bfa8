import 'package:flutter/cupertino.dart';

import '../../custom_widget/dialog_webview.dart';
import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import 'model.dart';

class BillionServiceChangeNotifier extends ComponentChangeNotifier {
  BillionServiceModel? get billionServiceModel =>
      itemDetailModel.billionServiceModel;

  BillionServiceChangeNotifier(ComponentContext context) : super(context);

  ///保障浮层
  void shoPop(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      final DialogWebView dialogWebView =
          DialogWebView(context: context, url: url,itemDetailEngine: itemDetailEngine);
      dialogWebView.showPop();
    }
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class TravelOdetailWidget extends StatelessWidget {
  const TravelOdetailWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TravelOdetailChangeNotifier changeNotifier =
        Provider.of<TravelOdetailChangeNotifier>(context);
    return Container();
  }
}
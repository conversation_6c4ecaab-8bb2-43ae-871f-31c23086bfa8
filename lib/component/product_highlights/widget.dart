import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class ProductHighlightsWidget extends StatelessWidget {
  const ProductHighlightsWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ProductHighlightsChangeNotifier changeNotifier =
        Provider.of<ProductHighlightsChangeNotifier>(context);
    return Container();
  }
}
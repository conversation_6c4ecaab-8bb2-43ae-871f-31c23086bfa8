import 'dart:ui';

import '../../utils/common_util.dart';
import '../../utils/safe_access.dart';

class VacationScenicMembershipCardDataModel {
  ExtendInfo? extendInfo;
  LocatorDisplay? locatorDisplay;
  List<Activities>? activities;
  String? memberLevelIcon;
  String? memberRegisterUrl;
  String? memberRegisteringUrl;
  String? memberRightDisplayUrl;
  List<Rights>? rights;

  VacationScenicMembershipCardDataModel.fromJson(
      Map<String, dynamic> dataModel) {
    final Map<String, dynamic> partnerMemberRight = SafeAccess.safeParseMap(
        dataModel['partnerMemberRight'] ?? dataModel['hotelMemberRightsV2']);
    final Map<String, dynamic> json =
        SafeAccess.safeParseMap(partnerMemberRight['data']);
    extendInfo = json['extendInfo'] != null
        ? ExtendInfo.fromJson(SafeAccess.safeParseMap(json['extendInfo']))
        : null;
    locatorDisplay = json['locatorDisplay'] != null
        ? LocatorDisplay.fromJson(
            SafeAccess.safeParseMap(json['locatorDisplay']))
        : null;
    if(json['activities'] != null){
      activities = <Activities>[];
      SafeAccess.safeParseList(json['activities']).forEach((dynamic v) {
        activities!
            .add(Activities.fromJson(SafeAccess.safeParseMap( v)));
      });
    }
    memberLevelIcon = json['memberLevelIcon'] ?? json['memeberLevelIcon'];
    memberRegisterUrl = SafeAccess.safeParseString(json['memberRegisterUrl']);
    memberRegisteringUrl =
        SafeAccess.safeParseString(json['memberRegisteringUrl']);
    memberRightDisplayUrl =
        SafeAccess.safeParseString(json['memberRightDisplayUrl']);
    if (json['rights'] != null) {
      rights = <Rights>[];
      SafeAccess.safeParseList(json['rights']).forEach((dynamic v) {
        rights!
            .add(Rights.fromJson(SafeAccess.safeParseMap(v['display'] ?? v)));
      });
    }
  }
}

class ExtendInfo {
  String? rightTagFontColor;
  String? partnerName;
  int? level;
  String? levelName;
  bool? partnerMember;
  String? rightTagBackgroundColor;
  String? interflowScenario;
  int? sellerId;
  String? vendor;
  String? partnerShortName;
  int? registerStatus;
  int? memLevel;
  int? partnerId;
  int? bindStatus;
  bool? partnerMemValid;
  int? categoryId;

  ExtendInfo.fromJson(Map<String, dynamic> json) {
    rightTagFontColor = SafeAccess.safeParseString(json['rightTagFontColor']);
    partnerName = SafeAccess.safeParseString(json['partnerName']);
    level = SafeAccess.safeParseInt(json['level']);
    levelName = SafeAccess.safeParseString(json['levelName']);
    partnerMember = SafeAccess.safeParseBoolean(json['partnerMember']);
    rightTagBackgroundColor =
        SafeAccess.safeParseString(json['rightTagBackgroundColor']);
    interflowScenario = SafeAccess.safeParseString(json['interflowScenario']);
    sellerId = SafeAccess.safeParseInt(json['sellerId']);
    vendor = SafeAccess.safeParseString(json['vendor']);
    partnerShortName = SafeAccess.safeParseString(json['partnerShortName']);
    registerStatus = SafeAccess.safeParseInt(json['registerStatus']);
    memLevel = SafeAccess.safeParseInt(json['memLevel']);
    partnerId = SafeAccess.safeParseInt(json['partnerId']);
    bindStatus = SafeAccess.safeParseInt(json['bindStatus']);
    partnerMemValid = SafeAccess.safeParseBoolean(json['partnerMemValid']);
    categoryId = SafeAccess.safeParseInt(json['categoryId']);
  }

  bool get notRegistered => <int>[-1, 0, 3].contains(registerStatus);

  // 注册中
  bool get registering => <int>[2, 4].contains(registerStatus);

  // 已注册
  bool get registered => <int>[1].contains(registerStatus);
}

class LocatorDisplay {
  String? subTitleIcon;
  String? logo2;
  String? brandColor;
  Color? brandDarkColor;
  String? notRequiredProtocol;
  String? memberBrandAlias;
  String? notCrowdMainTitle;
  String? brandLightLogo;
  String? title;
  String? type;
  String? brandAnnouncementBeginTime;
  String? subTitle;
  String? groupProtocol;
  String? partnerLevelName;
  String? brandDark;
  String? brandChallengeActivity;
  String? registerActivityTwoJumpLink;
  String? registerActivityTwoPicture;
  String? registerSubTitle;
  String? underRegisterTipsCopyWrite;
  String? registerProtocol;
  String? cardDetailCopyWrite;
  String? brandName;
  String? cardDetailHeadImage;
  String? levelCompare;
  String? protocolPicAddress;
  String? registerMainTitle;
  String? correspondLogo;
  String? registerActivityTwoButtonText;
  String? registerActivityTwoMainTitle;
  String? registerActivityTwoSubTitle;
  bool? isNewRightMaterial;
  String? registerJumpLink;
  String? notRegisterSubTitle;
  String? memberNeedToKnow;
  String? actionButton;
  String? cardDetailJumpLink;
  String? partnerServicePhone;
  String? brandDarkLogo;
  String? brandAnnouncementEndTime;
  Color? brandContentColor;
  Color? brandLightColor;
  String? activityRule;
  String? memberLevelIcon;

  LocatorDisplay.fromJson(Map<String, dynamic> json) {
    subTitleIcon = json['subTitleIcon'];
    logo2 = SafeAccess.safeParseString(json['logo2']);
    brandColor = SafeAccess.safeParseString(json['brandColor']);
    brandDarkColor = stringToColor(json['brandDarkColor']);
    notRequiredProtocol =
        SafeAccess.safeParseString(json['notRequiredProtocol']);
    memberBrandAlias = SafeAccess.safeParseString(json['memberBrandAlias']);
    notCrowdMainTitle = SafeAccess.safeParseString(json['notCrowdMainTitle']);
    brandLightLogo = SafeAccess.safeParseString(json['brandLightLogo']);
    title = SafeAccess.safeParseString(json['title']);
    type = SafeAccess.safeParseString(json['type']);
    brandAnnouncementBeginTime =
        SafeAccess.safeParseString(json['brandAnnouncementBeginTime']);
    subTitle = json['subTitle'];
    groupProtocol = SafeAccess.safeParseString(json['groupProtocol']);
    partnerLevelName = SafeAccess.safeParseString(json['partnerLevelName']);
    brandDark = SafeAccess.safeParseString(json['brandDark']);
    brandChallengeActivity =
        SafeAccess.safeParseString(json['brandChallengeActivity']);
    registerActivityTwoJumpLink =
        SafeAccess.safeParseString(json['registerActivityTwoJumpLink']);
    registerActivityTwoPicture =
        SafeAccess.safeParseString(json['registerActivityTwoPicture']);
    registerSubTitle = SafeAccess.safeParseString(json['registerSubTitle']);
    underRegisterTipsCopyWrite =
        SafeAccess.safeParseString(json['underRegisterTipsCopyWrite']);
    registerProtocol = SafeAccess.safeParseString(json['registerProtocol']);
    cardDetailCopyWrite =
        SafeAccess.safeParseString(json['cardDetailCopyWrite']);
    brandName = SafeAccess.safeParseString(json['brandName']);
    cardDetailHeadImage =
        SafeAccess.safeParseString(json['cardDetailHeadImage']);
    levelCompare = SafeAccess.safeParseString(json['levelCompare']);
    protocolPicAddress = SafeAccess.safeParseString(json['protocolPicAddress']);
    registerMainTitle = SafeAccess.safeParseString(json['registerMainTitle']);
    correspondLogo = SafeAccess.safeParseString(json['correspondLogo']);
    registerActivityTwoButtonText =
        SafeAccess.safeParseString(json['registerActivityTwoButtonText']);
    registerActivityTwoMainTitle =
        SafeAccess.safeParseString(json['registerActivityTwoMainTitle']);
    registerActivityTwoSubTitle =
        SafeAccess.safeParseString(json['registerActivityTwoSubTitle']);
    isNewRightMaterial =
        SafeAccess.safeParseBoolean(json['isNewRightMaterial']);
    registerJumpLink = SafeAccess.safeParseString(json['registerJumpLink']);
    notRegisterSubTitle =
        SafeAccess.safeParseString(json['notRegisterSubTitle']);
    memberNeedToKnow = SafeAccess.safeParseString(json['memberNeedToKnow']);
    actionButton = SafeAccess.safeParseString(json['actionButton']);
    cardDetailJumpLink = SafeAccess.safeParseString(json['cardDetailJumpLink']);
    partnerServicePhone =
        SafeAccess.safeParseString(json['partnerServicePhone']);
    brandDarkLogo = SafeAccess.safeParseString(json['brandDarkLogo']);
    brandAnnouncementEndTime =
        SafeAccess.safeParseString(json['brandAnnouncementEndTime']);
    brandContentColor = stringToColor(json['brandContentColor']);
    brandLightColor = stringToColor(json['brandLightColor']);
    activityRule = SafeAccess.safeParseString(json['activityRule']);
    memberLevelIcon = json['memberLevelIcon'] ?? json['memeberLevelIcon'];
  }
}

class Activities {
  Color? borderColor;
  Color? darkColor;
  String? actionTypeStr;
  Color? lightColor;
  String? actionLabel;
  String? activityMainTitle;
  String? actionType;
  String? activityId;
  String? displayType;
  String? buttonType;
  String? activityTitle;
  String? actionContent;
  String? activitySubTitle;
  String? activityIcon;
  String? activityTag;
  String? subTitleHighLight;
  Map<String, dynamic>? trackArgObj;
  String? activityType;
  Color? fontColor;

  Activities.fromJson(Map<String, dynamic> json) {
    borderColor = stringToColor(json['borderColor']);
    darkColor = stringToColor(json['darkColor']??'#644119');
    actionTypeStr = SafeAccess.safeParseString(json['actionTypeStr']);
    lightColor = stringToColor(json['lightColor']??'#A87E4B');
    actionLabel = SafeAccess.safeParseString(json['actionLabel']);
    activityMainTitle = SafeAccess.safeParseString(json['activityMainTitle']);
    actionType = SafeAccess.safeParseString(json['actionType']);
    activityId = SafeAccess.safeParseString(json['activityId']);
    displayType = SafeAccess.safeParseString(json['displayType']);
    buttonType = SafeAccess.safeParseString(json['buttonType']);
    activityTitle = SafeAccess.safeParseString(json['activityTitle']);
    actionContent = json['actionContent'];
    activitySubTitle = SafeAccess.safeParseString(json['activitySubTitle']);
    activityIcon = SafeAccess.safeParseString(json['activityIcon']);
    activityTag = SafeAccess.safeParseString(json['activityTag']);
    subTitleHighLight = SafeAccess.safeParseString(json['subTitleHighLight']);
    trackArgObj = SafeAccess.safeParseMap(json['trackArgObj']);
    activityType = SafeAccess.safeParseString(json['activityType']);
    fontColor = stringToColor(json['fontColor']??'#ffffff');
  }
}

class Rights {
  String? description;
  String? fullName;
  String? icon;
  String? iconfontColor;
  int? index;
  String? rightDescription;
  String? rightIconSelect;
  int? rightId;
  String? rightName;
  bool? tagHideLine;
  String? type;

  Rights.fromJson(Map<String, dynamic> json) {
    description = SafeAccess.safeParseString(json['description']);
    fullName = SafeAccess.safeParseString(json['fullName']);
    icon = SafeAccess.safeParseString(json['icon']);
    iconfontColor = SafeAccess.safeParseString(json['iconfontColor']);
    index = SafeAccess.safeParseInt(json['index']);
    rightDescription = SafeAccess.safeParseString(json['rightDescription']);
    rightIconSelect = SafeAccess.safeParseString(json['rightIconSelect']);
    rightId = SafeAccess.safeParseInt(json['rightId']);
    rightName = SafeAccess.safeParseString(json['rightName']);
    tagHideLine = SafeAccess.safeParseBoolean(json['tagHideLine']);
    type = SafeAccess.safeParseString(json['type']);
  }
}

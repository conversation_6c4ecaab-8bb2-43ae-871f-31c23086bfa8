import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fround_image/fround_image.dart';
import '../../custom_widget/carousel_slider/carousel_slider.dart';
import '../../custom_widget/null_widget.dart';
import '../../utils/TextConfig.dart';
import '../../utils/common_config.dart';
import 'model.dart';
import 'notifier.dart';

class VacationScenicMembershipCardWidget extends StatefulWidget {
  const VacationScenicMembershipCardWidget({Key? key}) : super(key: key);

  @override
  State<VacationScenicMembershipCardWidget> createState() =>
      _VacationScenicMembershipCardWidgetState();
}

class _VacationScenicMembershipCardWidgetState
    extends State<VacationScenicMembershipCardWidget> {
  @override
  Widget build(BuildContext context) {
    final VacationScenicMembershipCardChangeNotifier changeNotifier =
        Provider.of<VacationScenicMembershipCardChangeNotifier>(context);
    final VacationScenicMembershipCardDataModel? memberDataModel =
        changeNotifier.memberDataModel;
    return memberDataModel == null || memberDataModel.extendInfo == null
        ? Container()
        : GestureDetector(
            onTap: () async {
              final bool needrefresh = await changeNotifier.cardClick(context);
              if (needrefresh) {
                setState(() {});
              }
            },
            child: Container(
              margin: const EdgeInsets.only(bottom: itemDivider),
              padding:
                  const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 0),
              decoration: BoxDecoration(
                  color: Color(0xffffffff),
                  borderRadius: BorderRadius.circular(cardBorderRadius)),
              child: Column(
                children: <Widget>[
                  if (memberDataModel.extendInfo?.notRegistered ?? false)
                    SizedBox(
                      height: 83,
                      child: _buildNotRegistered(
                          context, changeNotifier, memberDataModel),
                    ),
                  if ((memberDataModel.extendInfo?.registering ?? false) ||
                      (memberDataModel.extendInfo?.registered ?? false))
                    SizedBox(
                      height: 60,
                      child: _buildRegistered(
                          context, changeNotifier, memberDataModel),
                    ),
                  if (memberDataModel.activities != null)
                    _buildActivityWidget(
                        context, changeNotifier, memberDataModel.activities!),

                  // _buildBottomTab(memberDataModel.rights),
                ],
              ),
            ),
          );
  }

  Widget _buildNotRegistered(
      BuildContext context,
      VacationScenicMembershipCardChangeNotifier changeNotifier,
      VacationScenicMembershipCardDataModel memberDataModel) {
    return Stack(
      children: <Widget>[
        Container(
          margin: const EdgeInsets.only(top: 9),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    RichText(
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      text: TextSpan(
                        children: <InlineSpan>[
                          if (memberDataModel.locatorDisplay?.memberLevelIcon !=
                              null)
                            WidgetSpan(
                              child: Container(
                                margin: const EdgeInsets.only(
                                    right: 5, bottom: 1.5),
                                child: FRoundImage.network(
                                  memberDataModel.locatorDisplay!
                                      .memberLevelIcon!, // 图片文件的路径
                                  height: 15, // 图片的高度
                                  width: 55, // 图片的宽度
                                ),
                              ),
                            ),
                          TextSpan(
                              text: memberDataModel.locatorDisplay?.title ?? '',
                              style: TextStyle(
                                  fontWeight: FontWeightExt.bold,
                                  color: Color.fromARGB(255, 40, 44, 51),
                                  fontSize: 12.00)),
                          // 你可以继续添加更多的WidgetSpan或TextSpan...
                        ],
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(top: 5),
                      child: RichText(
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        text: TextSpan(
                          children: <InlineSpan>[
                            if (memberDataModel.locatorDisplay?.subTitleIcon !=
                                null)
                              WidgetSpan(
                                child: Container(
                                  margin: const EdgeInsets.only(
                                    right: 5,
                                  ),
                                  child: FRoundImage.network(
                                    memberDataModel
                                        .locatorDisplay!.subTitleIcon!,
                                    // 图片文件的路径
                                    height: 18, // 图片的高度
                                    width: 20, // 图片的宽度
                                  ),
                                ),
                              ),
                            if (memberDataModel.locatorDisplay?.subTitle !=
                                null)
                              WidgetSpan(
                                child: Container(
                                  height: 12,
                                  width: 0.5,
                                  margin: const EdgeInsets.only(right: 5),
                                  color:
                                      const Color.fromARGB(255, 234, 234, 234),
                                ),
                              ),
                            TextSpan(
                                text:
                                    memberDataModel.locatorDisplay?.subTitle ??
                                        '',
                                style: const TextStyle(
                                    color: Color.fromARGB(255, 40, 44, 51),
                                    fontSize: 12.00)),
                            // 你可以继续添加更多的WidgetSpan或TextSpan...
                          ],
                        ),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      child: Image.network(
                        'https://gw.alicdn.com/imgextra/i1/O1CN01GD4szy25wjCu1uV9h_!!6000000007591-2-tps-653-33.png',
                        width: double.infinity,
                        height: 25,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: const EdgeInsets.only(left: 6, right: 28),
                child: _buildCard(
                    context,
                    changeNotifier,
                    memberDataModel.locatorDisplay!,
                    memberDataModel.extendInfo!),
              )
            ],
          ),
        ),
        Positioned(
          bottom: 6,
          right: 4,
          child: Container(
            width: 128,
            height: 22,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(13),
              gradient: const LinearGradient(
                begin: Alignment.topLeft, // 渐变开始的位置
                end: Alignment.bottomRight, // 渐变结束的位置
                colors: <Color>[
                  // 渐变颜色数组
                  Color.fromARGB(255, 252, 242, 229), // 开始颜色
                  Color.fromARGB(255, 211, 182, 153), // 结束颜色
                ],
              ),
            ),
            child: RichText(
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              text: TextSpan(
                children: <InlineSpan>[
                  const TextSpan(
                      text: '仅剩一步，立即开通',
                      style: TextStyle(
                          color: Color.fromARGB(255, 58, 47, 33),
                          fontSize: 11.00)),
                  WidgetSpan(
                    child: Container(
                      margin: const EdgeInsets.only(left: 3, bottom: 1.5),
                      child: FRoundImage.network(
                        'https://gw.alicdn.com/imgextra/i1/O1CN012B1N721GhZhF9vNUo_!!6000000000654-2-tps-10-18.png',
                        // 图片文件的路径
                        height: 9, // 图片的高度
                        width: 5, // 图片的宽度
                      ),
                    ),
                  ),
                  // 你可以继续添加更多的WidgetSpan或TextSpan...
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRegistered(
      BuildContext context,
      VacationScenicMembershipCardChangeNotifier changeNotifier,
      VacationScenicMembershipCardDataModel memberDataModel) {
    return Stack(
      children: <Widget>[
        // Positioned(
        //   right: 0,
        //   child: FRoundImage.network(
        //     'https://gw.alicdn.com/imgextra/i2/O1CN01iRzxBj1koqSHJC4DZ_!!6000000004731-2-tps-237-120.png',
        //     borderRadius: const BorderRadius.only(topRight: Radius.circular(6)),
        //     width: 127,
        //     height: 60,
        //   ),
        // ),
        Row(
          children: <Widget>[
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(memberDataModel.locatorDisplay?.title ?? '',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontWeight: FontWeightExt.bold,
                          color: Color.fromARGB(255, 40, 44, 51),
                          fontSize: 14.00)),
                  Container(
                    margin: const EdgeInsets.only(top: 6),
                    child: Text(memberDataModel.locatorDisplay?.subTitle ?? '',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                            color: Color.fromARGB(255, 145, 147, 153),
                            fontSize: 11.00)),
                  ),
                ],
              ),
            ),
            Container(
              margin: const EdgeInsets.only(left: 6, right: 6),
              child: _buildCard(context, changeNotifier,
                  memberDataModel.locatorDisplay!, memberDataModel.extendInfo!),
            ),
            // FRoundImage.network(
            //   'https://img.alicdn.com/imgextra/i1/O1CN01OKHRtm1Rc2gZVP8KB_!!6000000002131-2-tps-15-18.png',
            //   width: 10,
            //   height: 8,
            // ),
          ],
        ),
      ],
    );
  }

  Widget _buildCard(
      BuildContext context,
      VacationScenicMembershipCardChangeNotifier changeNotifier,
      LocatorDisplay locatorDisplay,
      ExtendInfo extendInfo) {
    return Container(
      width: 80,
      height: 46,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        gradient: LinearGradient(
          begin: Alignment.topLeft, // 渐变开始的位置
          end: Alignment.bottomRight, // 渐变结束的位置
          colors: <Color>[
            // 渐变颜色数组
            locatorDisplay.brandDarkColor!, // 开始颜色
            locatorDisplay.brandLightColor!, // 结束颜色
          ],
        ),
      ),
      child: Stack(
        children: <Widget>[
          Positioned(
            top: -19,
            left: 43,
            child: Opacity(
              opacity: 0.1,
              child: FRoundImage.network(
                locatorDisplay.brandDark!,
                width: 50,
                height: 50,
              ),
            ),
          ),
          SizedBox(
            width: 80,
            height: 46,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                FRoundImage.network(
                  locatorDisplay.correspondLogo!,
                  width: 58,
                  height: 29,
                ),
                Container(
                    padding: const EdgeInsets.fromLTRB(6, 1, 6, 1),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: locatorDisplay.brandContentColor),
                    child: Text(
                      extendInfo.levelName!,
                      style: TextStyle(
                          color: locatorDisplay.brandLightColor, fontSize: 8),
                    ))
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomTab(List<Rights>? rights) {
    return rights == null || rights.length == 1
        ? nullWidget
        : Container(
            height: 32,
            color: const Color.fromARGB(255, 250, 239, 227),
            child: Row(
              children: _buildSubTab(rights),
            ),
          );
  }

  List<Widget> _buildSubTab(List<Rights> rights) {
    final List<Widget> tabs = <Widget>[];
    for (int i = 0; i < rights.length; i++) {
      final Rights model = rights[i];
      tabs.add(Expanded(
        child: Text(
          model.rightName!,
          textAlign: TextAlign.center,
          style: const TextStyle(
              color: Color.fromARGB(255, 41, 43, 51), fontSize: 12),
        ),
      ));
      if (!model.tagHideLine! && i != rights.length - 1) {
        tabs.add(const SizedBox(
          height: 13,
          width: 1,
          child: VerticalDivider(color: Color.fromARGB(255, 205, 176, 146)),
        ));
      }
      if (tabs.length == 6) {
        break;
      }
    }
    return tabs;
  }

  Widget _buildActivityWidget(
      BuildContext context,
      VacationScenicMembershipCardChangeNotifier changeNotifier,
      List<Activities> activities) {
    return Container(
      margin: EdgeInsets.only(bottom: 9),
      child: NotificationListener(
        onNotification: (ScrollNotification notification) {
          return true;
        },
        child: CarouselSlider(
          items: activities!.map((Activities subData) {
            return Builder(
              builder: (BuildContext context) {
                return GestureDetector(
                  onTap: () {
                    changeNotifier.jumpActivityPage(context, subData);
                  },
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(9, 0, 9, 0),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(cardBorderRadius),
                      color: Color(0xfffff5eb),
                    ),
                    child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Image.network(
                            subData.activityIcon!,
                            height: 32,
                            width: 32,
                          ),
                          Expanded(
                            child: Container(
                              margin: const EdgeInsets.fromLTRB(6, 0, 6, 0),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Text(subData.activityMainTitle!,
                                      textAlign: TextAlign.left,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                          fontWeight: FontWeight.w500,
                                          color: Color(0xff292c33),
                                          fontSize: 12.00)),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 3),
                                    child: Text(subData.activitySubTitle!,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.left,
                                        style: TextStyle(
                                            color: Color(0xff5c5f66),
                                            fontSize: 10.00)),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          if ('oneLineText' == subData.buttonType)
                            Container(
                              child: Text(subData.actionLabel!,
                                  textAlign: TextAlign.left,
                                  style: TextStyle(
                                      color: Color(0xff919499), fontSize: 10.00)),
                            ),
                          if ('buttonWithBorder' == subData.buttonType)
                            Container(
                              height: 21,
                              padding: const EdgeInsets.fromLTRB(12, 0, 12, 0),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10.5),
                                border: Border.all(
                                  color: subData.borderColor!, // 边框颜色
                                  width: 0.5, // 边框宽度
                                ),
                              ),
                              child: Center(
                                child: Text(subData.actionLabel!,
                                    textAlign: TextAlign.left,
                                    style: TextStyle(
                                        color: Color(0xffffffff), fontSize: 11.00)),
                              ),
                            ),
                          if ('buttonWithGradient' == subData.buttonType)
                            Container(
                              height: 21,
                              padding: const EdgeInsets.fromLTRB(9, 0, 9, 0),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10.5),
                                gradient: LinearGradient(
                                  colors: <Color>[
                                    subData.lightColor!,
                                    subData.darkColor!
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                              ),
                              child: Center(
                                child: Text(subData.actionLabel!,
                                    textAlign: TextAlign.left,
                                    style: TextStyle(
                                        color: subData.fontColor, fontSize: 11.00)),
                              ),
                            ),
                        ]),
                  ),
                );
                ;
              },
            );
          }).toList(),
          options: CarouselOptions(
            autoPlay: true,
            aspectRatio: 315 / 45,
            viewportFraction: 1,
            scrollDirection: Axis.vertical,
            autoPlayInterval: const Duration(seconds: 2),
            scrollPhysics: NeverScrollableScrollPhysics(),
          ),
        ),
      ),
    );
  }
}

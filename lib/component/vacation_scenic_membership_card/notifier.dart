import 'package:fliggy_mtop/fliggy_mtop.dart';
import 'package:flutter/cupertino.dart';

import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'model.dart';
import 'package:fjson_exports/fjson_exports.dart';

class VacationScenicMembershipCardChangeNotifier
    extends ComponentChangeNotifier {
  VacationScenicMembershipCardDataModel? get memberDataModel =>
      itemDetailModel.vacationScenicMembershipCardDataModel;

  VacationScenicMembershipCardChangeNotifier(ComponentContext context)
      : super(context);

  ///TODO h5页面需要关两次，页面没有调用桥关闭导致，已经提给h5谦融修改
  Future<bool> cardClick(BuildContext context) async {
    String? url;
    if (memberDataModel!.extendInfo!.notRegistered) {
      // H5样例链接: https://market.m.taobao.com/app/trip/rx-member-iframe/pages/home?_fli_background_transparent=true&animated=false&titleBarHidden=1&sellerId=724059099&vendor=taobao&visible=1&fromType=detail&interflowScenario=platformInterflowPage&dataSpmA=181&dataSpmB=7850105&fromitem=1
      // native 样例链接 https://market.wapa.taobao.com/app/trip/rx-member2021/pages/registerPop?visible=true&dataSpmB=7850105&partnerId=724059099&vendor=taobao&interflowScenario=platformInterflowPage&anime_type=-1&_fli_close_anim=none&_fli_background_transparent=true&_fli_new_router=true&titleBarHidden=1&_fli_poplayer_count=0&animated=false&fromitem=1&callbackType=bridge
      url = safeNonNullString(memberDataModel!.memberRegisterUrl);
      url = transformRegisterUrl(url!); // H5 的其他会员
    } else if (memberDataModel!.extendInfo!.registering) {
      url = safeNonNullString(memberDataModel!.memberRegisterUrl);
      url = transformRegisterUrl(url!); // H5 的其他会员
    } else if (memberDataModel!.extendInfo!.registered) {
      // 样例链接 https://market.wapa.taobao.com/app/trip/rx-ticket-iframe/pages/member-rights?itemId=687918291308&parentType=h5&fromitem=1
      url = safeNonNullString(memberDataModel!.memberRightDisplayUrl);
      url = transformRegistedUrl(url);
    }
    // url = 'https://market.wapa.taobao.com/app/trip/rx-ticket-iframe/pages/member-rights?_fli_background_transparent=true&animated=false&titleBarHidden=1&itemId=687918291308&parentType=native&fromitem=1';
    // url = 'https://market.m.taobao.com/app/trip/rx-member-iframe/pages/home?_fli_background_transparent=true&animated=false&titleBarHidden=1&sellerId=724059099&vendor=taobao&visible=1&fromType=detail&interflowScenario=platformInterflowPage&dataSpmA=181&dataSpmB=7850105&fromitem=1';
    if (url != null && url.isNotEmpty) {
      final Map<dynamic, dynamic> value = await FliggyNavigatorApi.getInstance()
          .push(context, url, anim: Anim.none);
      if (value.containsKey('is_need_refresh')) {
        if (context.mounted) {
          return refreshMemberCard(context);
        } else {
          return false;
        }
      } else {
        return false;
      }
    } else {
      return Future<bool>.value(false);
    }
  }

  /// 处理已注册的 URL
  String transformRegistedUrl(String url) {
    final Uri uri = Uri.parse(url);
    // 补齐参数
    final Map<String, String> newParams =
        Map<String, String>.from(uri.queryParameters);
    newParams['_fli_background_transparent'] = 'true';
    newParams['titleBarHidden'] = '1';
    newParams['parentType'] = 'native';
    newParams['animated'] = 'false';
    final Uri newUri = Uri.parse(url).replace(queryParameters: newParams);
    return newUri.toString();
  }

  /// 处理注册的 URL
  String transformRegisterUrl(String url) {
    final Uri uri = Uri.parse(url);
    if (uri.path.contains('trip/rx-member-iframe')) {
      // 1 替换 path 链接
      final RegExp regExp = RegExp(r'trip/rx-member-iframe/pages/home');
      final String newUrl = url.replaceFirst(
          regExp, 'trip/rx-member2021/pages/registerPop-native');

      // 2 补齐参数
      final Map<String, String> newParams =
          Map<String, String>.from(uri.queryParameters);
      final Uri newUri = Uri.parse(newUrl).replace(queryParameters: newParams);
      return newUri.toString();
    }
    return url;
  }

  Future<bool> refreshMemberCard(BuildContext context) {
    // 1/用 itemID 刷新商详
    final String itemId = safeNonNullString(itemDetailModel.itemModel?.itemId);
    // 2  请求 mtop.fliggy.traveldc.poi.member.queryScenicMemberRightInfo
    return requestRefreshData(context, itemId: itemId);
  }

  /// 会员卡片请求服务端
  Future<bool> requestRefreshData(BuildContext context,
      {required String itemId}) async {
    const String apiName =
        'mtop.fliggy.traveldc.poi.member.queryScenicMemberRightInfo';
    const String apiVersion = '1.0';

    final Map<String, String> _requestParams = <String, String>{
      'itemId': itemId
    };
    final MtopRequestModel request = MtopRequestModel.buildRequset(
        api: apiName,
        method: 'POST',
        version: apiVersion,
        params: _requestParams);
    final MtopResponseModel responseModel =
        await FliggyMtopApi.getInstance().send(context, request);
    if (responseModel.success) {
      final Map<String, dynamic> requestdata =
          safeNonNullMap(responseModel.data, (dynamic e) => e);
      refreshMemberShipCard(requestdata);
      return true;
    } else {
      return false;
    }
  }

  /// 会员卡片使用服务端数据刷新
  void refreshMemberShipCard(Map<String, dynamic> data) {
    final Map<String, dynamic> module =
        safeNonNullMap(data['module'], (dynamic e) => e);

    // 这里不是直接用这个数据渲染,是替换掉 global 里有的数据即可
    final Map<String, dynamic> partnerMemberRight = safeNonNullMap(
        itemDetailModel.data['partnerMemberRight'], (dynamic e) => e);

    final Map<String, dynamic> partnerMemberRightData =
        safeNonNullMap(partnerMemberRight['data'], (dynamic e) => e);

    partnerMemberRightData.addAll(module);
    itemDetailModel.vacationScenicMembershipCardDataModel =
        VacationScenicMembershipCardDataModel.fromJson(itemDetailModel.data);

    print('');
  }

  void jumpActivityPage(BuildContext context, Activities data) {
    if ('2' == data.actionTypeStr) {
      if (data.actionContent != null) {
        FliggyNavigatorApi.getInstance()
            .push(context, data.actionContent!, anim: Anim.none);
      }
    } else if ('1' == data.actionTypeStr) {

    }
  }
}

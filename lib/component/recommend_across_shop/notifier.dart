import 'package:flutter/cupertino.dart';

import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import '../recommend/model.dart';
import 'package:fliggy_router/fliggy_router.dart';

class RecommendAcrossShopChangeNotifier extends ComponentChangeNotifier {
  RecommendAcrossShopChangeNotifier(ComponentContext context) : super(context);
  RecommendDataModel? get recommendDataModel =>
      itemDetailModel.recommendDataModel;
  void itemClick(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      FliggyNavigatorApi.getInstance().push(context, url, anim: Anim.slide,params:<String,dynamic>{'_fli_inner_router_':true,});
    }
  }
}
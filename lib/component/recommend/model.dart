import 'dart:ui';

import '../../utils/common_util.dart';
import '../../utils/safe_access.dart';

class RecommendDataModel {
  bool get visible =>
      (seeMoreItemInfoList != null && seeMoreItemInfoList!.isNotEmpty) ||
      (acrossItemInfoList != null && acrossItemInfoList!.isNotEmpty);
  List<ItemInfoList>? seeMoreItemInfoList;
  String? seeMoreTitle;
  List<ItemInfoList>? acrossItemInfoList;
  String? acrossShopTitle;

  List<String?> cdnItems = <String>[];

  RecommendDataModel.fromJson(Map<String, dynamic> dataModel) {
    final Map<String, dynamic>? seeMore = dataModel['seeMore'];
    final Map<String, dynamic>? seeMoreData = seeMore?['data'];
    if (seeMoreData?['itemInfoList'] != null) {
      seeMoreItemInfoList = <ItemInfoList>[];
      SafeAccess.safeParseList(seeMoreData?['itemInfoList'])
          .forEach((dynamic v) {
        seeMoreItemInfoList!
            .add(ItemInfoList.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    seeMoreTitle = SafeAccess.safeParseString(seeMoreData?['title']);

    final Map<String, dynamic>? acrossShop = dataModel['acrossShop'];
    final Map<String, dynamic>? acrossShopData = acrossShop?['data'];
    if (acrossShopData?['itemInfoList'] != null) {
      acrossItemInfoList = <ItemInfoList>[];
      SafeAccess.safeParseList(acrossShopData?['itemInfoList'])
          .forEach((dynamic v) {
        acrossItemInfoList!
            .add(ItemInfoList.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    acrossShopTitle = SafeAccess.safeParseString(acrossShopData?['title']);
    if (seeMoreItemInfoList != null) {
      for (final ItemInfoList info in seeMoreItemInfoList!) {
        cdnItems.add(info.item1?.itemId);
        cdnItems.add(info.item2?.itemId);
      }
    }
    if (acrossItemInfoList != null) {
      for (final ItemInfoList info in acrossItemInfoList!) {
        cdnItems.add(info.item1?.itemId);
        cdnItems.add(info.item2?.itemId);
      }
    }
  }
}

class ItemInfoList {
  Item? item1;
  Item? item2;

  ItemInfoList.fromJson(Map<String, dynamic> json) {
    item1 = json['item1'] != null
        ? Item.fromJson(SafeAccess.safeParseMap(json['item1']))
        : null;
    item2 = json['item2'] != null
        ? Item.fromJson(SafeAccess.safeParseMap(json['item2']))
        : null;
  }
}

class Item {
  String? categoryDesc;
  Color? discountPriceTextColor;
  String? discountPriceText;
  String? currPriceSuffix;
  String? bigPromotionBelt;
  String? bigPromotionSubscript;
  String? doubleElevenIcon;
  String? currPrice;
  String? sellPoint;
  String? index;
  String? itemId;
  String? itemName;
  String? itemPic;
  String? sold;
  String? trackInfo;
  Trackargs? trackargs;
  TripJumpInfo? tripJumpInfo;

  Item.fromJson(Map<String, dynamic> json) {
    categoryDesc = SafeAccess.safeParseString(json['categoryDesc']);
    discountPriceTextColor =
        stringToColor(json['discountPriceTextColor'] ?? '#ff401A');
    discountPriceText = SafeAccess.safeParseString(json['discountPriceText']);
    currPriceSuffix = SafeAccess.safeParseString(json['currPriceSuffix']);
    bigPromotionBelt = SafeAccess.safeParseString(json['bigPromotionBelt']);
    bigPromotionSubscript =
        SafeAccess.safeParseString(json['bigPromotionSubscript']);
    doubleElevenIcon = SafeAccess.safeParseString(json['doubleElevenIcon']);
    currPrice = SafeAccess.safeParseString(json['currPrice']);

    sellPoint = SafeAccess.safeParseString(json['sellPoint']);
    index = SafeAccess.safeParseString(json['index']);
    itemId = SafeAccess.safeParseString(json['itemId']);
    itemName = SafeAccess.safeParseString(json['itemName']);
    itemPic = SafeAccess.safeParseString(json['itemPic']);
    sold = SafeAccess.safeParseString(json['sold']);
    trackInfo = SafeAccess.safeParseString(json['trackInfo']);
    trackargs = json['trackargs'] != null
        ? Trackargs.fromJson(SafeAccess.safeParseMap(json['trackargs']))
        : null;
    tripJumpInfo = json['tripJumpInfo'] != null
        ? TripJumpInfo.fromJson(SafeAccess.safeParseMap(json['tripJumpInfo']))
        : null;
  }
}

class Trackargs {
  String? biz;
  String? clkinfos;
  String? pvid;
  String? scm;

  Trackargs.fromJson(Map<String, dynamic> json) {
    biz = SafeAccess.safeParseString(json['biz']);
    clkinfos = SafeAccess.safeParseString(json['clkinfos']);
    pvid = SafeAccess.safeParseString(json['pvid']);
    scm = SafeAccess.safeParseString(json['scm']);
  }
}

class TripJumpInfo {
  String? jumpH5Url;
  bool? jumpNative;

  TripJumpInfo.fromJson(Map<String, dynamic> json) {
    jumpH5Url = SafeAccess.safeParseString(json['jumpH5Url']);
    jumpNative = SafeAccess.safeParseBoolean(json['jumpNative']);
  }
}

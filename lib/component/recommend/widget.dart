import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:fround_image/fround_image.dart';
import 'package:provider/provider.dart';

import '../../custom_widget/null_widget.dart';
import '../../data/router_net/item_detail_preload_helper.dart';
import '../../utils/TextConfig.dart';
import '../../utils/img_utils.dart';
import '../component_key_constant.dart';
import 'model.dart';
import 'notifier.dart';

class RecommendWidget extends StatelessWidget {
  const RecommendWidget({Key? key}) : super(key: key);

  static const double cardWidth = 174.00;

  @override
  Widget build(BuildContext context) {
    final RecommendChangeNotifier changeNotifier =
        Provider.of<RecommendChangeNotifier>(context);
    changeNotifier.fromJson();
    final RecommendDataModel? recommendDataModel =
        changeNotifier.recommendDataModel;
    return recommendDataModel == null || !recommendDataModel.visible
        ? nullWidget
        : SizedBox(
            width: double.infinity,
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: <
                    Widget>[
              //tab滚动位置记录
              Container(
                key: changeNotifier.itemDetailModel.titleBarModel
                    ?.tabGlobalKey[ComponentKeyConstant.recommend],
              ),
              if (recommendDataModel.seeMoreItemInfoList?.isNotEmpty ?? false)
                _seeTitleWidget(recommendDataModel.seeMoreTitle!),
              if (recommendDataModel.seeMoreItemInfoList?.isNotEmpty ?? false)
                SizedBox(
                    width: double.infinity,
                    child: ListView.builder(
                      padding: EdgeInsets.zero,
                      // 设置内边距为0
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemCount: recommendDataModel.seeMoreItemInfoList?.length,
                      itemBuilder: (BuildContext context, int index) {
                        final ItemInfoList? itemInfoList =
                            recommendDataModel.seeMoreItemInfoList?[index];
                        return itemInfoList == null
                            ? nullWidget
                            : Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                    _itemWidget(
                                        itemInfoList.item1,
                                        changeNotifier,
                                        context,
                                        'fliggy_recommend_seemore.d${index * 2}',
                                        'recommend_seemore_d${index * 2}'),
                                    Container(
                                      width: 9,
                                    ),
                                    _itemWidget(
                                        itemInfoList.item2,
                                        changeNotifier,
                                        context,
                                        'fliggy_recommend_seemore.d${index * 2 + 1}',
                                        'recommend_seemore_d${index * 2 + 1}'),
                                  ]);
                      },
                    )),
            ]),
          );
  }

  ///看了又看,邻家好货
  Widget _seeTitleWidget(String title) {
    return SizedBox(
      width: double.infinity,
      height: 56.00,
      child:
          Row(mainAxisAlignment: MainAxisAlignment.center, children: <Widget>[
        Container(
          width: 45.00,
          height: 0.50,
          color: const Color.fromARGB(255, 221, 221, 56831),
        ),
        Container(
            child: Text(title,
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontWeight: FontWeightExt.bold,
                    color: Color.fromARGB(255, 17, 17, 17),
                    fontSize: 16.00)),
            margin: const EdgeInsets.fromLTRB(15.00, 0, 15.00, 0)),
        Container(
          width: 45.00,
          height: 0.50,
          color: const Color.fromARGB(255, 221, 221, 56831),
        )
      ]),
    );
  }

  ///猜你喜欢item
  Widget _itemWidget(Item? item, RecommendChangeNotifier changeNotifier,
      BuildContext context, String spmCD, String controlName) {
    if (item == null) {
      return Container();
    }

    changeNotifier.ctrlExposure(context, spmCD, <String, String>{});
    PreLoadHelper.downloadImage(item.itemId);

    return GestureDetector(
      onTap: () {
        // 埋点
        changeNotifier
            .ctrlClicked(context, spmCD, controlName, <String, String>{});
        changeNotifier.itemClick(context, item.tripJumpInfo?.jumpH5Url);
      },
      child: Container(
        margin: const EdgeInsets.fromLTRB(0, 0, 0, 9),
        width: cardWidth,
        height: 254.00,
        decoration: BoxDecoration(
            color: const Color.fromARGB(255, 255, 255, 255),
            borderRadius: BorderRadius.circular(6.00)),
        child: Column(children: <Widget>[
          item.itemPic == null
              ? Container()
              : Container(
                  width: cardWidth,
                  height: cardWidth,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(6),
                        topRight: Radius.circular(6)),
                  ),
                  child: Stack(
                    children: <Widget>[
                      // 首图
                  ClipRRect(
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(6),
                              topRight: Radius.circular(6)), // 设置圆角半径
                          child: CachedNetworkImage(
                            imageUrl: ImgUtils.resizeCDNUrl(
                                url: item.itemPic,
                                width: cardWidth,
                                height: cardWidth),
                            width: cardWidth,
                            height: cardWidth,
                            fadeInDuration: Duration(milliseconds: 50),
                          )),
                      item.bigPromotionSubscript != ''
                          ? CachedNetworkImage(
                              imageUrl: item.bigPromotionSubscript!,
                              width: cardWidth,
                              fit: BoxFit.fitWidth,
                              fadeInDuration: Duration(milliseconds: 50),
                            )
                          : nullWidget,

                      // 大促腰带
                      item.categoryDesc != '' &&
                              item.bigPromotionSubscript == ''
                          ? Container(
                              height: 24,
                              alignment: Alignment.center,
                              padding: const EdgeInsets.only(left: 6, right: 6),
                              decoration: const BoxDecoration(
                                color: Color.fromARGB(20, 111, 111, 111),
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(6),
                                    bottomRight: Radius.circular(6)),
                              ),
                              child: Text(item.categoryDesc!,
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(
                                      color: Color.fromARGB(255, 255, 255, 255),
                                      fontSize: 11.00)),
                            )
                          : nullWidget,
                      Positioned(
                        bottom: 0,
                        child: item.bigPromotionBelt != ''
                            ? CachedNetworkImage(
                                imageUrl: item.bigPromotionBelt!, // 图片文件的路径
                                width: cardWidth,
                                fit: BoxFit.fitWidth,
                                fadeInDuration: Duration(milliseconds: 50),
                              )
                            : nullWidget,
                      )
                    ],
                  ),
                ),
          SizedBox(
              width: cardWidth - 16,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    // 标题
                    _buildTitle(item),
                    // 价格
                    _buildPrice(item),
                  ]))
        ]),
      ),
    );
  }

  Widget _buildTitle(Item item) {
    return Container(
      height: 39,
      margin: const EdgeInsets.fromLTRB(0, 6, 0, 6),
      child: RichText(
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        text: TextSpan(
          children: <InlineSpan>[
            if (item.doubleElevenIcon != '')
              WidgetSpan(
                child: Container(
                  margin: const EdgeInsets.only(right: 3, bottom: 1.5),
                  child: FRoundImage.network(
                    item.doubleElevenIcon!, // 图片文件的路径
                    height: 12, // 图片的高度
                    fit: BoxFit.cover, // 图片的宽度
                  ),
                ),
              ),
            TextSpan(
                text: item.itemName,
                style: const TextStyle(
                    height: 1.2,
                    fontWeight: FontWeight.w600,
                    color: Color.fromARGB(255, 15, 19, 26),
                    fontSize: 13.00)),
            // 你可以继续添加更多的WidgetSpan或TextSpan...
          ],
        ),
      ),
    );
  }

  Widget _buildPrice(Item item) {
    return Row(
        // crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Expanded(
              child: Row(
                  // crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                Text(item.discountPriceText!,
                    textAlign: TextAlign.end,
                    style: TextStyle(
                        color: item.discountPriceTextColor, fontSize: 12.00)),
                const Padding(
                  padding: EdgeInsets.only(top: 1, right: 2),
                  child: Text(
                    '¥',
                    textAlign: TextAlign.end,
                    style: TextStyle(
                        color: Color.fromARGB(255, 255, 85, 51),
                        fontSize: 12.00,
                        fontFamily: 'fliggy_sans102_bd',
                        package: 'ffonts',
                        height: 1),
                  ),
                ),
                Text(item.currPrice!,
                    textAlign: TextAlign.end,
                    style: const TextStyle(
                        color: Color.fromARGB(255, 255, 85, 51),
                        fontSize: 17.00,
                        fontFamily: 'fliggy_sans102_bd',
                        package: 'ffonts',
                        height: 1)),
                Text(item.currPriceSuffix!,
                    textAlign: TextAlign.end,
                    style: const TextStyle(
                        color: Color.fromARGB(255, 255, 85, 51),
                        fontSize: 12.00))
              ])),
          Text(item.sold!,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.end,
              style: const TextStyle(
                  height: 1,
                  color: Color.fromARGB(255, 145, 148, 153),
                  fontSize: 10.00))
        ]);
  }
}

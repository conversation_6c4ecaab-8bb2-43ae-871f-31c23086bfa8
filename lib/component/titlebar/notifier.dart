import 'package:fbroadcast/fbroadcast.dart';
import 'package:ffloat/ffloat.dart';
import 'package:flutter/material.dart';

import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:ftitlebar/ftitlebar_main.dart';

import '../../utils/share_utils.dart';

import 'model.dart';

class TitleBarChangeNotifier extends ComponentChangeNotifier {

  TitleBarModel? get titleBarModel => itemDetailModel.titleBarModel;
  String? get itemId => itemDetailModel.itemModel?.itemId;


  TitleBarChangeNotifier(ComponentContext context) : super(context);

  ///页面返回
  void popBack(BuildContext context) {
    // FBroadcast.instance(uniqueId).broadcast('popBack');
    itemDetailEngine.pagePop();
  }

  ///tab点击滚动
  void scrollToComponent(int index) {
    final String id = titleBarModel!.tabList[index].id!;
    /// 现在第一位都是头图，直接可以滚动到指定index，防止有时滚动不到顶部
    if (index == 0) {
      itemDetailEngine.scrollToOffset(0);
      TitleBarModel.tabScrolling = false;
    } else {
      scrollToNode(id);
    }
  }

  ///购物车
  void shopCarClick(BuildContext context) {
    if (titleBarModel!.rightCustomUrl != null) {
      FliggyNavigatorApi.getInstance()
          .push(context, titleBarModel!.rightCustomUrl!);
    }
  }

  ///分享
  void shareClick(BuildContext context) {
    ShareUtil.shareClick(context, itemDetailModel);
  }
}

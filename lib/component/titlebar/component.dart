import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description titleBar
class TitleBarComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<TitleBarChangeNotifier>.value(
      value: TitleBarChangeNotifier(context),
      child: TitleBarWidget(),
    );
  }
}
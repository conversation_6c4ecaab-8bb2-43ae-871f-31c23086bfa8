import 'dart:convert';
import 'dart:math';

import 'package:fbridge/fbridge.dart';
import 'package:fbroadcast/fbroadcast.dart';
import 'package:ffloat/ffloat.dart';
import 'package:ficonfont/ficonfont.dart';
import 'package:fjson_exports/fjson_exports.dart';
import 'package:flutter/material.dart';
import 'package:ftitlebar/ftitlebar_main.dart';
import 'package:ftitlebar/ftitlebar_popupmenu.dart';
import 'package:ftitlebar_adapter/ftitlebar_adapter.dart';
import 'package:provider/provider.dart';

import '../../utils/TextConfig.dart';
import '../../utils/track_utils.dart';
import 'model.dart';
import 'notifier.dart';

import 'package:flutter_boost/flutter_boost.dart';

import 'package:fliggy_router/fliggy_router.dart';

class TitleBarWidget extends StatefulWidget {
  TitleBarWidget({Key? key, this.themeAlpha = 0.0}) : super(key: key);
  double themeAlpha;

  @override
  State<StatefulWidget> createState() {
    return _TitleBarState();
  }
}

class _TitleBarState extends State<TitleBarWidget>
    with TickerProviderStateMixin {
  // SingleTickerProviderStateMixin
  TabController? _tabController;
  int oldTabIndex = 0;
  int initialIndex = 0;

  /// 底部收藏和顶部联动的控制器
  late FFloatController floatController = FFloatController();
  late TitleBarChangeNotifier changeNotifier;
  TitleBarModel? titleBarModel;

  BackgroundAlphaNotifier? backgroundAlphaNotifier;
  double alphaNotifierData = 0.0;

  /// 分享动态按钮
  String? shareWhiteIconUrl;

  /// 分享是否展示动态按钮
  bool _showAnimatedShareIcon = false;
  String? carAndCollectPopTips;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: _tabColorAlpha(Color(0xffffffff)),
      child: Column(
        children: <Widget>[
          _buildTitleBar(context, changeNotifier),
          if ((titleBarModel?.tabList.length ?? 0) > 0) _buildTab(),
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    _fetchShareIconUrl();
  }

  /// 获取 kv 里缓存的分享按钮
  Future<void> _fetchShareIconUrl() async {
    final String? result = await FBridgeApi.newInstance(context)
        .invoke('get_kvcache', <String, dynamic>{'key': 'shareCouponConfig'});
    if (result != null && result != 'null' && result.isNotEmpty) {
      final Map<String, dynamic>? couponConfig =
      safeNonNullMap(json.decoder.convert(result), (dynamic e) => e);
      shareWhiteIconUrl = couponConfig?['shareEntranceWhiteImageUrl'] ?? '';
      if (shareWhiteIconUrl!.isNotEmpty) {
        setState(() {
          _showAnimatedShareIcon = true;
        });
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _initWidget();
  }

  void _initWidget() {
    TitleBarModel.tabScrolling = false;
    changeNotifier = Provider.of<TitleBarChangeNotifier>(context);
    titleBarModel = changeNotifier.titleBarModel;
    if (_tabController != null) {
      _tabController!.dispose();
      _tabController = null;
    }
    _tabController = TabController(
        length: titleBarModel?.tabList.length ?? 0,
        vsync: this,
        initialIndex: initialIndex);
    registerEvent();
    if (backgroundAlphaNotifier != null) {
      backgroundAlphaNotifier = null;
    }
    backgroundAlphaNotifier = BackgroundAlphaNotifier(alphaNotifierData);
    widget.themeAlpha = backgroundAlphaNotifier!.value;
    backgroundAlphaNotifier?.addListener(() {
      if (mounted) {
        setState(() {
          widget.themeAlpha = backgroundAlphaNotifier?.value ?? 0.0;
        });
      }
    });
  }

  // 第一排
  Widget _buildTitleBar(
      BuildContext context, TitleBarChangeNotifier changeNotifier) {
    double paddingTop =
    safeNonNullDouble(MediaQuery.of(context).padding.top - 6);
    if (paddingTop < 0) {
      paddingTop = 0;
    }
    return FTitleBar(
        paddingTop: paddingTop,
        backgroundAlpha: widget.themeAlpha,
        backgroundAlphaNotifier: backgroundAlphaNotifier,
        // onBackPressed: () {
        //   changeNotifier.popBack(context);
        // },
        leftItem: GestureDetector(
          onTap: () {
            const String spmCD = 'title_bar.back';
            changeNotifier
                .ctrlClicked(context, spmCD, 'naviBack', <String, String>{});
            changeNotifier.popBack(context);
          },
          child: Padding(
            padding: const EdgeInsets.only(top: 6, left: 3),
            child: Transform.rotate(
              angle: pi / 2, // 90度，使用弧度制
              child: Ficon(
                  0xe890, //ICON_FANHUIJIANTOU,
                  22,
                  _titleColorAlpha(const Color(0xFF000000))),
            ),
          ),
        ),
        middleItem: Container(
          margin: const EdgeInsets.fromLTRB(50, 0, 120, 0),
          width: 220,
          child: Text(
            changeNotifier.titleBarModel!.itemTitle,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: _tabColorAlpha(Color(0xFF000000)),
                fontWeight: FontWeightExt.bold),
          ),
        ),
        hasThirdItem: true,
        thirdItem: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            // 分享按钮
            GestureDetector(
              onTap: () {
                const String spmCD = 'title_bar.share';
                changeNotifier.ctrlClicked(
                    context, spmCD, 'navShare', <String, String>{});
                changeNotifier.shareClick(context);
              },
              child: Padding(
                padding: const EdgeInsets.only(top: 2, right: 12),
                child: shareWhiteIconUrl != null &&
                    shareWhiteIconUrl!.isNotEmpty &&
                    _showAnimatedShareIcon
                    ? Image.network(
                  shareWhiteIconUrl!,
                  height: 24,
                  width: 24,
                )
                    : Ficon(
                    ICON_FENXIANG, 22, _titleColorAlpha(Color(0xFF000000))),
              ),
            ),

            // 跳转购物车和收藏按钮
            if (changeNotifier.titleBarModel!.rightCustomIconFont != null)
              FFloat(
                    (_) {
                  return Container(
                    child: Text(
                      carAndCollectPopTips ?? '',
                      style: TextStyle(color: Color(0xffffffff)),
                    ),
                  );
                },
                controller: floatController,
                anchor: GestureDetector(
                    onTap: () {
                      const String spmCD = 'title_bar.cartAndCollect';
                      changeNotifier.ctrlClicked(context, spmCD,
                          'navCartAndCollect', <String, String>{});
                      changeNotifier.shopCarClick(context);
                    },
                    child: Padding(
                        padding: const EdgeInsets.only(top: 2, right: 12),
                        child: Ficon(
                            changeNotifier.titleBarModel!.rightCustomIconFont!,
                            22,
                            _titleColorAlpha(Color(0xFF000000))))),
                autoDismissDuration: Duration(milliseconds: 3000),
                hideTriangle: false,
                alignment: FFloatAlignment.bottomRight,
                strokeWidth: 1,
                canTouchOutside: false,
                // 有他才展示小三角
                corner: FFloatCorner.all(6),
                // 微调小三角位置
                triangleOffset: const Offset(45, 0),
                padding:
                const EdgeInsets.only(left: 9, right: 9, top: 6, bottom: 8),
                color: Color(0xCC000000),
              )
          ],
        ),

        // moreItem: Padding(
        //   padding: const EdgeInsets.only(top: 4, right: 3),
        //   child: Ficon(ICON_GENGDUO, 24, _titleColorAlpha(Color(0xFF000000))),
        // ),
        titleStyle: TextStyle(fontWeight: FontWeightExt.bold),
        hasMore: true,
        immersiveType: 1,
        backgroundType: BackgroundType.WHITE,
        themeUpdateCallback: FTitleBarAdapter.themeUpdateCallback,
        moreChildren: FTitleBarAdapter.getPopupMenuItemData(),
        onMorePressed: (int index) {
          // 更多下拉菜单点击埋点
          final List<FTitleBarPopupMenuItemData> menuItemData =
          FTitleBarAdapter.getPopupMenuItemData();
          final FTitleBarPopupMenuItemData itemData = menuItemData[index];
          final String spmCD = 'title_bar_menu.${itemData.spmName}';
          changeNotifier.ctrlClicked(context, spmCD, 'titlebarMenu',
              <String, String>{'index': '$index'});

          FTitleBarAdapter.doPopupMenuClickEvent(index, context);
        });
  }

  void registerEvent() {
    FBroadcast.instance(changeNotifier.uniqueId)
        .registerSingle('titleBarStatus', (dynamic arg, _) {
      if (mounted) {
        final double offset = arg['offset'];
        backgroundAlphaNotifier?.value = offset / 300 > 1 ? 1 : offset / 300;
        alphaNotifierData = backgroundAlphaNotifier?.value ?? 0.0;

        ///tab点击滚动时不执行以下逻辑（页面滚动tab联动）
        if (!TitleBarModel.tabScrolling && offset > 0) {
          _showAnimatedShareIcon = false;
          for (int i = titleBarModel!.tabList.length - 1; i >= 0; i--) {
            final String? id = titleBarModel!.tabList[i].id;
            final GlobalKey? globalKey = titleBarModel!.tabGlobalKey[id];
            final RenderObject? renderBox =
            globalKey?.currentContext?.findRenderObject();
            if (renderBox is RenderBox) {
              final Offset offset = renderBox.localToGlobal(Offset.zero);
              if (offset.dy < 300) {
                if (_tabController?.index != i) {
                  initialIndex = i;
                  oldTabIndex = i;
                  _tabController?.animateTo(i);
                }
                break;
              }
            }
          }
        }
      }
    });
    FBroadcast.instance(changeNotifier.uniqueId)
        .registerSingle('cardAndCollectPop', (dynamic arg, _) {
      carAndCollectPopTips = arg['message'];
      floatController.show();
      // ToastUtils.toast(context, arg['message']);
    });
    // FBroadcast.instance(fcontext_fliggyRouteObserver)
    //     .register(BoostContainer.of(context)?.topPageUniqueId() ?? '',
    //         (dynamic value, _) {
    //   if (value == 'pageHide') {
    //     floatController.dismiss();
    //   }
    // });
  }

  @override
  void dispose() {
    _tabController?.dispose();
    floatController.dispose();
    super.dispose();
  }

  Widget _buildTab() {
    return Offstage(
      child: Container(
        decoration: BoxDecoration(
          color: _tabColorAlpha(Color(0xffffffff)),
          boxShadow: widget.themeAlpha > 0.9
              ? const <BoxShadow>[
            BoxShadow(
              color: Color(0x20999999), // 阴影的颜色
              spreadRadius: -10, // 阴影扩散的范围
              blurRadius: 9, // 阴影的模糊程度
              offset: Offset(0, 15), // 阴影的位置
            ),
          ]
              : null,
        ),
        height: 40,
        width: double.infinity,
        child: TabBar(
          indicatorSize: TabBarIndicatorSize.label,
          tabs: _buildSubTab(),
          unselectedLabelColor: _tabColorAlpha(Color(0xFF000000)),
          labelColor: _tabColorAlpha(Color(0xFF000000)),
          unselectedLabelStyle:
          const TextStyle(fontSize: 15, fontWeight: FontWeight.normal),
          labelStyle:
          const TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
          labelPadding: EdgeInsets.symmetric(horizontal: 0),
          controller: _tabController,
          indicator: UnderlineTabIndicator(
            borderSide: BorderSide(
              width: 2.0, // 减小宽度，从 2.0 改为 1.0
              color: _tabColorAlpha(Color(0xFF000000)),
            ),
            // insets: const EdgeInsets.symmetric(horizontal: 16.0),
          ),
          onTap: (int index) {
            if (index != _tabController?.previousIndex &&
                !TitleBarModel.tabScrolling) {
              TitleBarModel.tabScrolling = true;
              oldTabIndex = index;
              final String spmD = titleBarModel!.tabList[index].spmD ?? 'd0';
              final String spm =
                  '${TrackUtils.vacationDetailSpmAB}.title_lift.$spmD';
              changeNotifier
                  .ctrlClicked(context, spm, 'naviTabs', <String, String>{});
              changeNotifier.scrollToComponent(index);
            } else {
              _tabController?.animateTo(oldTabIndex,
                  duration: Duration(milliseconds: 0));
            }
          },
        ),
      ),
      offstage: widget.themeAlpha == 0.0,
    );
  }

  List<Widget> _buildSubTab() {
    final List<Widget> tabs = <Widget>[];
    for (final TitleTabModel model in titleBarModel!.tabList) {
      tabs.add(
          Text(model.label!, softWrap: false, overflow: TextOverflow.fade));
    }
    return tabs;
  }

  Color _tabColorAlpha(Color color) {
    return color.withAlpha((255 * widget.themeAlpha).toInt());
  }

  Color _titleColorAlpha(Color color) {
    if (widget.themeAlpha > 0.5) {
      color = color.withOpacity(widget.themeAlpha);
    } else {
      color = Color(0xffffffff).withOpacity(1.0 - widget.themeAlpha);
    }
    return color;
  }
}

import '../../../custom_widget/service_widget.dart';
import '../../../utils/safe_access.dart';

class RouteBrandModel {
  bool get visible => mainInfo != null && mainInfo != '';
  DetailInfo? detailInfo;
  String? icon;
  String? labelInfo;
  String? popWindowUrl;
  String? mainInfo;
  List<ServiceModel>? serviceModels;

  RouteBrandModel.fromJson(Map<String, dynamic> dataModel) {
    final Map<String, dynamic> serviceTimeInfo =
        SafeAccess.safeParseMap(dataModel['routeBrand']);
    final Map<String, dynamic> json =
        SafeAccess.safeParseMap(serviceTimeInfo['data']);
    detailInfo = json['detailInfo'] != null
        ? DetailInfo.fromJson(SafeAccess.safeParseMap(json['detailInfo']))
        : null;
    icon = SafeAccess.safeParseString(json['icon']);
    labelInfo = SafeAccess.safeParseString(json['labelInfo']);
    mainInfo = SafeAccess.safeParseString(json['mainInfo']);
    popWindowUrl = json['popWindowUrl'];
    _createServiceModels();
  }

  void _createServiceModels() {
    serviceModels = <ServiceModel>[];
    final ServiceModel serviceModel = ServiceModel(
      mainInfo,
    );
    serviceModels!.add(serviceModel);
  }
}

class DetailInfo {
  String? backgroundImage;
  List<DetailList>? detailList;
  String? title;

  DetailInfo.fromJson(Map<String, dynamic> json) {
    backgroundImage = SafeAccess.safeParseString(json['backgroundImage']);
    if (json['detailList'] != null) {
      detailList = <DetailList>[];
      SafeAccess.safeParseList(json['detailList']).forEach((dynamic v) {
        detailList!.add(DetailList.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    title = SafeAccess.safeParseString(json['title']);
  }
}

class DetailList {
  List<SubDetailList>? subDetailList;
  String? title;
  String? desc;

  DetailList.fromJson(Map<String, dynamic> json) {
    if (json['subDetailList'] != null) {
      subDetailList = <SubDetailList>[];
      SafeAccess.safeParseList(json['subDetailList']).forEach((dynamic v) {
        subDetailList!.add(SubDetailList.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    title = SafeAccess.safeParseString(json['title']);
    desc = SafeAccess.safeParseString(json['desc']);
  }
}

class SubDetailList {
  String? desc;
  String? icon;
  String? title;

  SubDetailList.fromJson(Map<String, dynamic> json) {
    desc = SafeAccess.safeParseString(json['desc']);
    icon = SafeAccess.safeParseString(json['icon']);
    title = SafeAccess.safeParseString(json['title']);
  }
}

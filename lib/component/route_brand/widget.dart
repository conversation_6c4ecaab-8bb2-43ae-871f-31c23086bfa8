import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../custom_widget/null_widget.dart';
import '../../../custom_widget/service_widget.dart';
import 'model.dart';
import 'notifier.dart';

class RouteBrandWidget extends StatelessWidget {
  const RouteBrandWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RouteBrandChangeNotifier changeNotifier =
        Provider.of<RouteBrandChangeNotifier>(context);
    final RouteBrandModel? routeBrandModel = changeNotifier.routeBrandModel;
    return routeBrandModel == null || !routeBrandModel.visible
        ? nullWidget
        : ServiceWidget(
            routeBrandModel.labelInfo,
            routeBrandModel.serviceModels!,
            jumpClick: () {
              changeNotifier.openPad(context);
            },
          );
  }
}

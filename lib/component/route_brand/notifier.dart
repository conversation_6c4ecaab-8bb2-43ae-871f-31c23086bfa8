import 'package:flutter/cupertino.dart';

import '../../../render/component/component_change_notifier.dart';
import '../../../render/component/component_context.dart';
import '../../custom_widget/dialog_webview.dart';
import 'model.dart';

class RouteBrandChangeNotifier extends ComponentChangeNotifier {
  RouteBrandModel? get routeBrandModel => itemDetailModel.routeBrandModel;

  RouteBrandChangeNotifier(ComponentContext context) : super(context);
  void openPad(BuildContext context) {
    final String? jumpUrl = routeBrandModel!.popWindowUrl;
    if(jumpUrl != null){
      final DialogWebView dialogWebView = DialogWebView(context: context, url: jumpUrl,itemDetailEngine: itemDetailEngine,);
      dialogWebView.showPop();
    }
  }
}

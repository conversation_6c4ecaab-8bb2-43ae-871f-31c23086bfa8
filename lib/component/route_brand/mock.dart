const String mockData = '''
{
  "routeBrand": {
    "data": {
      "detailInfo": {
        "backgroundImage": "https://img.alicdn.com/imgextra/i4/O1CN018ieH2V21dG2PI9Ch2_!!6000000007007-2-tps-84-84.png",
        "detailList": [
          {
            "subDetailList": [
              {
                "desc": "保障实际行程与商品描述一致，行程中遇增加购物、酒店不一致、交通不一致、增加同团人数、景点不一致，投诉成立必获赔付。",
                "icon": "https://img.alicdn.com/imgextra/i4/O1CN018ieH2V21dG2PI9Ch2_!!6000000007007-2-tps-84-84.png",
                "title": "行程一致保障"
              },
              {
                "desc": "保障实际行程与商品描述一致，行程中遇增加购物、酒店不一致、交通不一致、增加同团人数、景点不一致，投诉成立必获赔付。",
                "icon": "https://img.alicdn.com/imgextra/i4/O1CN018ieH2V21dG2PI9Ch2_!!6000000007007-2-tps-84-84.png",
                "title": "行中护航保障"
              }
            ],
            "title": "出行五重保障"
          },
          {
            "desc": "打上“无忧退”标签的商品，订单在出行前1天（12点前），用户可免费取消；",
            "title": "无忧退"
          },
          {
            "desc": "打上“无忧退”标签的商品，订单在出行前1天（12点前），用户可免费取消；",
            "title": "无购物"
          }
        ],
        "title": "行程描述一致保障说明"
      },
      "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01FbSiXK1zOU3KIHRcB_!!6000000006704-2-tps-306-52.png",
      "labelInfo": "保障",
      "mainInfo": "无自费·成团保障无自费·成团保障无自费·成团保障"
    },
    "tag": "routeBrand"
  }
}
''';

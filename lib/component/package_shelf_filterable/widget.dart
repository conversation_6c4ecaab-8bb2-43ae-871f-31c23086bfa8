import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class PackageShelfFilterableWidget extends StatelessWidget {
  const PackageShelfFilterableWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final PackageShelfFilterableChangeNotifier changeNotifier =
        Provider.of<PackageShelfFilterableChangeNotifier>(context);
    return Container();
  }
}
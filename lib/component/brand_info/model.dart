import '../../utils/safe_access.dart';

/// 品牌心智,对应的 widget 在
class BrandInfoModel {
  BrandInfoDataModel? data;

  BrandInfoModel({this.data});

  BrandInfoModel.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null
        ? BrandInfoDataModel.fromJson(
        SafeAccess.safeParseMap(json['data']))
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class BrandInfoDataModel {
  int? soldCount;
  String? description;
  String? icon;
  String? sold;
  bool? showPop;

  BrandInfoDataModel({this.soldCount, this.description, this.icon, this.sold, this.showPop});

  BrandInfoDataModel.fromJson(Map<String, dynamic> json) {
    soldCount = SafeAccess.safeParseInt(json['soldCount']);
    description = SafeAccess.safeParseString(json['description']);
    icon = SafeAccess.safeParseString(json['icon']);
    sold = SafeAccess.safeParseString(json['sold']);
    showPop = SafeAccess.safeParseBoolean(json['showPop']);
  }


  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['soldCount'] = this.soldCount;
    data['description'] = this.description;
    data['icon'] = this.icon;
    data['sold'] = this.sold;
    data['showPop'] = this.showPop;
    return data;
  }
}

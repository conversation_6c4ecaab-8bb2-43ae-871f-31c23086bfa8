import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class TravelHotelStoreWidget extends StatelessWidget {
  const TravelHotelStoreWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TravelHotelStoreChangeNotifier changeNotifier =
        Provider.of<TravelHotelStoreChangeNotifier>(context);
    return Container();
  }
}
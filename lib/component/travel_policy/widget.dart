import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class TravelPolicyWidget extends StatelessWidget {
  const TravelPolicyWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TravelPolicyChangeNotifier changeNotifier =
        Provider.of<TravelPolicyChangeNotifier>(context);
    return Container();
  }
}
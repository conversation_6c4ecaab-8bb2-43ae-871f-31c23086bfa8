import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class VacationHotelScenicIntroCardWidget extends StatelessWidget {
  const VacationHotelScenicIntroCardWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VacationHotelScenicIntroCardChangeNotifier changeNotifier =
        Provider.of<VacationHotelScenicIntroCardChangeNotifier>(context);
    return Container();
  }
}
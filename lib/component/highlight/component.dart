import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 商家说亮点
class HighlightComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<HighlightChangeNotifier>.value(
      value: HighlightChangeNotifier(context),
      child: const HighlightWidget(),
    );
  }
}
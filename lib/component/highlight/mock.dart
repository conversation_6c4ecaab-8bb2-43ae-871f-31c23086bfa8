// ignore_for_file: always_specify_types, prefer_single_quotes

const Map<String, dynamic> mockData = <String, dynamic>{
  "highLight": {
    "data": {
      "contents": [
        {
          "type": "text",
          "value": "隐于千岛湖静谧的湖光山色之中，极目之处尽是漫山葱茏"
        },
        {
          "type": "text",
          "value": "配有千岛湖蓝湾水上乐园，让您尽情玩乐，享受亲子时光"
        },
        {
          "type": "text",
          "value": "健身房配备各类有氧及力量型锻炼设备，享受运动、焕新活力！"
        },
        {
          "type": "text",
          "value": "浸泡在温暖的汤泉中，赴一场无关风月的闲适"
        }
      ],
      "descList": [
        "隐于千岛湖静谧的湖光山色之中，极目之处尽是漫山葱茏",
        "配有千岛湖蓝湾水上乐园，让您尽情玩乐，享受亲子时光",
        "健身房配备各类有氧及力量型锻炼设备，享受运动、焕新活力！",
        "浸泡在温暖的汤泉中，赴一场无关风月的闲适"
      ],
      "picUrl": "",
      "title": "商家说亮点"
    },
    "tag": "highLight"
  }
};

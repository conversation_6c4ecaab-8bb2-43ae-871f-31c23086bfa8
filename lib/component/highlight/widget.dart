import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../custom_widget/block_title.dart';
import '../../utils/common_config.dart';
import 'notifier.dart';
import 'package:flutter_common/api/empty.dart';

class HighlightWidget extends StatelessWidget {
  const HighlightWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final HighlightChangeNotifier changeNotifier =
        Provider.of<HighlightChangeNotifier>(context);
    changeNotifier.fromJson();
    final Map<dynamic, dynamic>? moduleData = changeNotifier.moduleData;
    if (moduleData == null) {
      return empty;
    }
    return Container(
        margin: const EdgeInsets.only(bottom: itemDivider),
        padding: const EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 9),
        decoration: BoxDecoration(
            color: Color(0xffffffff),
            borderRadius: BorderRadius.circular(cardBorderRadius)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            BlockTitle(moduleData['title'],),
            _buildContent(moduleData['descList'])
          ],
        ));
  }

  Widget _buildContent(List<dynamic> datas) {
    final List<Widget> list = <Widget>[];
    for (final String data in datas) {
      list.add(Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: Text('·$data',
              style:
                  const TextStyle(color: Color(0xFF919499), fontSize: 14.0))));
    }
    return Container(
        margin: const EdgeInsets.only(top: 12.0),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start, children: list));
  }
}


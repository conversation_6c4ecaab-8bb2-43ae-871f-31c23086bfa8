import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import './mock.dart';
class HighlightChangeNotifier extends ComponentChangeNotifier {
  HighlightChangeNotifier(ComponentContext context) : super(context);
  Map<dynamic, dynamic>? moduleData;

  @override
  void fromJson() {
    //mock数据
    // moduleData = mockData['highLight']['data'];
    // return;
    if (dataModel == null || dataModel['highLight'] == null || dataModel['highLight']['data'] == null) {
      return;
    }
    moduleData = dataModel['highLight']['data'];
  }
}

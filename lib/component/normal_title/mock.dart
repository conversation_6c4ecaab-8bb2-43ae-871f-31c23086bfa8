const String mockData = '''
{
"title": {
      "data": {
        "itemTitle": "[北京环球度假区-1日门票]北京环球影城指定单日门票",
        "shareInfo": {
          "image_url": "https://img.alicdn.com/bao/uploaded/i4/6000000000167/O1CN019vMc8S1D6WvP4KAvT_!!6000000000167-0-itemdesc.jpg",
          "shareIconFont": "ꄪ",
          "text_context": "Fliggy商品",
          "title_context": "[北京环球度假区-1日门票]北京环球影城指定单日门票",
          "url_content": "https://market.m.taobao.com/app/trip/h5-traveldx-detail/pages/index/index.html?id=623799150375&_fli_newpage=1&_fli_native_ver=9.9.3&categoryId=*********"
        },
        "titleIcon": "https://gw.alicdn.com/imgextra/i2/O1CN01md1bVm1acnzZ6G28h_!!6000000003351-2-tps-336-90.png",
        "titleIconList": [
          "https://gw.alicdn.com/imgextra/i2/O1CN01md1bVm1acnzZ6G28h_!!6000000003351-2-tps-336-90.png",
          "https://gw.alicdn.com/tfs/TB1ZH2upkcx_u4jSZFlXXXnUFXa-168-90.png"
        ],
        "subBrand": {
          "detailInfo": {
            "backgroundColor": "#fef5ec",
            "backgroundImage": "https://gw.alicdn.com/imgextra/i1/O1CN017oT8Ew1TOshIOvi1Z_!!6000000002373-0-tps-1125-375.jpg",
            "closeIcon": "https://gw.alicdn.com/imgextra/i2/O1CN01ZqbY1E1ZfGYgn5DJI_!!6000000003221-2-tps-96-96.png",
            "detailList": [
              {
                "desc": "每单独立成团，不与别人拼团，出行更私密",
                "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01BJZniW20kIX2m5CNY_!!6000000006887-2-tps-56-56.png",
                "subDetailList": [],
                "title": "独立成团"
              },
              {
                "desc": "行中独立用车，随走随停，并可按需灵活调整",
                "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01BJZniW20kIX2m5CNY_!!6000000006887-2-tps-56-56.png",
                "subDetailList": [],
                "title": "行程自由"
              },
              {
                "desc": "纯玩无购物行程，包含至少一次以上当地特色体验",
                "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01BJZniW20kIX2m5CNY_!!6000000006887-2-tps-56-56.png",
                "subDetailList": [],
                "title": "深度纯玩"
              }
            ],
            "titleIcon": "https://gw.alicdn.com/imgextra/i1/O1CN01rKkC1H1X7iLX0QSDt_!!6000000002877-2-tps-248-80.png"
          },
          "popWindowUrl": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/route-info?type=routeQualitySelected"
        }
      },
      "tag": "title"
    }
}
''';

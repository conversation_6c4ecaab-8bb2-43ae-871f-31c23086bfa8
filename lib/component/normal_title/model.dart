import '../../utils/safe_access.dart';
import '../rate_and_share/model.dart';

class NormalTitleDataModel {
  String? itemTitle;
  ShareInfo? shareInfo;
  String? titleIcon;
  List<String>? titleIconList;
  Map<String, dynamic>? subBrand;

  NormalTitleDataModel.fromJson(Map<String, dynamic> dataModel) {
   final Map<String,dynamic> title = SafeAccess.safeParseMap(dataModel['title']);
   final Map<String,dynamic> json = SafeAccess.safeParseMap(title['data']);
    itemTitle = SafeAccess.safeParseString(json['itemTitle']);
   shareInfo = json['shareInfo'] != null
       ? ShareInfo.fromJson(SafeAccess.safeParseMap(json?['shareInfo']))
       : null;
    titleIcon = SafeAccess.safeParseString(json['titleIcon']);
    titleIconList =
    List<String>.from(SafeAccess.safeParseList(json['titleIconList']));
    subBrand = json['subBrand'] != null
        ? SafeAccess.safeParseMap(json['subBrand'])
        : null;
    _resetIconList();
  }

  void _resetIconList() {
    if (titleIconList != null && titleIconList!.isEmpty &&
        titleIcon!.isNotEmpty) {
      titleIconList!.add(titleIcon!);
    }
  }
}

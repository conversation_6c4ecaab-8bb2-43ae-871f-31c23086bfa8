import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class TravelDetailWidget extends StatelessWidget {
  const TravelDetailWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TravelDetailChangeNotifier changeNotifier =
        Provider.of<TravelDetailChangeNotifier>(context);
    return Container();
  }
}
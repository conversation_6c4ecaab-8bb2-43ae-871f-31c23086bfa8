import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class VacationTicketMarketWidget extends StatelessWidget {
  const VacationTicketMarketWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VacationTicketMarketChangeNotifier changeNotifier =
        Provider.of<VacationTicketMarketChangeNotifier>(context);
    return Container();
  }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class TopRadiusDividerWidget extends StatelessWidget {
  const TopRadiusDividerWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final TopRadiusDividerChangeNotifier changeNotifier =
        Provider.of<TopRadiusDividerChangeNotifier>(context);
    return Container();
  }
}
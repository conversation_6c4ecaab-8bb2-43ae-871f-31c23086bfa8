import 'dart:convert';

import 'package:flutter/cupertino.dart';

import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import 'mock.dart';
import 'model.dart';
import 'package:fliggy_router/fliggy_router.dart';

class HotelPackageToCalendarChangeNotifier extends ComponentChangeNotifier {
  HotelPackageToCalendarModel? hotelPackageToCalendarModel;

  HotelPackageToCalendarChangeNotifier(ComponentContext context)
      : super(context);

  @override
  void fromJson() {
    hotelPackageToCalendarModel =
        HotelPackageToCalendarModel.fromJson(jsonDecode(storeMockData));
  }

  void jumpToPop(BuildContext context, String? url) {
    if (url != null) {
      FliggyNavigatorApi.getInstance().push(context, url, anim: Anim.slide);
    }
  }
}

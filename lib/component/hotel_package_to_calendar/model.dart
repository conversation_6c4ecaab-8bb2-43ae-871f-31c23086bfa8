import 'dart:io';
import 'dart:ui';

import '../../utils/safe_access.dart';

class HotelPackageToCalendarModel {
  MultiShotelComponentInfoVO? multiShotelComponentInfoVO;
  SingleShotelComponentInfoVO? singleShotelComponentInfoVO;

  HotelPackageToCalendarModel.fromJson(Map<String, dynamic> dataModel) {
    final Map<String, dynamic> json =
        SafeAccess.safeParseMap(dataModel['shotelComponentInfoVO']);
    multiShotelComponentInfoVO = json['multiShotelComponentInfoVO'] != null
        ? MultiShotelComponentInfoVO.fromJson(
            SafeAccess.safeParseMap(json['multiShotelComponentInfoVO']))
        : null;
    singleShotelComponentInfoVO = json['singleShotelComponentInfoVO'] != null
        ? SingleShotelComponentInfoVO.fromJson(
            SafeAccess.safeParseMap(json['singleShotelComponentInfoVO']))
        : null;
  }
}

class MultiShotelComponentInfoVO {
  List<SubTitle>? subTitle;
  String? prefix;
  String? omitOverLengthText;
  String? suffix;
  List<TextVO> subTitleTips = <TextVO>[];
  String? jumpUrl;
  String? desc;
  double? omitOverLengthTextWidth;

  MultiShotelComponentInfoVO.fromJson(Map<String, dynamic> json) {
    if (json['subTitle'] != null) {
      subTitle = <SubTitle>[];
      SafeAccess.safeParseList(json['subTitle']).forEach((dynamic v) {
        subTitle!.add(SubTitle.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    omitOverLengthTextWidth = 150;
    prefix = json['omitTextVO']?['prefix'];
    omitOverLengthText = json['omitTextVO']?['omitOverLengthText'];
    suffix = json['omitTextVO']?['suffix'];
    if((prefix?.length??0)>4){
      omitOverLengthTextWidth = omitOverLengthTextWidth! - (prefix!.length-4)*15;
    }
    if((suffix?.length??0)>10){
      omitOverLengthTextWidth = omitOverLengthTextWidth! - (suffix!.length-10)*15;
    }
    // if((omitOverLengthText?.length??0)>14){
    //   omitOverLengthText = '${omitOverLengthText!.substring(0, 14 - 3)}...';
    // }


    jumpUrl = SafeAccess.safeParseString(json['jumpUrl']);
    desc = SafeAccess.safeParseString(json['desc']);
    int maxLength = 330;
    if (subTitle != null && subTitle!.isNotEmpty) {
      for (int i = 0; i < subTitle!.length; i++) {
        int subTitleLength = 0;
        final SubTitle e = subTitle![i];
        if (e.textVO?.desc != null) {
          subTitleLength += e.textVO!.desc!.length * 12;
        }
        TextVO? t1;
        TextVO? t2;
        if (e.shotelTag != null) {
          t1 = TextVO(desc: '“', fontColor: '#7c5a46', isColon: true);
          t2 = TextVO(desc: '”', fontColor: '#7c5a46', isColon: true);
          subTitleLength += (e.shotelTag!.desc!.length + 3) * 12;
        }
        subTitleLength += 12;
        final TextVO d = TextVO(desc: '|', fontColor: '#7c5a46');

        maxLength -= subTitleLength;
        if (maxLength > 0) {
          if (e.textVO?.desc != null) {
            subTitleTips.add(e.textVO!);
          }
          if (t1 != null && t2 != null) {
            subTitleTips.add(t1);
            subTitleTips.add(e.shotelTag!);
            subTitleTips.add(t2);
          }
          subTitleTips.add(d);
        }
      }
      if(subTitleTips.isNotEmpty){
        subTitleTips.removeAt(subTitleTips.length-1);
      }
    }
  }
}

class SubTitle {
  TextVO? shotelTag;
  TextVO? textVO;

  SubTitle.fromJson(Map<String, dynamic> json) {
    shotelTag = json['shotelTag'] != null
        ? TextVO.fromJson(SafeAccess.safeParseMap(json['shotelTag']))
        : null;
    textVO = json['textVO'] != null
        ? TextVO.fromJson(SafeAccess.safeParseMap(json['textVO']))
        : null;
  }
}

class SingleShotelComponentInfoVO {
  List<TextVO>? promotionTags;
  List<TextVO>? subTitleTips;
  List<ShotelDetailComponents>? shotelDetailComponents;
  List<TextVO>? infoTags;
  List<TextVO> titleList = <TextVO>[];
  String? shotelName;
  String? jumpUrl;
  String? jumpDesc;
  bool? highLight;

  SingleShotelComponentInfoVO.fromJson(Map<String, dynamic> json) {
    if (json['promotionTags'] != null) {
      promotionTags = <TextVO>[];
      SafeAccess.safeParseList(json['promotionTags']).forEach((dynamic v) {
        promotionTags!.add(TextVO.fromJson(SafeAccess.safeParseMap(v)));
      });
      if (promotionTags!.isNotEmpty) {
        highLight = promotionTags?[0].highLight ?? false;
      }
    }

    int maxLength = 270;
    subTitleTips = <TextVO>[];
    if (promotionTags != null && promotionTags!.isNotEmpty) {
      for (int i = 0; i < promotionTags!.length; i++) {
        int subTitleLength = 0;
        final TextVO textVO = promotionTags![i];
        if (textVO?.desc != null) {
          //计算字符宽度,中文12  数字6
          subTitleLength += textVO!.desc!.length * 12-countNumbers(textVO!.desc!)*6;

        }
        subTitleLength += 12;
        final TextVO d = TextVO(desc: '|', fontColor: '#7c5a46');

        maxLength -= subTitleLength;
        if (maxLength > 0) {
          if (textVO?.desc != null) {
            subTitleTips!.add(textVO!);
          }
          subTitleTips!.add(d);
        }
      }
      if(subTitleTips!.isNotEmpty){
        subTitleTips!.removeAt(subTitleTips!.length-1);
      }
    }
    if (json['shotelDetailComponents'] != null) {
      shotelDetailComponents = <ShotelDetailComponents>[];
      SafeAccess.safeParseList(json['shotelDetailComponents'])
          .forEach((dynamic v) {
        shotelDetailComponents!
            .add(ShotelDetailComponents.fromJson(SafeAccess.safeParseMap(v)));
      });
      if ((shotelDetailComponents?[0].content?[0].desc?.isNotEmpty ?? false) &&
          shotelDetailComponents?[0].mainTitle?[0].desc != '暂无评分') {
        shotelDetailComponents?[0].content?[0].desc =
            '“${shotelDetailComponents?[0].content?[0].desc}”';
      }
    }
    if (json['infoTags'] != null) {
      infoTags = <TextVO>[];
      SafeAccess.safeParseList(json['infoTags']).forEach((dynamic v) {
        infoTags!.add(TextVO.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    shotelName = SafeAccess.safeParseString(json['shotelName']);
    jumpUrl = SafeAccess.safeParseString(json['jumpUrl']);
    jumpDesc = SafeAccess.safeParseString(json['jumpDesc']);
    final TextVO titleText =
        TextVO(desc: shotelName, fontSize: 15, fontColor: '#0F131A',fontWeight: Platform.isIOS
            ? FontWeight.w600
            : FontWeight.w600);
    titleList.add(titleText);
    if (infoTags != null) {
      int allTagsLength = 0;
      infoTags?.forEach((TextVO element) {
        allTagsLength += (element.desc?.length ?? 0) * 10 + 10; //tag字号+间距
      });
      final int titleNum = (331 * 2 - allTagsLength) ~/ 15;
      if (shotelName!.length > titleNum && shotelName!.length > 4) {
        titleText.desc = '${shotelName!.substring(0, titleNum - 3)}...';
      }
      titleList.addAll(infoTags!);
    }
  }
  int countNumbers(String input) {
    // 使用正则表达式匹配所有数字
   final RegExp regExp = RegExp(r'\d');

    // 查找所有匹配，并返回匹配的数量
    return regExp.allMatches(input).length;
  }
}


class ShotelDetailComponents {
  String? backGroundColor;
  List<TextVO>? subTitle;
  List<TextVO>? mainTitle;
  String? jumpUrl;
  List<TextVO>? content;
  String? backGroundPic;

  ShotelDetailComponents.fromJson(Map<String, dynamic> json) {
    backGroundColor = json['backGroundColor'];
    if (json['subTitle'] != null) {
      subTitle = <TextVO>[];
      SafeAccess.safeParseList(json['subTitle']).forEach((dynamic v) {
        subTitle!.add(TextVO.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    if (json['mainTitle'] != null) {
      mainTitle = <TextVO>[];
      SafeAccess.safeParseList(json['mainTitle']).forEach((dynamic v) {
        mainTitle!.add(TextVO.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    jumpUrl = SafeAccess.safeParseString(json['jumpUrl']);
    if (json['content'] != null) {
      content = <TextVO>[];
      SafeAccess.safeParseList(json['content']).forEach((dynamic v) {
        content!.add(TextVO.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    backGroundPic = json['backGroundPic'];
  }
}

class TextVO {
  String? backgroundColor;
  String? icon;
  double? fontSize;
  String? desc;
  String? fontColor;
  bool? highLight;
  String? score;
  bool? isColon;
  FontWeight? fontWeight = FontWeight.w400;

  TextVO(
      {this.backgroundColor,
      this.icon,
      this.fontColor,
      this.desc,
      this.fontSize,
      this.highLight,
      this.isColon,this.fontWeight});

  TextVO.fromJson(Map<String, dynamic> json) {
    backgroundColor = json['backgroundColor'];
    highLight = SafeAccess.safeParseBoolean(json['highLight']);
    icon = json['icon'];
    // fontSize = SafeAccess.safeParseDouble(json['fontSize'] ?? 12);
    desc = json['desc'] ?? json['text'];
    score = SafeAccess.safeParseString(json['score']);
    fontColor =
        SafeAccess.safeParseString(json['fontColor'] ?? json['textColor']);
  }
}

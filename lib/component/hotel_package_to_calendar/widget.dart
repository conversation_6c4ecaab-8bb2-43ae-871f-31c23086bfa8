import 'dart:io';

import 'package:flutter/material.dart';
import '../../render/component/component_change_notifier.dart';
import '../../utils/common_config.dart';
import '../../utils/common_util.dart';
import 'model.dart';
import 'package:ficonfont/ficonfont.dart';
import 'package:fliggy_router/fliggy_router.dart';

class HotelPackageToCalendarWidget extends StatelessWidget {
  HotelPackageToCalendarModel? hotelPackageToCalendarModel;
  ComponentChangeNotifier? changeNotifier;

  HotelPackageToCalendarWidget(this.hotelPackageToCalendarModel,this.changeNotifier, {Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final MultiShotelComponentInfoVO? multiShotelComponentInfoVO =
        hotelPackageToCalendarModel?.multiShotelComponentInfoVO;
    final SingleShotelComponentInfoVO? singleShotelComponentInfoVO =
        hotelPackageToCalendarModel?.singleShotelComponentInfoVO;
    ;
    if (multiShotelComponentInfoVO != null) {
      changeNotifier?.ctrlExposure(context, 'sHotel.multiStore', <String,dynamic>{});
      return Container(
        child: buildMultiShotelWidegt(context, multiShotelComponentInfoVO),
      );
    }
    if (singleShotelComponentInfoVO != null) {
      changeNotifier?.ctrlExposure(context, 'sHotel.singleStore', <String,dynamic>{});
      return Container(
        child: buildSingleShotelWidegt(context, singleShotelComponentInfoVO),
      );
    }
    return SizedBox.shrink();
  }

  Widget buildMultiShotelWidegt(BuildContext context,
      MultiShotelComponentInfoVO multiShotelComponentInfoVO) {
    return Container(
        padding: EdgeInsets.only(left: 12, right: 12, top: 15, bottom: 11),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              if (multiShotelComponentInfoVO.desc != null)
                Text(
                  multiShotelComponentInfoVO.desc!,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    color: Color(0xff919499),
                    fontSize: 13,
                  ),
                ),
              GestureDetector(
                onTap: () {
                  changeNotifier?.ctrlClicked(context, 'sHotel.multiStore','sHotel' ,<String,dynamic>{});
                  jumpToPop(context, multiShotelComponentInfoVO.jumpUrl);
                },
                child: Container(
                    margin: const EdgeInsets.only(top: 6),
                    child: Row(children: <Widget>[
                      if (multiShotelComponentInfoVO.prefix != null)
                        Text(
                          multiShotelComponentInfoVO.prefix!,
                          style: TextStyle(
                            color: Color(0xff0F131A),
                            fontWeight: Platform.isIOS
                                ? FontWeight.w500
                                : FontWeight.w600,
                            fontSize: 15,
                          ),
                        ),
                      if (multiShotelComponentInfoVO.omitOverLengthText != null)
                        Container(
                          constraints: BoxConstraints(
                            maxWidth: multiShotelComponentInfoVO.omitOverLengthTextWidth??0, // 设置最大宽度
                          ),
                          child: Text(
                            multiShotelComponentInfoVO.omitOverLengthText!,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: Color(0xff0F131A),
                              fontWeight: Platform.isIOS
                                  ? FontWeight.w500
                                  : FontWeight.w600,
                              fontSize: 15,
                            ),
                          ),
                        ),
                      if (multiShotelComponentInfoVO.suffix != null)
                        Text(
                          multiShotelComponentInfoVO.suffix!,
                          style: TextStyle(
                            color: Color(0xff0F131A),
                            fontWeight: Platform.isIOS
                                ? FontWeight.w500
                                : FontWeight.w600,
                            fontSize: 15,
                          ),
                        ),
                      Ficon(
                          ICON_JINRUJIANTOUXIAO1,
                          // 0xeb22,
                          12,
                          const Color(0xff5C5F66)),
                    ])),
              ),
              if (multiShotelComponentInfoVO.subTitleTips.isNotEmpty)
                Container(
                  margin: EdgeInsets.only(top: 6),
                  child: RichText(
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      text: TextSpan(
                          children: _buildRichWidget(
                              multiShotelComponentInfoVO.subTitleTips,
                              textColor: '#7c5a46',
                              textSize: 12.0))),
                ),
            ]));
  }

  Widget buildSingleShotelWidegt(BuildContext context,
      SingleShotelComponentInfoVO singleShotelComponentInfoVO) {
    return Stack(
      children: <Widget>[
        if (singleShotelComponentInfoVO.highLight ?? false)
          Image.network(
            'https://gw.alicdn.com/imgextra/i4/O1CN01Eu6oA11QpV48c0vxz_!!6000000002025-2-tps-1428-532.png',
            fit: BoxFit.fill,
          ),
        Container(
            padding: EdgeInsets.only(left: 12, right: 12, top: 13, bottom: 11),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: <Widget>[
                  Container(
                    child: RichText(
                        //标题
                        // maxLines: 2,
                        // overflow: TextOverflow.ellipsis,
                        text: TextSpan(
                            style: TextStyle(
                                height: 1.4,
                                fontWeight: Platform.isIOS
                                    ? FontWeight.w500
                                    : FontWeight.w600),
                            children: _buildRichWidget(
                                singleShotelComponentInfoVO.titleList,
                                textSize: 10))),
                  ),
                  Container(
                      //tag
                      margin: const EdgeInsets.only(top: 7),
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            if (singleShotelComponentInfoVO.promotionTags !=
                                null)
                              Expanded(
                                child: RichText(
                                    text: TextSpan(
                                  children: _buildRichWidget(
                                      singleShotelComponentInfoVO.subTitleTips!,
                                      textSize: 12,
                                      textColor: '#b9babb'),
                                )),
                              ),
                            GestureDetector(
                              onTap: () {
                                changeNotifier?.ctrlClicked(context, 'sHotel.singleStore_info','sHotel.singleStore_info' ,<String,dynamic>{});
                                jumpToPop(context,
                                    singleShotelComponentInfoVO.jumpUrl);
                              },
                              child: Padding(
                                padding:
                                    const EdgeInsets.only(left: 12, right: 4),
                                child: Text(
                                    singleShotelComponentInfoVO.jumpDesc ??
                                        '酒店详情',
                                    textAlign: TextAlign.left,
                                    style: const TextStyle(
                                        fontSize: 12.00,
                                        color: Color(0xff5C5F66))),
                              ),
                            ),
                            Image.network(
                              'https://gw.alicdn.com/imgextra/i3/O1CN01k0Jszb1vW2K8DHojU_!!6000000006179-2-tps-14-24.png',
                              height: 6,
                              width: 3.5,
                            ),
                          ])),
                  Container(
                      //卡片
                      height: 61,
                      margin: EdgeInsets.only(top: 10),
                      child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            if (singleShotelComponentInfoVO
                                    .shotelDetailComponents?[0] !=
                                null)
                              GestureDetector(
                                onTap: () {
                                  changeNotifier?.ctrlClicked(context, 'sHotel.singleStore_comment','sHotel.singleStore_comment' ,<String,dynamic>{});
                                  jumpToPop(
                                      context,
                                      singleShotelComponentInfoVO
                                          .shotelDetailComponents?[0].jumpUrl);
                                },
                                child: Container(
                                  width: 145.00,
                                  margin: const EdgeInsets.only(right: 9),
                                  decoration: BoxDecoration(
                                    color: stringToColor(
                                        singleShotelComponentInfoVO
                                                .shotelDetailComponents?[0]
                                                .backGroundPic ??
                                            '#f7f7fe'), // 背景颜色
                                    border: Border.all(
                                      color: Color(0xffEBEBFF), // 边框颜色
                                      width: 0.5, // 边框宽度
                                    ),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  padding: EdgeInsets.only(
                                      left: 8, right: 9, top: 0, bottom: 1),
                                  child: _buildCardWidget(
                                      singleShotelComponentInfoVO
                                          .shotelDetailComponents![0]),
                                ),
                              ),
                            if (singleShotelComponentInfoVO
                                    .shotelDetailComponents?[1] !=
                                null)
                              Expanded(
                                child: GestureDetector(
                                  onTap: () {
                                    changeNotifier?.ctrlClicked(context, 'sHotel.singleStore_map','sHotel.singleStore_map' ,<String,dynamic>{});
                                    jumpToPop(
                                        context,
                                        singleShotelComponentInfoVO
                                            .shotelDetailComponents?[1]
                                            .jumpUrl);
                                  },
                                  child: Container(
                                    decoration: BoxDecoration(
                                      image: singleShotelComponentInfoVO
                                                  .shotelDetailComponents?[1]
                                                  .backGroundPic !=
                                              null
                                          ? DecorationImage(
                                              image: NetworkImage(
                                                  singleShotelComponentInfoVO
                                                      .shotelDetailComponents![
                                                          1]
                                                      .backGroundPic!),
                                            )
                                          : null,
                                      border: Border.all(
                                        color: Color(0xffE6F2FF), // 边框颜色
                                        width: 0.5, // 边框宽度
                                      ),
                                      borderRadius: BorderRadius.circular(6),
                                    ),
                                    padding: EdgeInsets.only(
                                        left: 9, right: 9, top: 0, bottom: 1),
                                    child: _buildCardWidget1(
                                        singleShotelComponentInfoVO
                                            .shotelDetailComponents![1]),
                                  ),
                                ),
                              ),
                          ]))
                ])),
      ],
    );
  }

  Widget _buildCardWidget(ShotelDetailComponents shotelDetailComponents) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          SizedBox(
              child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                if (shotelDetailComponents.mainTitle != null)
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(right: 6),
                      child: Row(
                        children: <Widget>[
                          if (shotelDetailComponents
                                  .mainTitle?[0]?.score?.isNotEmpty ??
                              false)
                            Container(
                              margin: EdgeInsets.only(
                                  right: 3, top: Platform.isIOS ? 1 : 0),
                              child: Text(
                                shotelDetailComponents.mainTitle![0]!.score!,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: stringToColor(shotelDetailComponents
                                          .mainTitle![0]?.fontColor ??
                                      '#0F131A'),
                                  fontWeight: Platform.isIOS
                                      ? FontWeight.w500
                                      : FontWeight.w600,
                                  fontFamily: 'fliggy_sans102_md',
                                  package: 'ffonts',
                                ),
                              ),
                            ),
                          if (shotelDetailComponents.mainTitle?[0]?.desc !=
                              null)
                            Expanded(
                              child: Text(
                                shotelDetailComponents.mainTitle![0]!.desc!,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 13,
                                  color: stringToColor(shotelDetailComponents
                                          .mainTitle![0]?.fontColor ??
                                      '#0F131A'),
                                  fontWeight: Platform.isIOS
                                      ? FontWeight.w500
                                      : FontWeight.w600,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                if (shotelDetailComponents.subTitle?[0]?.desc != null)
                  Container(
                    margin: EdgeInsets.only(bottom: 2),
                    child: Text(shotelDetailComponents.subTitle![0]!.desc!,
                        style: TextStyle(
                          color: stringToColor(
                              shotelDetailComponents.subTitle![0]!.fontColor ??
                                  '#7c5945'),
                          fontSize:
                              shotelDetailComponents.subTitle![0]!.fontSize ??
                                  11,
                        )),
                  ),
                // RichText(
                //     maxLines: 1,
                //     overflow: TextOverflow.ellipsis,
                //     text: TextSpan(
                //         children: _buildRichWidget(
                //             shotelDetailComponents.subTitle!,
                //             textSize: 11,
                //             textColor: '#7c5945'))),
                if (shotelDetailComponents.subTitle != null)
                  Container(
                      margin: EdgeInsets.only(left: 3),
                      child: Image.network(
                        'https://gw.alicdn.com/imgextra/i3/O1CN01Ly4yHc1YDxWpEXwQ4_!!6000000003026-2-tps-14-24.png',
                        height: 6,
                        width: 3.5,
                      )),
              ])),
          if (shotelDetailComponents.content?[0]?.desc != null)
            Container(
              margin: EdgeInsets.only(top: 6),
              child: Text(
                shotelDetailComponents.content![0]!.desc!,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    color: stringToColor(
                        shotelDetailComponents.content![0]?.fontColor ??
                            '#0F131A'),
                    fontSize: 12),
              ),
            ),
        ]);
  }

  Widget _buildCardWidget1(ShotelDetailComponents shotelDetailComponents) {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          SizedBox(
              child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.only(right: 6),
                      child: Row(
                        children: <Widget>[
                          if (shotelDetailComponents
                                  .mainTitle?[0]?.score?.isNotEmpty ??
                              false)
                            Container(
                              margin: EdgeInsets.only(
                                  right: 3, top: Platform.isIOS ? 1 : 0),
                              child: Text(
                                shotelDetailComponents.mainTitle![0]!.score!,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: stringToColor(shotelDetailComponents
                                          .mainTitle![0]?.fontColor ??
                                      '#0F131A'),
                                  fontWeight: Platform.isIOS
                                      ? FontWeight.w500
                                      : FontWeight.w600,
                                  fontFamily: 'fliggy_sans102_md',
                                  package: 'ffonts',
                                ),
                              ),
                            ),
                          if (shotelDetailComponents.mainTitle?[0]?.desc !=
                              null)
                            Expanded(
                              child: Text(
                                shotelDetailComponents.mainTitle![0]!.desc!,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 13,
                                  color: stringToColor(shotelDetailComponents
                                          .mainTitle![0]?.fontColor ??
                                      '#0F131A'),
                                  fontWeight: Platform.isIOS
                                      ? FontWeight.w500
                                      : FontWeight.w600,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                if (shotelDetailComponents.subTitle?[0]?.desc != null)
                  Container(
                    margin: EdgeInsets.only(bottom: 2),
                    child: Text(shotelDetailComponents.subTitle![0]!.desc!,
                        style: TextStyle(
                          color: stringToColor(
                              shotelDetailComponents.subTitle![0]!.fontColor ??
                                  '#7c5945'),
                          fontSize:
                              shotelDetailComponents.subTitle![0]!.fontSize ??
                                  11,
                        )),
                  ),
                // RichText(
                //     maxLines: 1,
                //     overflow: TextOverflow.ellipsis,
                //     text: TextSpan(
                //         children: _buildRichWidget(
                //             shotelDetailComponents.subTitle!,
                //             textSize: 11,
                //             textColor: '#7c5945'))),
                if (shotelDetailComponents.subTitle != null)
                  Container(
                      margin: EdgeInsets.only(left: 4.5),
                      child: Image.network(
                        'https://gw.alicdn.com/imgextra/i3/O1CN01Ly4yHc1YDxWpEXwQ4_!!6000000003026-2-tps-14-24.png',
                        height: 6,
                        width: 3.5,
                      )),
              ])),
          if (shotelDetailComponents.content?[0]?.desc != null)
            Container(
              margin: EdgeInsets.only(top: 6),
              child: Text(
                shotelDetailComponents.content![0]!.desc!,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    color: stringToColor(
                        shotelDetailComponents.content![0]?.fontColor ??
                            '#0F131A'),
                    fontSize: 12),
              ),
            ),
        ]);
  }

  List<InlineSpan> _buildRichWidget(
    List<TextVO> list, {
    bool needDiv = false,
    double textSize = 12,
    String? textColor,
  }) {
    final List<InlineSpan> widgetList = <InlineSpan>[];
    for (int i = 0; i < list.length; i++) {
      final TextVO model = list[i];
      if (model.icon != null) {
        widgetList.add(_buildIcon(model));
      }
      // if (model.score?.isNotEmpty ?? false) {
      //   widgetList.add(_buildText(model,
      //       textColor: textColor,
      //       textSize: 14,
      //       text: '${model.score} ',
      //       fontFamily: 'fliggy_sans102_md',));
      // }
      if (model.desc != null) {
        if (model.desc == '|') {
          widgetList.add(WidgetSpan(
              child: Container(
            height: 7,
            width: 0.5,
            color: Color(0xffD2D4D9),
            margin: EdgeInsets.only(left: 5.5, right: 5.5, bottom: 5),
          )));
        } else if (model.backgroundColor != null) {
          widgetList
              .add(_buildTag(model, textColor: textColor, textSize: textSize));
        } else if (model.isColon ?? false) {
          widgetList.add(
              _buildColonText(model, textColor: textColor, textSize: textSize));
        } else {
          widgetList.add(_buildText(
            model,
            textColor: textColor,
            textSize: textSize,
          ));
        }
      }

      if (needDiv) {
        widgetList.add(WidgetSpan(
            child: Container(
          height: 7,
          width: 0.5,
          color: Color(0xffD2D4D9),
          margin: EdgeInsets.only(left: 4, right: 4, bottom: 5),
        )));
      }
    }
    if (needDiv && widgetList.isNotEmpty) {
      widgetList.removeAt(widgetList.length - 1);
    }
    return widgetList;
  }

  WidgetSpan _buildColonText(
    TextVO itemRichTextModel, {
    double textSize = 12,
    String? textColor,
  }) {
    return WidgetSpan(
        child: Container(
      margin: EdgeInsets.only(left: 2, right: 2, top: 1),
      child: Text(itemRichTextModel.desc!,
          style: TextStyle(
            color: stringToColor(
                itemRichTextModel.fontColor ?? textColor ?? '#5C5F66'),
            fontSize: itemRichTextModel.fontSize ?? textSize,
          )),
    ));
  }

  TextSpan _buildText(
    TextVO itemRichTextModel, {
    double textSize = 12,
    String? textColor,
    String? text,
  }) {
    // return WidgetSpan(
    // child: Container(
    //   child: Text(text ?? itemRichTextModel.desc!,
    //       style: TextStyle(
    //         color: stringToColor(
    //             itemRichTextModel.fontColor ?? textColor ?? '#5C5F66'),
    //         fontSize: itemRichTextModel.fontSize ?? textSize,
    //         fontWeight: itemRichTextModel.fontWeight
    //       )),
    // ));
    return TextSpan(
        text: text ?? itemRichTextModel.desc,
        style: TextStyle(
            color: stringToColor(
                itemRichTextModel.fontColor ?? textColor ?? '#5C5F66'),
            fontSize: itemRichTextModel.fontSize ?? textSize,
            fontWeight: itemRichTextModel.fontWeight));
  }

  WidgetSpan _buildTag(TextVO itemRichTextModel,
      {double textSize = 12, String? textColor}) {
    return WidgetSpan(
        child: Container(
      padding: itemRichTextModel.backgroundColor != null
          ? EdgeInsets.symmetric(horizontal: 3.0, vertical: 1.5)
          : EdgeInsets.zero,
      margin: itemRichTextModel.backgroundColor != null
          ? EdgeInsets.only(left: 6)
          : EdgeInsets.zero,
      decoration: itemRichTextModel.backgroundColor != null
          ? BoxDecoration(
              color: stringToColor(itemRichTextModel.backgroundColor),
              borderRadius: BorderRadius.circular(2.0),
            )
          : null,
      child: Text(itemRichTextModel.desc!,
          style: TextStyle(
            color: stringToColor(
                itemRichTextModel.fontColor ?? textColor ?? '#5C5F66'),
            fontSize: itemRichTextModel.fontSize ?? textSize,
          )),
    ));
  }

  WidgetSpan _buildIcon(TextVO itemRichTextModel, {double textSize = 12}) {
    return WidgetSpan(
      child: Container(
        margin: const EdgeInsets.only(right: 2, bottom: 1.5),
        child: Image.network(
          itemRichTextModel.icon!, // 图片文件的路径
          height: itemRichTextModel.fontSize ?? textSize,
          // width: itemRichTextModel.fontSize ?? textSize,
        ),
      ),
    );
  }

  void jumpToPop(BuildContext context, String? url) {
    if (url != null) {
      FliggyNavigatorApi.getInstance().push(context, url, anim: Anim.slide);
    }
  }
}

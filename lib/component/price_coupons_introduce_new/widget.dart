import '../../custom_widget/block_title.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fround_image/fround_image.dart';
import '../../custom_widget/detail_arrow.dart';
import '../../fliggy_item_detail.dart';
import '../../utils/common_config.dart';
import '../price_coupons_introduce/model.dart';
import 'notifier.dart';

class PriceCouponsIntroduceNewWidget extends StatelessWidget {
  const PriceCouponsIntroduceNewWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final PriceCouponsIntroduceNewChangeNotifier changeNotifier =
        Provider.of<PriceCouponsIntroduceNewChangeNotifier>(context);
    if (changeNotifier.itemDetailModel.priceCouponsIntroduceModel == null ||
        !(changeNotifier.itemDetailModel.itemModel?.couponItem ?? false)) {
      return SizedBox.shrink();
    }
    changeNotifier.ctrlExposure(context, 'yjq_new_coupon.default', null);
    return Container(
      decoration: BoxDecoration(
          color: Color(0xFFFFFFFF), borderRadius: BorderRadius.circular(6)),
      padding: EdgeInsets.fromLTRB(paddingLeft, 0, paddingRight, 12),
      margin: const EdgeInsets.only(bottom: itemDivider),
      child: Column(
        children: <Widget>[
          BlockTitle(changeNotifier
                  .itemDetailModel.priceCouponsIntroduceModel!.title ??
              ''),
          if (changeNotifier
                  .itemDetailModel.priceCouponsIntroduceModel!.descList !=
              null)
            ValueListenableBuilder(
                valueListenable: changeNotifier
                    .itemDetailModel.priceCouponsIntroduceModel!.isExpand,
                builder: (BuildContext context, bool isExpand, Widget? child) {
                  List<PriceCouponsDescModel> descList;
                  if (isExpand) {
                    descList = changeNotifier
                        .itemDetailModel.priceCouponsIntroduceModel!.descList!;
                  } else {
                    descList = changeNotifier.itemDetailModel
                        .priceCouponsIntroduceModel!.subDescList!;
                  }
                  return Column(
                    children: descList
                        .asMap()
                        .entries
                        .map((MapEntry<int, PriceCouponsDescModel> desc) =>
                            _buildTableRow(context,changeNotifier,desc.value, desc.key))
                        .toList(),
                  );
                }),
          if (changeNotifier
                  .itemDetailModel.priceCouponsIntroduceModel!.canExpand ??
              false)
            ValueListenableBuilder(
                valueListenable: changeNotifier
                    .itemDetailModel.priceCouponsIntroduceModel!.isExpand,
                builder: (BuildContext context, bool isExpand, Widget? child) {
                  return GestureDetector(
                    onTap: () {
                      changeNotifier.itemDetailModel.priceCouponsIntroduceModel!
                              .isExpand.value =
                          !changeNotifier.itemDetailModel
                              .priceCouponsIntroduceModel!.isExpand.value;
                    },
                    child: Container(
                      height: 18,
                      color: Color(0xFFFFFFFF),
                      margin: EdgeInsets.only(top: 10),
                      alignment: Alignment.center,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Container(
                            margin: EdgeInsets.only(right: 5),
                            child: Text(
                              changeNotifier
                                      .itemDetailModel
                                      .priceCouponsIntroduceModel!
                                      .isExpand
                                      .value
                                  ? '收起更多'
                                  : '展开更多',
                              style: const TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 13,
                                  color: Color(0xFF0F131A)),
                            ),
                          ),
                          if (!changeNotifier.itemDetailModel
                              .priceCouponsIntroduceModel!.isExpand.value)
                            topArrowBig,
                          if (changeNotifier.itemDetailModel
                              .priceCouponsIntroduceModel!.isExpand.value)
                            bottomArrowBig,
                        ],
                      ),
                    ),
                  ); // hotelPackageManager: hotelPackageManager,
                }),
        ],
      ),
    );
  }

  Widget _buildTableRow(BuildContext context,PriceCouponsIntroduceNewChangeNotifier changeNotifier,PriceCouponsDescModel subValue, int index) {
    changeNotifier.ctrlExposure(context, 'yjq_new_coupon.$index', null);
    return Container(
      height: 84,
      width: 333,
      margin: EdgeInsets.only(top: index > 0 ? 9 : 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          if (subValue.value != null)
            Container(
              width: 86,
              height: 84,
              decoration: const BoxDecoration(
                color: Color(0xFFFFF2F0),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(6.0),
                  bottomLeft: Radius.circular(6.0),
                ),
              ),
              padding: EdgeInsets.symmetric(horizontal: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  const Padding(
                    padding: EdgeInsets.only(top: 3, right: 2),
                    child: Text(
                      '¥',
                      textAlign: TextAlign.end,
                      style: TextStyle(
                          color: Color(0XFFFF5533),
                          fontSize: 12.00,
                          fontFamily: 'fliggy_sans102_bd',
                          package: 'ffonts',
                          height: 1),
                    ),
                  ),
                  Text(subValue.value!,
                      textAlign: TextAlign.end,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                          color: Color(0XFFFF5533),
                          fontSize: 30.00,
                          fontFamily: 'fliggy_sans102_bd',
                          package: 'ffonts',
                          height: 1)),
                ],
              ),
            ),
          if (subValue.title != null && subValue.value == null)
            Container(
              width: 86,
              decoration: const BoxDecoration(
                color: Color(0xFFFFF2F0),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(6.0),
                  bottomLeft: Radius.circular(6.0),
                ),
              ),
              child: Center(
                child: Text(
                  subValue.title ?? '',
                  style: TextStyle(fontSize: 18,color: Color(0XFFFF5533),fontWeight: FontWeight.w500),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          FRoundImage.asset(
            'assets/price_coupon_line.png',
            package,
            width: 7,
            height: 84,
            fit: BoxFit.cover,
          ),
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                color: Color(0xFFFFF2F0),
                borderRadius: BorderRadius.only(
                  topRight: Radius.circular(6.0),
                  bottomRight: Radius.circular(6.0),
                ),
              ),
              padding: EdgeInsets.only(left: 9, right: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  if (subValue.text != null)
                    Text(
                      subValue.text!,
                      style: TextStyle(
                          fontSize: 13, color: Color(0xFF92949A), height: 1.3),
                    ),
                  if (subValue.textList != null)
                    ...subValue.textList!
                        .asMap()
                        .entries
                        .map((MapEntry<int, PriceCouponsTextList> data) =>
                            Container(
                              margin:
                                  EdgeInsets.only(top: data.key > 0 ? 4 : 0),
                              child: Text(
                                data.value.text ?? '',
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize:
                                      (data.value.highlight ?? false) ? 15 : 10,
                                  color: (data.value.highlight ?? false)
                                      ? Color(0xFF0F131A)
                                      : Color(0xFF5C5F66),
                                  fontWeight: (data.value.highlight ?? false)
                                      ? FontWeight.w500
                                      : FontWeight.w400,
                                ),
                              ),
                            ))
                        .toList(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class PhoneCardShelfWidget extends StatelessWidget {
  const PhoneCardShelfWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final PhoneCardShelfChangeNotifier changeNotifier =
        Provider.of<PhoneCardShelfChangeNotifier>(context);
    return Container();
  }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class RouteGraphicWidget extends StatelessWidget {
  const RouteGraphicWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RouteGraphicChangeNotifier changeNotifier =
        Provider.of<RouteGraphicChangeNotifier>(context);
    return Container();
  }
}
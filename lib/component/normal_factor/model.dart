import '../../utils/safe_access.dart';

class NormalFactorModel {
  bool get visible => productInfoList != null && productInfoList!.isNotEmpty;
  List<ProductInfoList>? productInfoList;

  String get mode => productInfoList!.length > 4
      ? 'oversize'
      : productInfoList!.length > 2
          ? 'split'
          : 'row';

  NormalFactorModel.fromJson(Map<String, dynamic> dataModel) {
    final Map<String, dynamic> productInfo =
        SafeAccess.safeParseMap(dataModel['productInfo']);
    final Map<String, dynamic> json =
        SafeAccess.safeParseMap(productInfo['data']);
    if (json['productInfoList'] != null) {
      productInfoList = <ProductInfoList>[];
      SafeAccess.safeParseList(json['productInfoList']).forEach((dynamic v) {
        productInfoList!
            .add(ProductInfoList.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
  }
}

class ProductInfoList {
  String? icon;
  String? mainInfo;
  String? subInfo;

  ProductInfoList.fromJson(Map<String, dynamic> json) {
    icon = SafeAccess.safeParseString(json['icon']);
    mainInfo = SafeAccess.safeParseString(json['mainInfo']);
    subInfo = SafeAccess.safeParseString(json['subInfo']);
  }
}

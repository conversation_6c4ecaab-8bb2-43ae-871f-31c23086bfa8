import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fround_image/fround_image.dart';
import '../../custom_widget/null_widget.dart';
import '../../utils/common_config.dart';
import 'model.dart';
import 'notifier.dart';

class NormalFactorWidget extends StatelessWidget {
  const NormalFactorWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final NormalFactorChangeNotifier changeNotifier =
        Provider.of<NormalFactorChangeNotifier>(context);
    final NormalFactorModel? normalFactorModel =
        changeNotifier.normalFactorModel;
    return normalFactorModel == null || !normalFactorModel.visible
        ? nullWidget
        : Container(
            padding: const EdgeInsets.fromLTRB(paddingLeft, 8, paddingRight, 0),
            color: Color(0xffffffff),
            child: _buildPage(normalFactorModel),
          );
  }

  Widget _buildPage(NormalFactorModel normalFactorModel) {
    if (normalFactorModel.mode == 'oversize') {
      return SizedBox(
        height: 68,
        child: ListView.builder(
          padding: EdgeInsets.zero,
          scrollDirection: Axis.horizontal,
          itemCount: normalFactorModel.productInfoList?.length ?? 0,
          itemBuilder: (BuildContext context, int index) {
            final ProductInfoList? productInfoList =
                normalFactorModel.productInfoList![index];
            return _buildOversize(productInfoList);
          },
        ),
      );
    } else if (normalFactorModel.mode == 'split') {
      return SizedBox(
        height: 68,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            if (normalFactorModel.productInfoList!.isNotEmpty)
              _buildSplit(normalFactorModel.productInfoList?[0]),
            if (normalFactorModel.productInfoList!.length > 1)
              _buildSplit(normalFactorModel.productInfoList?[1]),
            if (normalFactorModel.productInfoList!.length > 2)
              _buildSplit(normalFactorModel.productInfoList?[2]),
            if (normalFactorModel.productInfoList!.length > 3)
              _buildSplit(normalFactorModel.productInfoList?[3]),
          ],
        ),
      );
    } else if (normalFactorModel.mode == 'row') {
      return SizedBox(
        height: 20,
        child: ListView.separated(
          padding: EdgeInsets.zero,
          scrollDirection: Axis.horizontal,
          separatorBuilder: (BuildContext context, int index) => const SizedBox(
            width: 12,
          ),
          itemCount: normalFactorModel.productInfoList?.length ?? 0,
          itemBuilder: (BuildContext context, int index) {
            final ProductInfoList? productInfoList =
                normalFactorModel.productInfoList![index];
            return _buildRow(productInfoList);
          },
        ),
      );
    } else {
      return nullWidget;
    }
  }

  Widget _buildOversize(ProductInfoList? productInfoList) {
    return productInfoList == null
        ? nullWidget
        : SizedBox(
            width: 80,
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  if (productInfoList.icon != '')
                    FRoundImage.network(productInfoList.icon!,
                        width: 15.00, height: 15.00),
                  Container(
                      child: Text(productInfoList.mainInfo!,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.left,
                          style: const TextStyle(
                            color: Color(0xFF292c33),
                            fontSize: 13.00,
                          )),
                      margin: const EdgeInsets.only(top: 5)),
                  Container(
                      child: Text(productInfoList.subInfo!,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.left,
                          style: const TextStyle(
                            color: Color(0xFFabaeb3),
                            fontSize: 12.00,
                          )),
                      margin: const EdgeInsets.only(top: 5)),
                ]));
  }

  Widget _buildSplit(ProductInfoList? productInfoList) {
    return productInfoList == null
        ? nullWidget
        : Expanded(
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  if (productInfoList.icon != '')
                    FRoundImage.network(productInfoList.icon!,
                        width: 15.00, height: 15.00),
                  Container(
                      child: Text(productInfoList.mainInfo!,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.left,
                          style: const TextStyle(
                            color: Color(0xFF292c33),
                            fontSize: 13.00,
                          )),
                      margin: const EdgeInsets.only(top: 5)),
                  Container(
                      child: Text(productInfoList.subInfo!,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.left,
                          style: const TextStyle(
                            color: Color(0xFFabaeb3),
                            fontSize: 12.00,
                          )),
                      margin: const EdgeInsets.only(top: 5)),
                ]),
          );
  }

  Widget _buildRow(ProductInfoList? productInfoList) {
    return productInfoList == null
        ? nullWidget
        : Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          if (productInfoList.icon != '')
            FRoundImage.network(productInfoList.icon!,
                width: 15.00, height: 15.00),
          Container(
              child: Text(productInfoList.mainInfo!,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                    color: Color(0xFF292c33),
                    fontSize: 13.00,
                  )),
              margin: const EdgeInsets.only(left: 4)),
          Container(
              child: Text(productInfoList.subInfo!,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                    color: Color(0xFFabaeb3),
                    fontSize: 12.00,
                  )),
              margin: const EdgeInsets.only(left: 6)),
        ]);
  }
}

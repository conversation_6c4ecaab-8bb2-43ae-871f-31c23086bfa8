import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description  包车权益
class PlatformInsuranceInfoComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<PlatformInsuranceInfoChangeNotifier>.value(
      value: PlatformInsuranceInfoChangeNotifier(context),
      child: const PlatformInsuranceInfoWidget(),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fround_image/fround_image.dart';
import '../../custom_widget/null_widget.dart';
import '../../utils/common_config.dart';
import 'notifier.dart';

class PlatformInsuranceInfoWidget extends StatelessWidget {
  const PlatformInsuranceInfoWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final PlatformInsuranceInfoChangeNotifier changeNotifier =
        Provider.of<PlatformInsuranceInfoChangeNotifier>(context);
    changeNotifier.fromJson();
    return changeNotifier.picPath == null
        ? nullWidget
        : Container(
            margin: const EdgeInsets.only(bottom: itemDivider),
            child: FRoundImage.network(
              changeNotifier.picPath!,
              width: double.infinity,
            ));
  }
}

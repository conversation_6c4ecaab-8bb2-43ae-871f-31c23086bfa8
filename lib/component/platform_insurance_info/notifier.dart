import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import '../../utils/safe_access.dart';

class PlatformInsuranceInfoChangeNotifier extends ComponentChangeNotifier {
  String? picPath;

  PlatformInsuranceInfoChangeNotifier(ComponentContext context)
      : super(context);

  @override
  void fromJson() {
    picPath ??= dataModel['platformInsuranceInfo'] != null
        ? SafeAccess.safeParseMap(SafeAccess.safeParseMap(
            dataModel['platformInsuranceInfo'])['data'])['picPath']
        : null;
  }
}

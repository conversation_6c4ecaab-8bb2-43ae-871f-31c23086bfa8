
import 'package:fjson_exports/fjson_exports.dart';
import 'package:flutter/material.dart';

import 'Hotel_package_manager/Hotel_package_model.dart';


/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

/// 机票货架管理，涉及od的筛选和联动，原始数据的存放，以及筛选后的数据的存放
class HotelStyleNewModel {

  /// 原始数据
  List<HotelPackageModel>? packageList;

  /// 筛选后的数据
  List<HotelPackageModel>? filterPackageList;

  String? title;

  /// 是否可以展开
  bool canExpand = false;

  int expandCount = 4;

  /// 是否展开
  ValueNotifier<bool> isExpand = ValueNotifier<bool>(false);


  HotelStyleNewModel.fromJson(Map<dynamic, dynamic> json,{bool showXelements = true}) {

    // 初始化货架数据
    if (json.containsKey('packageShelf')) {
      final Map<String,dynamic> packageShelf = safeNonNullMap(json['packageShelf'], (dynamic e) => e);
      final Map<String,dynamic> packageShelfData = safeNonNullMap(packageShelf['data'], (dynamic e) => e);
      final List<dynamic> packageInfos = safeNonNullList(packageShelfData['packageInfos'], (dynamic e) => e);
      packageList = <HotelPackageModel>[];
      for (final dynamic package in packageInfos) {
       final HotelPackageModel hotelPackageModel = HotelPackageModel.fromJson(package,promotionPriceTitle:packageShelfData['promotionPriceTitle']);
       hotelPackageModel.showXelements = showXelements;
        packageList!.add(hotelPackageModel);
      }

      expandCount = safeInt(packageShelfData['expandCount']) ?? 4;

      if (packageList!.length > expandCount) {
        canExpand = true;
        filterPackageList = packageList!.sublist(0, expandCount);
      } else {
        canExpand = false;
        filterPackageList = packageList;
      }

      title = packageShelfData['title'] ?? '';

    }
  }
}


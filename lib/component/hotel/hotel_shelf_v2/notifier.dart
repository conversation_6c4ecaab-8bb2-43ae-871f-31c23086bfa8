

import '../../../custom_widget/dialog_webview.dart';
import '../../../render/component/component_context.dart';
import 'package:flutter/cupertino.dart';

import '../../../item_sku/add_to_buy/buy_url_manager.dart';
import '../../../render/component/component_change_notifier.dart';
import '../../../sku/sku_data_manager.dart';

/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

class HotelShelfV2ChangeNotifier extends ComponentChangeNotifier {
  HotelShelfV2ChangeNotifier(ComponentContext componentContext) : super(componentContext);
 void gotoBuy(BuildContext context,String? skuId){
   BuyUrlManager.gotoBuyPage(context, itemDetailModel.buyBannerDataModel?.buyJumpInfo?.newJumpH5Url, itemId: itemDetailEngine.itemId!,categoryId:itemDetailModel.itemModel?.categoryId,newtonParams:itemDetailEngine.pageUrlMap['newtonParams'],skuId: skuId,quantity: 1);
 }
  void gotoBuySku(BuildContext context,String? skuId,String? propPath){
    itemDetailEngine.skuManager.buyNow(skuType: SkuDataManager.skuBuyKey,enterTypeFrom: SkuDataManager.buyBannerBuy,skuId: skuId,propPath: propPath);
  }
  void addCar(BuildContext context,String? skuId,String? propPath){
    itemDetailEngine.skuManager.buyNow(skuType: SkuDataManager.skuCartKey,enterTypeFrom: SkuDataManager.buyBannerBuy,skuId: skuId,propPath: propPath);
  }
  void showPackageExplain(BuildContext context,String? url){
    if (url != null && url.isNotEmpty) {
      final DialogWebView dialogWebView =
      DialogWebView(context: context, url: '$url&hideClose=true',itemDetailEngine: itemDetailEngine,popConfig: H5PopConfig(popHeight: MediaQuery.of(context).size.height * 3 / 4,popTitle: '套餐说明'));
      dialogWebView.showPop();
    }
  }

  void shoDialogHeightWebPop(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      final DialogWebView dialogWebView =
      DialogWebView(context: context, url: url,itemDetailEngine: itemDetailEngine,popConfig: H5PopConfig(popHeight: MediaQuery.of(context).size.height * 3 / 4));
      dialogWebView.showPop();
    }
  }
}
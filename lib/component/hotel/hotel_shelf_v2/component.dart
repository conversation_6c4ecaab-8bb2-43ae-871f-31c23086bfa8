import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../render/component/component.dart';
import '../../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description


///@description titleBar
class HotelShelfV2Component extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<HotelShelfV2ChangeNotifier>.value(
      value: HotelShelfV2ChangeNotifier(context),
      child: HotelShelfV2(),
    );
  }
}
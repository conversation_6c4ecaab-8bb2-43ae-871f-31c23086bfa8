import 'dart:io';

import '../../../custom_widget/detail_arrow.dart';
import '../../../utils/TextConfig.dart';
import '../../../utils/common_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../custom_widget/block_title.dart';
import '../../../utils/common_config.dart';
import '../../hotel_package_to_calendar/widget.dart';
import '../hotel_package_manager/hotel_package_manager.dart';
import '../hotel_package_manager/hotel_package_model.dart';
import '../hotel_style_new_model.dart';
import '../hotel_style_new_widget.dart';
import 'notifier.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:ficonfont/ficonfont.dart';

/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

class HotelShelfV2 extends StatefulWidget {
  const HotelShelfV2({Key? key}) : super(key: key);

  @override
  State<HotelShelfV2> createState() => _HotelShelfV2State();
}

class _HotelShelfV2State extends State<HotelShelfV2> {
  @override
  Widget build(BuildContext context) {
    final HotelShelfV2ChangeNotifier changeNotifier =
        Provider.of<HotelShelfV2ChangeNotifier>(context);
    final HotelPackageManager? hotelPackageManager =
        changeNotifier.itemDetailModel.hotelPackageManager;
    final HotelStyleNewModel? hotelStyleNewModel =
        changeNotifier.itemDetailModel.hotelStyleNewModel;

    if (hotelStyleNewModel != null) {
      return HotelStyleNewWidget(changeNotifier);
    }
    if (hotelPackageManager != null) {
      changeNotifier.ctrlExposure(context, 'dnw-packages-choose.default', null);
      return Container(
        margin: const EdgeInsets.only(bottom: paddingBottom),
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Column(
          children: <Widget>[
            //套餐日历交互
            HotelPackageToCalendarWidget(
                changeNotifier.itemDetailModel.hotelPackageToCalendarModel,changeNotifier),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Column(
                children: <Widget>[
                  // 标题
                  if (hotelPackageManager.title != null &&
                      hotelPackageManager.title!.isNotEmpty)
                    BlockTitle(hotelPackageManager.title ?? ''),
                  if (hotelPackageManager.canExpand)
                    ValueListenableBuilder(
                        valueListenable: hotelPackageManager.isExpand,
                        builder: (BuildContext context, bool isExpand,
                            Widget? child) {
                          List<HotelPackageModel> packageInfos;
                          if (isExpand) {
                            packageInfos = hotelPackageManager!.packageList!;
                          } else {
                            packageInfos =
                                hotelPackageManager!.filterPackageList!;
                          }

                          return buildPackageList(packageInfos,
                              changeNotifier); // hotelPackageManager: hotelPackageManager,
                        })
                  else
                    buildPackageList(
                        hotelPackageManager!.packageList!, changeNotifier),
                  if (hotelPackageManager.canExpand)
                    ValueListenableBuilder(
                        valueListenable: hotelPackageManager.isExpand,
                        builder: (BuildContext context, bool isExpand,
                            Widget? child) {
                          return GestureDetector(
                            onTap: () {
                              hotelPackageManager.isExpand.value =
                                  !hotelPackageManager.isExpand.value;
                            },
                            child: Container(
                              height: 55,
                              color: Color(0xFFFFFFFF),
                              alignment: Alignment.center,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: <Widget>[
                                  Container(
                                    margin: EdgeInsets.only(right: 5),
                                    child: Text(
                                      hotelPackageManager.isExpand.value
                                          ? '收起'
                                          : '查看更多',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.w700,
                                          fontSize: 13,
                                          color: Color(0xFF333333)),
                                    ),
                                  ),
                                  if (!hotelPackageManager.isExpand.value)
                                    topArrowBig,
                                  if (hotelPackageManager.isExpand.value)
                                    bottomArrowBig,
                                ],
                              ),
                            ),
                          ); // hotelPackageManager: hotelPackageManager,
                        }),
                ],
              ),
            ),
          ],
        ),
      );
    }
    return SizedBox.shrink();

    //   Container(
    //   child: Column (
    //     children: <Widget>[
    //       HotelPackageWidget(hotelPackageManager: hotelPackageManager, packageInfos: packageInfos,)
    //     ],
    //   )
    //   ,
    // );
  }

  Widget buildPackageList(List<HotelPackageModel> packageInfos,
      HotelShelfV2ChangeNotifier changeNotifier) {
    final List<Widget> listWidget = <Widget>[];
    packageInfos.asMap().forEach((int key, HotelPackageModel item) {
      listWidget.add(linearLayoutWidget(context, item, changeNotifier,key));
    });
    return Column(
      children: listWidget,
    );
  }

  Widget linearLayoutWidget(
      BuildContext context,
      HotelPackageModel packageInfos,
      HotelShelfV2ChangeNotifier changeNotifier,int index) {
    changeNotifier.ctrlExposure(context, 'dnw-packages-choose.title_$index', <String,dynamic>{'index':index,'sku_id':packageInfos.skuId,'pv_id':packageInfos.skuPvId});
    return Container(
      margin: EdgeInsets.only(bottom: 10.0),
      padding: EdgeInsets.only(top: 15, bottom: 15, left: 12, right: 12),
      decoration: BoxDecoration(
        color: Color(0xFFFDFDFD), // 背景颜色
        borderRadius: BorderRadius.circular(2), // 设置圆角
      ),
      width: 333,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          // 标题
          if (packageInfos.packageName != null)
            GestureDetector(
              onTap: () {
                changeNotifier.ctrlClicked(context, 'dnw-packages-choose.title_$index','dnw-packages-choose', <String,dynamic>{'index':index,'sku_id':packageInfos.skuId,'pv_id':packageInfos.skuPvId});
                changeNotifier.showPackageExplain(
                    context, packageInfos.packageExplainJumpUrl);
              },
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      packageInfos.packageName ?? '',
                      style: TextStyle(
                        fontSize: 15.0,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF0F131A),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (packageInfos.packageExplainJumpUrl != null)
                    Container(
                        margin: EdgeInsets.only(left: 5),
                        child: rightArrowSmall),
                ],
              ),
            ),

          if (packageInfos.hotelTags != null)
            Container(
              margin: EdgeInsets.only(top: 10),
              child: Row(
                children: packageInfos.hotelTags!.map((HotelTags tag) {
                  return Text(
                    ((tag.isFirst ?? false) ? '' : ' | ') + (tag.value ?? ''),
                    style: TextStyle(color: Color(0xFF333333), fontSize: 12),
                  );
                }).toList(),
              ),
            ),

          if (packageInfos.xelements != null)
            Column(
              children: packageInfos.xelements!.map((Xelements element) {
                return Container(
                  padding: EdgeInsets.only(top: 9.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // 第一部分：图标、标题、箭头和标签
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: <Widget>[
                          // 图标部分
                          if (element.icon != null)
                            Container(
                              width: 15.0,
                              height: 15.0,
                              margin: EdgeInsets.only(right: 3.0),
                              child: Stack(
                                children: <Widget>[
                                  Container(
                                    width: 15.0,
                                    height: 15.0,
                                    decoration: BoxDecoration(
                                      color: Color(0xffffffff),
                                      borderRadius: BorderRadius.circular(7.5),
                                      border: Border.all(
                                        color: Color(0xFFFF8C1A),
                                        width: 0.5,
                                      ),
                                    ),
                                    child: Center(
                                      child: Text(
                                        element.icon!,
                                        style: TextStyle(
                                          color: Color(0xFFFF8C1A),
                                          fontSize: 11,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  // Positioned(
                                  //   right: 0,
                                  //   top: 0,
                                  //   child: Visibility(
                                  //     visible: !(element.hideIconGapLine ||
                                  //         element.icon != null),
                                  //     child: Container(
                                  //       width: 1.0,
                                  //       height: 15.0,
                                  //       color: Color(0xFF000000), // 根据需求调整颜色
                                  //     ),
                                  //   ),
                                  // ),
                                ],
                              ),
                            ),
                          // 标题
                          if (element.title != null)
                            Expanded(
                              child: Text(
                                element.title!,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 12.0,
                                  color: Color(0xFF0F131A),
                                ),
                              ),
                            ),
                          // 箭头
                          // Visibility(
                          //   visible: element.showArrow,
                          //   child: Container(
                          //     height: element.titleFontSize != null &&
                          //             element.titleFontSize! == 10.0
                          //         ? 9.0
                          //         : 11.0,
                          //     margin: EdgeInsets.only(left: 3.0),
                          //     child: Image.network(
                          //       'https://gw.alicdn.com/tfs/TB1ivuyINnaK1RjSZFBXXcW7VXa-26-48.png',
                          //       fit: BoxFit.fitCenter,
                          //     ),
                          //   ),
                          // ),
                          // 标签列表
                          // if (element.descList != null &&
                          //     element.descList!.isNotEmpty)
                          //   Row(
                          //     children: element.descList!.map((desc) {
                          //       return Container(
                          //         margin: EdgeInsets.only(left: 5.0, right: 15.0),
                          //         height: 16.0,
                          //         child: Text(
                          //           desc.value,
                          //           maxLines: 1,
                          //           overflow: TextOverflow.ellipsis,
                          //           style: TextStyle(
                          //             fontSize: 11.0,
                          //             color: Color(0xFF666666),
                          //           ),
                          //         ),
                          //       );
                          //     }).toList(),
                          //   ),
                        ],
                      ),

                      // 第二部分：线条和子标题
                      if (element.subTitle != null)
                        Container(
                          padding: EdgeInsets.only(top: 8.0),
                          child: Text(
                            element.subTitle ?? '',
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: 10.0,
                              color: Color(0xFF919499),
                            ),
                          ),
                        )
                    ],
                  ),
                );
              }).toList(),
            ),

          Padding(
            padding: EdgeInsets.only(top: 9.0),
            child: Row(
              children: <Widget>[
                // 左边价格
                if (packageInfos.finalPriceText != null)
                  priceWidget(packageInfos),
                // Text(
                //   '¥${packageInfos.finalPriceText}',
                //   style: TextStyle(
                //       fontSize: 18.0,
                //       color: Color(0xFFFF401A),
                //       fontWeight: FontWeight.bold,
                //       height: 1),
                // ),

                // 价格说明
                if (packageInfos.lookInventoryDesc != null)
                  GestureDetector(
                    onTap: () {
                      changeNotifier.ctrlClicked(context, 'dnw-packages-choose.action_$index','dnw-packages-choose', <String,dynamic>{'index':index,'sku_id':packageInfos.skuId,'pv_id':packageInfos.skuPvId});
                      FliggyNavigatorApi.getInstance().push(context,
                          packageInfos.lookInventoryLink?.jumpH5Url ?? '');
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 3),
                      child: Text(
                        packageInfos.lookInventoryDesc ?? '',
                        style: TextStyle(
                            fontSize: 11.0,
                            color: Color(0xFF00A2FF),
                            height: 1),
                      ),
                    ),
                  ),

                // 实在也没有蓝色箭头了，展示不出来就再换吧
                Ficon(
                  0xeb15,
                  10,
                  Color(0xFF00A2FF),
                ),
                Spacer(),
                // 右边购买按钮

                if ((packageInfos.cartButtonSupport ?? false) &&
                    !(packageInfos.buyButtonGray ?? false))
                  GestureDetector(
                    onTap: () {
                      changeNotifier.ctrlClicked(context, 'dnw-packages-choose.cart_$index','dnw-packages-choose', <String,dynamic>{'index':index,'sku_id':packageInfos.skuId,'pv_id':packageInfos.skuPvId});
                      changeNotifier.addCar(
                          context, packageInfos.skuId, packageInfos.skuPvId);
                    },
                    child: Container(
                      width: 39,
                      height: 39,
                      padding: EdgeInsets.symmetric(horizontal: 3),
                      margin: EdgeInsets.only(right: 10.0),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6.00),
                          border: Border.all(color: const Color(0xFFFF7300)),
                          color: Color(0xFFFFFFFF)),
                      // https://gw.alicdn.com/imgextra/i4/O1CN01ZWeUJA1xX4NeVDmbi_!!6000000006452-2-tps-96-96.png 大促用这个
                      child: Align(
                        child: SizedBox(
                          width: 16,
                          height: 16,
                          // 绝了，这里给图片设置宽高不生效
                          child: Image.network(
                            'https://gw.alicdn.com/imgextra/i3/O1CN01ucqtme1fBOYLSvpud_!!6000000003968-2-tps-96-96.png',
                            width: 16,
                            height: 16,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                  ),

                if ((packageInfos.showBuyButton ?? false) &&
                    (packageInfos.buyButtonSupport ?? false))
                  GestureDetector(
                    onTap: () {
                      changeNotifier.ctrlClicked(context, 'dnw-packages-choose.buy_$index','dnw-packages-choose', <String,dynamic>{'index':index,'sku_id':packageInfos.skuId,'pv_id':packageInfos.skuPvId});
                      if (packageInfos.directJumpBuy ?? false) {
                        changeNotifier.gotoBuy(context, packageInfos.skuId);
                      } else {
                        changeNotifier.gotoBuySku(
                            context, packageInfos.skuId, packageInfos.skuPvId);
                      }
                    },
                    child: Container(
                      width: 39,
                      height: 39,
                      padding: EdgeInsets.symmetric(horizontal: 3),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6.00),
                        color: (packageInfos.buyButtonGray ?? false)
                            ? Color(0xFFE6E6E6)
                            : Color(0xFFFF7300),
                      ),
                      child: Align(
                        child: SizedBox(
                          width: 28,
                          child: Center(
                            child: Text(
                              packageInfos.buyButtonDesc ?? '',
                              style: TextStyle(
                                fontSize:
                                    (packageInfos.buyButtonDesc?.length ?? 0) >
                                            1
                                        ? 14
                                        : 18.00,
                                fontWeight: FontWeightExt.bold,
                                color: (packageInfos.buyButtonGray ?? false)
                                    ? Color(0xFF999999)
                                    : Color(0xFFFFFFFF),
                              ),
                              maxLines: 2,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
          if (packageInfos.highLightList != null)
            Container(
              child: Row(
                children: packageInfos.highLightList!.map((HighLightItem tag) {
                  return Container(
                    padding: EdgeInsets.all(1),
                    margin: EdgeInsets.only(right: 2),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(3),
                      border: Border.all(
                          color: tag.textColor == null
                              ? Color(0xFFfcfcfc)
                              : stringToColor(tag.textColor),
                          width: 0.5,
                          style: BorderStyle.solid),
                    ),
                    child: Text(
                      tag.title!,
                      style: TextStyle(
                          color: tag.textColor == null
                              ? Color(0xFF5c5f66)
                              : stringToColor(tag.textColor),
                          fontSize: 10),
                    ),
                  );
                }).toList(),
              ),
            ),
        ],
      ),
    );
  }

  /// 价格
  Widget priceWidget(HotelPackageModel packageInfos) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        if (packageInfos.priceTitle != null &&
            packageInfos.priceTitle!.isNotEmpty)
          Container(
              child: Text('${packageInfos.priceTitle!} ', //cell.priceTitle!,
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                      // fontFamily: 'fliggy_sans102_bd',
                      // package: 'ffonts',
                      color: Color(0xFFFF5533),
                      fontSize: 12.00)),
              margin: EdgeInsets.only(bottom: Platform.isAndroid ? 3 : 2.00)),
        Container(
            child: const Text('¥',
                textAlign: TextAlign.left,
                style: TextStyle(
                    fontFamily: 'fliggy_sans102_bd',
                    package: 'ffonts',
                    color: Color(0xFFFF5533),
                    fontSize: 12.00)),
            margin: EdgeInsets.only(bottom: Platform.isAndroid ? 3 : 2.00)),
        if (packageInfos.finalPriceText != null)
          Text(packageInfos.finalPriceText!,
              textAlign: TextAlign.left,
              style: const TextStyle(
                  fontFamily: 'fliggy_sans102_bd',
                  package: 'ffonts',
                  color: Color(0xFFFF5533),
                  fontWeight: FontWeight.w500,
                  fontSize: 21.00,
                  height: 1)),
      ],
    );
  }
}

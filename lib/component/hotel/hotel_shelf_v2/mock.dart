/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description


const String mockData = '''
"availableDepartureCityList": {
      "data": {
        "departureCityList": [
          {
            "name": "阿克苏",
            "value": "AKU"
          },
          {
            "name": "包头",
            "value": "BAV"
          },
          {
            "name": "北京",
            "value": "BJS"
          },
          {
            "name": "长春",
            "value": "CGQ"
          },
          {
            "name": "长沙",
            "value": "CSX"
          },
          {
            "name": "成都",
            "value": "CTU"
          },
          {
            "name": "重庆",
            "value": "CKG"
          },
          {
            "name": "大连",
            "value": "DLC"
          },
          {
            "name": "福州",
            "value": "FOC"
          },
          {
            "name": "广州",
            "value": "CAN"
          },
          {
            "name": "桂林",
            "value": "KWL"
          },
          {
            "name": "贵阳",
            "value": "KWE"
          },
          {
            "name": "哈尔滨",
            "value": "HRB"
          },
          {
            "name": "海口",
            "value": "HAK"
          },
          {
            "name": "杭州",
            "value": "HGH"
          },
          {
            "name": "合肥",
            "value": "HFE"
          },
          {
            "name": "呼和浩特",
            "value": "HET"
          },
          {
            "name": "佳木斯",
            "value": "JMU"
          },
          {
            "name": "揭阳",
            "value": "SWA"
          },
          {
            "name": "济南",
            "value": "TNA"
          },
          {
            "name": "喀什",
            "value": "KHG"
          },
          {
            "name": "库尔勒",
            "value": "KRL"
          },
          {
            "name": "昆明",
            "value": "KMG"
          },
          {
            "name": "兰州",
            "value": "LHW"
          },
          {
            "name": "临沂",
            "value": "LYI"
          },
          {
            "name": "绵阳",
            "value": "MIG"
          },
          {
            "name": "牡丹江",
            "value": "MDG"
          },
          {
            "name": "南昌",
            "value": "KHN"
          },
          {
            "name": "南京",
            "value": "NKG"
          },
          {
            "name": "南宁",
            "value": "NNG"
          },
          {
            "name": "宁波",
            "value": "NGB"
          },
          {
            "name": "青岛",
            "value": "TAO"
          },
          {
            "name": "齐齐哈尔",
            "value": "NDG"
          },
          {
            "name": "泉州",
            "value": "JJN"
          },
          {
            "name": "三亚",
            "value": "SYX"
          },
          {
            "name": "上海",
            "value": "SHA"
          },
          {
            "name": "沈阳",
            "value": "SHE"
          },
          {
            "name": "深圳",
            "value": "SZX"
          },
          {
            "name": "十堰",
            "value": "WDS"
          },
          {
            "name": "太原",
            "value": "TYN"
          },
          {
            "name": "台州",
            "value": "HYN"
          },
          {
            "name": "天津",
            "value": "TSN"
          },
          {
            "name": "威海",
            "value": "WEH"
          },
          {
            "name": "温州",
            "value": "WNZ"
          },
          {
            "name": "武汉",
            "value": "WUH"
          },
          {
            "name": "乌鲁木齐",
            "value": "URC"
          },
          {
            "name": "厦门",
            "value": "XMN"
          },
          {
            "name": "西安",
            "value": "SIA"
          },
          {
            "name": "西宁",
            "value": "XNN"
          },
          {
            "name": "延吉",
            "value": "YNJ"
          },
          {
            "name": "烟台",
            "value": "YNT"
          },
          {
            "name": "银川",
            "value": "INC"
          },
          {
            "name": "郑州",
            "value": "CGO"
          },
          {
            "name": "舟山",
            "value": "HSN"
          },
          {
            "name": "珠海",
            "value": "ZUH"
          }
        ],
        "destCityList": [
          {
            "name": "桂林",
            "value": "KWL"
          },
          {
            "name": "青岛",
            "value": "TAO"
          },
          {
            "name": "济南",
            "value": "TNA"
          },
          {
            "name": "合肥",
            "value": "HFE"
          },
          {
            "name": "太原",
            "value": "TYN"
          },
          {
            "name": "长沙",
            "value": "CSX"
          },
          {
            "name": "郑州",
            "value": "CGO"
          },
          {
            "name": "武汉",
            "value": "WUH"
          },
          {
            "name": "珠海",
            "value": "ZUH"
          },
          {
            "name": "牡丹江",
            "value": "MDG"
          },
          {
            "name": "沈阳",
            "value": "SHE"
          },
          {
            "name": "海口",
            "value": "HAK"
          },
          {
            "name": "呼和浩特",
            "value": "HET"
          },
          {
            "name": "包头",
            "value": "BAV"
          },
          {
            "name": "厦门",
            "value": "XMN"
          },
          {
            "name": "西宁",
            "value": "XNN"
          },
          {
            "name": "大连",
            "value": "DLC"
          },
          {
            "name": "烟台",
            "value": "YNT"
          },
          {
            "name": "银川",
            "value": "INC"
          },
          {
            "name": "南宁",
            "value": "NNG"
          },
          {
            "name": "乌鲁木齐",
            "value": "URC"
          },
          {
            "name": "北京",
            "value": "BJS"
          },
          {
            "name": "喀什",
            "value": "KHG"
          },
          {
            "name": "上海",
            "value": "SHA"
          },
          {
            "name": "泉州",
            "value": "JJN"
          },
          {
            "name": "南昌",
            "value": "KHN"
          },
          {
            "name": "齐齐哈尔",
            "value": "NDG"
          },
          {
            "name": "延吉",
            "value": "YNJ"
          },
          {
            "name": "贵阳",
            "value": "KWE"
          },
          {
            "name": "兰州",
            "value": "LHW"
          },
          {
            "name": "揭阳",
            "value": "SWA"
          },
          {
            "name": "临沂",
            "value": "LYI"
          },
          {
            "name": "重庆",
            "value": "CKG"
          },
          {
            "name": "舟山",
            "value": "HSN"
          },
          {
            "name": "十堰",
            "value": "WDS"
          },
          {
            "name": "杭州",
            "value": "HGH"
          },
          {
            "name": "宁波",
            "value": "NGB"
          },
          {
            "name": "西安",
            "value": "SIA"
          },
          {
            "name": "南京",
            "value": "NKG"
          },
          {
            "name": "长春",
            "value": "CGQ"
          },
          {
            "name": "惠州",
            "value": "HUZ"
          },
          {
            "name": "天津",
            "value": "TSN"
          },
          {
            "name": "哈尔滨",
            "value": "HRB"
          },
          {
            "name": "威海",
            "value": "WEH"
          },
          {
            "name": "徐州",
            "value": "XUZ"
          },
          {
            "name": "成都",
            "value": "CTU"
          },
          {
            "name": "深圳",
            "value": "SZX"
          },
          {
            "name": "福州",
            "value": "FOC"
          },
          {
            "name": "台州",
            "value": "HYN"
          },
          {
            "name": "建三江",
            "value": "JSJ"
          },
          {
            "name": "昆明",
            "value": "KMG"
          },
          {
            "name": "佳木斯",
            "value": "JMU"
          },
          {
            "name": "库尔勒",
            "value": "KRL"
          },
          {
            "name": "绵阳",
            "value": "MIG"
          },
          {
            "name": "阿克苏",
            "value": "AKU"
          },
          {
            "name": "广州",
            "value": "CAN"
          },
          {
            "name": "丽江",
            "value": "LJG"
          },
          {
            "name": "三亚",
            "value": "SYX"
          }
        ],
        "floatingLayerUrl": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/list-picker?type=flightshelf&itemId=************"
      },
      "tag": "availableDepartureCityList"
    },
   "packageShelf": {
      "data": {
        "packageInfos": [
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "KWL",
              "TAO",
              "TNA",
              "HFE",
              "NNG",
              "TYN",
              "CGO",
              "SIA",
              "WUH",
              "TSN",
              "KHN",
              "SHE",
              "HAK",
              "BAV",
              "XMN",
              "XNN",
              "DLC",
              "WEH",
              "YNT",
              "INC"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 20000,
            "finalPriceText": "200",
            "hotRoutes": "热门航线：银川、沈阳、大连等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5785957651326&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "KWL": [
                  "HAK"
                ],
                "TAO": [
                  "SHE",
                  "CGO"
                ],
                "TNA": [
                  "DLC",
                  "WUH"
                ],
                "HFE": [
                  "TAO"
                ],
                "NNG": [
                  "WUH"
                ],
                "TYN": [
                  "BAV"
                ],
                "CGO": [
                  "INC"
                ],
                "SIA": [
                  "TNA"
                ],
                "WUH": [
                  "TNA",
                  "XMN",
                  "YNT"
                ],
                "TSN": [
                  "HET"
                ],
                "KHN": [
                  "ZUH"
                ],
                "SHE": [
                  "TAO",
                  "TNA",
                  "TYN"
                ],
                "HAK": [
                  "KWL",
                  "CSX"
                ],
                "BAV": [
                  "TYN"
                ],
                "XMN": [
                  "KWL"
                ],
                "XNN": [
                  "INC"
                ],
                "DLC": [
                  "MDG",
                  "TAO",
                  "TNA"
                ],
                "WEH": [
                  "TNA"
                ],
                "YNT": [
                  "SHE",
                  "HFE",
                  "TNA"
                ],
                "INC": [
                  "TNA",
                  "XNN",
                  "CGO"
                ]
              },
              "availableDepGroupByArr": {
                "KWL": [
                  "HAK",
                  "XMN"
                ],
                "TAO": [
                  "SHE",
                  "HFE",
                  "DLC"
                ],
                "TNA": [
                  "SHE",
                  "DLC",
                  "SIA",
                  "WUH",
                  "WEH",
                  "YNT",
                  "INC"
                ],
                "HFE": [
                  "YNT"
                ],
                "TYN": [
                  "SHE",
                  "BAV"
                ],
                "CSX": [
                  "HAK"
                ],
                "CGO": [
                  "TAO",
                  "INC"
                ],
                "WUH": [
                  "TNA",
                  "NNG"
                ],
                "ZUH": [
                  "KHN"
                ],
                "MDG": [
                  "DLC"
                ],
                "SHE": [
                  "TAO",
                  "YNT"
                ],
                "HAK": [
                  "KWL"
                ],
                "HET": [
                  "TSN"
                ],
                "BAV": [
                  "TYN"
                ],
                "XMN": [
                  "WUH"
                ],
                "XNN": [
                  "INC"
                ],
                "DLC": [
                  "TNA"
                ],
                "YNT": [
                  "WUH"
                ],
                "INC": [
                  "XNN",
                  "CGO"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "经济舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次经济舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01JomGj81r2UI897hsr_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DJoTOJ1r2UI7fso1k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZPVGwt1r2UI6NNYQ0_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01X0hfA71r2UI78Ybag_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014N1Nfl1r2UI2vgbp8_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN017LyniK1r2UI4wgQGC_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Q6p1Xd1r2UI3rQlEm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01aPKXYz1r2UI3rRtwZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN016bCzDS1r2UI6NMgNZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01zNstcl1r2UI4wfopq_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01as3GZ51r2UI5O7HKm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN012iL6D91r2UI3rRpmI_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01RinYHm1r2UHyazPpW_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01IOOhoH1r2UI2vcmni_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014i0GEr1r2UI4wgxW4_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Ou65b01r2UI6NMY3j_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN017ocXD31r2UI4wfU4w_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "经济舱200元套餐",
            "packagePid": "5919063",
            "packageVId": "41363006",
            "price": 20000,
            "priceText": "200",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5785957651326,
            "skuPvId": "5919063:3266779",
            "skuRoutes": [
              {
                "arrival": "青岛、牡丹江、济南",
                "departure": "大连"
              },
              {
                "arrival": "济南、烟台、厦门",
                "departure": "武汉"
              },
              {
                "arrival": "武汉、大连",
                "departure": "济南"
              },
              {
                "arrival": "青岛、济南、太原",
                "departure": "沈阳"
              },
              {
                "arrival": "郑州、济南、西宁",
                "departure": "银川"
              },
              {
                "arrival": "沈阳、郑州",
                "departure": "青岛"
              },
              {
                "arrival": "合肥、沈阳、济南",
                "departure": "烟台"
              },
              {
                "arrival": "长沙、桂林",
                "departure": "海口"
              },
              {
                "arrival": "青岛",
                "departure": "合肥"
              },
              {
                "arrival": "太原",
                "departure": "包头"
              },
              {
                "arrival": "银川",
                "departure": "郑州"
              },
              {
                "arrival": "珠海",
                "departure": "南昌"
              },
              {
                "arrival": "海口",
                "departure": "桂林"
              },
              {
                "arrival": "武汉",
                "departure": "南宁"
              },
              {
                "arrival": "呼和浩特",
                "departure": "天津"
              },
              {
                "arrival": "包头",
                "departure": "太原"
              },
              {
                "arrival": "济南",
                "departure": "威海"
              },
              {
                "arrival": "济南",
                "departure": "西安"
              },
              {
                "arrival": "桂林",
                "departure": "厦门"
              },
              {
                "arrival": "银川",
                "departure": "西宁"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "TAO",
              "TNA",
              "HFE",
              "URC",
              "BJS",
              "SHA",
              "JJN",
              "KHN",
              "NDG",
              "SHE",
              "MDG",
              "LHW",
              "FOC",
              "SWA",
              "LYI",
              "YNT",
              "CKG",
              "KWL",
              "HSN",
              "WDS",
              "TYN",
              "NGB",
              "HGH",
              "CSX",
              "CGO",
              "SIA",
              "WUH",
              "NKG",
              "CGQ",
              "ZUH",
              "TSN",
              "HAK",
              "HET",
              "XMN",
              "HRB",
              "DLC",
              "INC"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 30000,
            "finalPriceText": "300",
            "hotRoutes": "热门航线：济南、青岛、厦门等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5618345389687&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "TAO": [
                  "HFE",
                  "HGH",
                  "NGB",
                  "CSX",
                  "CGO",
                  "SIA",
                  "NKG",
                  "WUH",
                  "CGQ",
                  "KHN",
                  "SHE",
                  "YNJ",
                  "DLC"
                ],
                "TNA": [
                  "CSX",
                  "SIA",
                  "WUH",
                  "CGQ",
                  "SHA",
                  "JJN",
                  "KHN",
                  "HRB",
                  "DLC",
                  "LHW",
                  "WEH",
                  "CKG",
                  "INC"
                ],
                "HFE": [
                  "KWL",
                  "CKG",
                  "YNT"
                ],
                "URC": [
                  "KHG"
                ],
                "BJS": [
                  "YNT"
                ],
                "SHA": [
                  "TNA",
                  "YNT"
                ],
                "JJN": [
                  "HSN"
                ],
                "KHN": [
                  "HAK",
                  "TNA",
                  "ZUH"
                ],
                "NDG": [
                  "DLC"
                ],
                "SHE": [
                  "TAO",
                  "TNA",
                  "CGO",
                  "XUZ",
                  "YNT"
                ],
                "MDG": [
                  "DLC"
                ],
                "LHW": [
                  "TNA",
                  "URC"
                ],
                "FOC": [
                  "TNA"
                ],
                "SWA": [
                  "CSX"
                ],
                "LYI": [
                  "TYN"
                ],
                "YNT": [
                  "SHE",
                  "BJS",
                  "WUH",
                  "NKG",
                  "SHA"
                ],
                "CKG": [
                  "HFE",
                  "TYN",
                  "ZUH"
                ],
                "KWL": [
                  "XMN",
                  "CGO",
                  "NKG"
                ],
                "HSN": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "JJN"
                ],
                "WDS": [
                  "TYN"
                ],
                "TYN": [
                  "SHE",
                  "TAO",
                  "WDS",
                  "LYI",
                  "CKG"
                ],
                "NGB": [
                  "TAO",
                  "CGO"
                ],
                "HGH": [
                  "TAO"
                ],
                "CSX": [
                  "TAO",
                  "HAK",
                  "TNA",
                  "XMN",
                  "SWA"
                ],
                "CGO": [
                  "KWL",
                  "TAO",
                  "XMN",
                  "KWE"
                ],
                "SIA": [
                  "TAO",
                  "TNA",
                  "KWE"
                ],
                "WUH": [
                  "TAO",
                  "TNA",
                  "NNG",
                  "XMN",
                  "HUZ"
                ],
                "NKG": [
                  "TAO",
                  "YNT"
                ],
                "CGQ": [
                  "TAO",
                  "TNA",
                  "YNT"
                ],
                "ZUH": [
                  "KHN",
                  "HAK",
                  "CKG"
                ],
                "TSN": [
                  "HRB"
                ],
                "HAK": [
                  "KHN",
                  "ZUH"
                ],
                "HET": [
                  "TSN"
                ],
                "XMN": [
                  "SHE",
                  "HSN",
                  "HGH",
                  "CSX",
                  "CGO",
                  "WUH"
                ],
                "HRB": [
                  "TNA",
                  "TSN"
                ],
                "DLC": [
                  "NDG",
                  "TAO",
                  "TNA"
                ],
                "INC": [
                  "TNA"
                ]
              },
              "availableDepGroupByArr": {
                "TAO": [
                  "SHE",
                  "HSN",
                  "TYN",
                  "NGB",
                  "HGH",
                  "CSX",
                  "DLC",
                  "CGO",
                  "SIA",
                  "WUH",
                  "NKG",
                  "CGQ"
                ],
                "HFE": [
                  "TAO",
                  "CKG"
                ],
                "TNA": [
                  "HSN",
                  "CSX",
                  "SIA",
                  "WUH",
                  "CGQ",
                  "SHA",
                  "KHN",
                  "SHE",
                  "HRB",
                  "DLC",
                  "LHW",
                  "FOC",
                  "INC"
                ],
                "NNG": [
                  "WUH"
                ],
                "URC": [
                  "LHW"
                ],
                "BJS": [
                  "YNT"
                ],
                "KHG": [
                  "URC"
                ],
                "SHA": [
                  "TNA",
                  "YNT"
                ],
                "JJN": [
                  "HSN",
                  "TNA"
                ],
                "KHN": [
                  "TAO",
                  "HAK",
                  "TNA",
                  "ZUH"
                ],
                "NDG": [
                  "DLC"
                ],
                "SHE": [
                  "TAO",
                  "XMN",
                  "TYN",
                  "YNT"
                ],
                "YNJ": [
                  "TAO"
                ],
                "KWE": [
                  "CGO",
                  "SIA"
                ],
                "LHW": [
                  "TNA"
                ],
                "SWA": [
                  "CSX"
                ],
                "LYI": [
                  "TYN"
                ],
                "CKG": [
                  "TNA",
                  "HFE",
                  "TYN",
                  "ZUH"
                ],
                "YNT": [
                  "SHE",
                  "HFE",
                  "BJS",
                  "NKG",
                  "CGQ",
                  "SHA"
                ],
                "KWL": [
                  "HFE",
                  "CGO"
                ],
                "HSN": [
                  "XMN",
                  "JJN"
                ],
                "WDS": [
                  "TYN"
                ],
                "HGH": [
                  "TAO",
                  "XMN"
                ],
                "NGB": [
                  "TAO"
                ],
                "TYN": [
                  "WDS",
                  "LYI",
                  "CKG"
                ],
                "CSX": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "SWA"
                ],
                "SIA": [
                  "TAO",
                  "TNA"
                ],
                "CGO": [
                  "SHE",
                  "KWL",
                  "TAO",
                  "XMN",
                  "NGB"
                ],
                "WUH": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "YNT"
                ],
                "NKG": [
                  "KWL",
                  "TAO",
                  "YNT"
                ],
                "CGQ": [
                  "TAO",
                  "TNA"
                ],
                "HUZ": [
                  "WUH"
                ],
                "ZUH": [
                  "KHN",
                  "HAK",
                  "CKG"
                ],
                "TSN": [
                  "HET",
                  "HRB"
                ],
                "HAK": [
                  "KHN",
                  "CSX",
                  "ZUH"
                ],
                "XMN": [
                  "KWL",
                  "HSN",
                  "CSX",
                  "CGO",
                  "WUH"
                ],
                "HRB": [
                  "TNA",
                  "TSN"
                ],
                "DLC": [
                  "NDG",
                  "MDG",
                  "TAO",
                  "TNA"
                ],
                "WEH": [
                  "TNA"
                ],
                "XUZ": [
                  "SHE"
                ],
                "INC": [
                  "TNA"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "经济舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次经济舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01JomGj81r2UI897hsr_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DJoTOJ1r2UI7fso1k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZPVGwt1r2UI6NNYQ0_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01X0hfA71r2UI78Ybag_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014N1Nfl1r2UI2vgbp8_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN017LyniK1r2UI4wgQGC_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Q6p1Xd1r2UI3rQlEm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01aPKXYz1r2UI3rRtwZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN016bCzDS1r2UI6NMgNZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01zNstcl1r2UI4wfopq_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01as3GZ51r2UI5O7HKm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN012iL6D91r2UI3rRpmI_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01RinYHm1r2UHyazPpW_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01IOOhoH1r2UI2vcmni_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014i0GEr1r2UI4wgxW4_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Ou65b01r2UI6NMY3j_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN017ocXD31r2UI4wfU4w_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "经济舱300元套餐",
            "packagePid": "5919063",
            "packageVId": "41363007",
            "price": 30000,
            "priceText": "300",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5618345389687,
            "skuPvId": "5919063:3266781",
            "skuRoutes": [
              {
                "arrival": "大连、长沙、长春、哈尔滨、银川、兰州、西安、重庆、泉州、南昌、上海、威海、武汉",
                "departure": "济南"
              },
              {
                "arrival": "长春、长沙、大连、郑州、合肥、杭州、南昌、宁波、南京、沈阳、武汉、西安、延吉",
                "departure": "青岛"
              },
              {
                "arrival": "青岛、济南、海口、揭阳、厦门",
                "departure": "长沙"
              },
              {
                "arrival": "济南、青岛、齐齐哈尔",
                "departure": "大连"
              },
              {
                "arrival": "郑州、青岛、烟台、济南、徐州",
                "departure": "沈阳"
              },
              {
                "arrival": "舟山、沈阳、郑州、长沙、杭州、武汉",
                "departure": "厦门"
              },
              {
                "arrival": "青岛、济南、惠州、南宁、厦门",
                "departure": "武汉"
              },
              {
                "arrival": "北京、武汉、南京、上海、沈阳",
                "departure": "烟台"
              },
              {
                "arrival": "青岛、厦门、贵阳、桂林",
                "departure": "郑州"
              },
              {
                "arrival": "厦门、泉州、青岛、济南",
                "departure": "舟山"
              },
              {
                "arrival": "重庆、临沂、沈阳、青岛、十堰",
                "departure": "太原"
              },
              {
                "arrival": "青岛、济南、烟台",
                "departure": "长春"
              },
              {
                "arrival": "济南、海口、珠海",
                "departure": "南昌"
              },
              {
                "arrival": "南京、郑州、厦门",
                "departure": "桂林"
              },
              {
                "arrival": "济南、乌鲁木齐",
                "departure": "兰州"
              },
              {
                "arrival": "合肥、太原、珠海",
                "departure": "重庆"
              },
              {
                "arrival": "重庆、桂林、烟台",
                "departure": "合肥"
              },
              {
                "arrival": "贵阳、青岛、济南",
                "departure": "西安"
              },
              {
                "arrival": "重庆、海口、南昌",
                "departure": "珠海"
              },
              {
                "arrival": "南昌、珠海",
                "departure": "海口"
              },
              {
                "arrival": "济南、天津",
                "departure": "哈尔滨"
              },
              {
                "arrival": "大连",
                "departure": "牡丹江"
              },
              {
                "arrival": "郑州、青岛",
                "departure": "宁波"
              },
              {
                "arrival": "青岛、烟台",
                "departure": "南京"
              },
              {
                "arrival": "喀什",
                "departure": "乌鲁木齐"
              },
              {
                "arrival": "济南",
                "departure": "福州"
              },
              {
                "arrival": "天津",
                "departure": "呼和浩特"
              },
              {
                "arrival": "青岛",
                "departure": "杭州"
              },
              {
                "arrival": "济南",
                "departure": "银川"
              },
              {
                "arrival": "舟山",
                "departure": "泉州"
              },
              {
                "arrival": "太原",
                "departure": "临沂"
              },
              {
                "arrival": "大连",
                "departure": "齐齐哈尔"
              },
              {
                "arrival": "烟台",
                "departure": "北京"
              },
              {
                "arrival": "烟台、济南",
                "departure": "上海"
              },
              {
                "arrival": "长沙",
                "departure": "揭阳"
              },
              {
                "arrival": "哈尔滨",
                "departure": "天津"
              },
              {
                "arrival": "太原",
                "departure": "十堰"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "TAO",
              "CTU",
              "TNA",
              "URC",
              "BJS",
              "KHG",
              "SHA",
              "JJN",
              "KHN",
              "NDG",
              "SZX",
              "SHE",
              "KWE",
              "LHW",
              "FOC",
              "CKG",
              "YNT",
              "WNZ",
              "KMG",
              "WDS",
              "TYN",
              "HGH",
              "SIA",
              "CGO",
              "MIG",
              "NKG",
              "CGQ",
              "ZUH",
              "TSN",
              "CAN",
              "HAK",
              "HET",
              "XMN",
              "HRB",
              "DLC",
              "INC"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 40000,
            "finalPriceText": "400",
            "hotRoutes": "热门航线：上海、烟台、重庆等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5618344709789&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "TAO": [
                  "KMG",
                  "CTU",
                  "HGH",
                  "TYN",
                  "SIA",
                  "WUH",
                  "NDG",
                  "SHE",
                  "JMU",
                  "XMN",
                  "HRB",
                  "DLC",
                  "LHW",
                  "FOC",
                  "CKG",
                  "INC"
                ],
                "CTU": [
                  "TAO",
                  "XMN"
                ],
                "TNA": [
                  "SZX",
                  "CTU",
                  "HET",
                  "XMN",
                  "HRB",
                  "SIA",
                  "FOC",
                  "CGQ",
                  "SHA",
                  "CKG"
                ],
                "URC": [
                  "LHW",
                  "KHG",
                  "INC"
                ],
                "BJS": [
                  "FOC",
                  "ZUH",
                  "YNT"
                ],
                "KHG": [
                  "URC"
                ],
                "SHA": [
                  "TNA",
                  "XMN",
                  "ZUH",
                  "YNT"
                ],
                "JJN": [
                  "TNA"
                ],
                "KHN": [
                  "TAO"
                ],
                "NDG": [
                  "TAO"
                ],
                "SZX": [
                  "TNA"
                ],
                "SHE": [
                  "XMN"
                ],
                "KWE": [
                  "XMN",
                  "TYN",
                  "SIA"
                ],
                "LHW": [
                  "TAO"
                ],
                "FOC": [
                  "BJS"
                ],
                "CKG": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "YNT"
                ],
                "YNT": [
                  "JMU",
                  "NKG",
                  "CGQ",
                  "SHA",
                  "CKG"
                ],
                "WNZ": [
                  "ZUH"
                ],
                "KMG": [
                  "TAO",
                  "NKG"
                ],
                "WDS": [
                  "XMN"
                ],
                "TYN": [
                  "XMN",
                  "HGH",
                  "KWE"
                ],
                "HGH": [
                  "TAO",
                  "XMN"
                ],
                "SIA": [
                  "TAO",
                  "TNA",
                  "XMN"
                ],
                "CGO": [
                  "SHE",
                  "NGB"
                ],
                "MIG": [
                  "TNA"
                ],
                "NKG": [
                  "HET"
                ],
                "CGQ": [
                  "TAO",
                  "TNA"
                ],
                "ZUH": [
                  "BJS"
                ],
                "TSN": [
                  "XMN"
                ],
                "CAN": [
                  "TAO",
                  "TNA"
                ],
                "HAK": [
                  "TAO",
                  "TNA",
                  "NGB",
                  "HYN"
                ],
                "HET": [
                  "TNA"
                ],
                "XMN": [
                  "CTU",
                  "TNA",
                  "WDS",
                  "TYN",
                  "HGH",
                  "SIA",
                  "CGO",
                  "NKG",
                  "WUH",
                  "SHA",
                  "TSN",
                  "SHE",
                  "KWE"
                ],
                "HRB": [
                  "TAO",
                  "TNA"
                ],
                "DLC": [
                  "JSJ",
                  "HGH",
                  "CKG"
                ],
                "INC": [
                  "URC"
                ]
              },
              "availableDepGroupByArr": {
                "TAO": [
                  "KHN",
                  "NDG",
                  "CAN",
                  "HAK",
                  "KMG",
                  "CTU",
                  "HGH",
                  "HRB",
                  "SIA",
                  "LHW",
                  "CGQ",
                  "CKG"
                ],
                "CTU": [
                  "TAO",
                  "TNA",
                  "XMN"
                ],
                "TNA": [
                  "SZX",
                  "CAN",
                  "HAK",
                  "HET",
                  "XMN",
                  "HRB",
                  "SIA",
                  "MIG",
                  "CGQ",
                  "SHA",
                  "CKG",
                  "JJN"
                ],
                "URC": [
                  "KHG",
                  "INC"
                ],
                "BJS": [
                  "FOC",
                  "ZUH"
                ],
                "KHG": [
                  "URC"
                ],
                "SHA": [
                  "TNA",
                  "XMN",
                  "YNT"
                ],
                "NDG": [
                  "TAO"
                ],
                "SZX": [
                  "TNA"
                ],
                "SHE": [
                  "TAO",
                  "XMN",
                  "CGO"
                ],
                "KWE": [
                  "XMN",
                  "TYN"
                ],
                "LHW": [
                  "TAO",
                  "URC"
                ],
                "FOC": [
                  "TAO",
                  "TNA",
                  "BJS"
                ],
                "HYN": [
                  "HAK"
                ],
                "CKG": [
                  "TAO",
                  "TNA",
                  "DLC",
                  "YNT"
                ],
                "YNT": [
                  "BJS",
                  "SHA",
                  "CKG"
                ],
                "JSJ": [
                  "DLC"
                ],
                "KMG": [
                  "TAO"
                ],
                "WDS": [
                  "XMN"
                ],
                "HGH": [
                  "TAO",
                  "XMN",
                  "TYN",
                  "DLC"
                ],
                "TYN": [
                  "TAO",
                  "XMN",
                  "KWE"
                ],
                "NGB": [
                  "HAK",
                  "CGO"
                ],
                "SIA": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "KWE"
                ],
                "CGO": [
                  "XMN"
                ],
                "WUH": [
                  "TAO",
                  "XMN"
                ],
                "NKG": [
                  "KMG",
                  "XMN",
                  "YNT"
                ],
                "CGQ": [
                  "TNA",
                  "YNT"
                ],
                "ZUH": [
                  "WNZ",
                  "BJS",
                  "SHA"
                ],
                "TSN": [
                  "XMN"
                ],
                "JMU": [
                  "TAO",
                  "YNT"
                ],
                "HET": [
                  "TNA",
                  "NKG"
                ],
                "XMN": [
                  "SHE",
                  "TAO",
                  "CTU",
                  "TNA",
                  "WDS",
                  "TYN",
                  "KWE",
                  "HGH",
                  "SIA",
                  "SHA",
                  "CKG",
                  "TSN"
                ],
                "HRB": [
                  "TAO",
                  "TNA"
                ],
                "DLC": [
                  "TAO"
                ],
                "INC": [
                  "TAO",
                  "URC"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "经济舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次经济舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01JomGj81r2UI897hsr_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DJoTOJ1r2UI7fso1k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZPVGwt1r2UI6NNYQ0_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01X0hfA71r2UI78Ybag_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014N1Nfl1r2UI2vgbp8_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN017LyniK1r2UI4wgQGC_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Q6p1Xd1r2UI3rQlEm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01aPKXYz1r2UI3rRtwZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN016bCzDS1r2UI6NMgNZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01zNstcl1r2UI4wfopq_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01as3GZ51r2UI5O7HKm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN012iL6D91r2UI3rRpmI_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01RinYHm1r2UHyazPpW_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01IOOhoH1r2UI2vcmni_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014i0GEr1r2UI4wgxW4_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Ou65b01r2UI6NMY3j_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN017ocXD31r2UI4wfU4w_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "经济舱400元套餐",
            "packagePid": "5919063",
            "packageVId": "41363008",
            "price": 40000,
            "priceText": "400",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5618344709789,
            "skuPvId": "5919063:3266785",
            "skuRoutes": [
              {
                "arrival": "大连、哈尔滨、重庆、杭州、西安、昆明、成都、福州、银川、佳木斯、兰州、齐齐哈尔、沈阳、太原、武汉、厦门",
                "departure": "青岛"
              },
              {
                "arrival": "济南、贵阳、太原、西安、郑州、杭州、南京、上海、沈阳、成都、天津、十堰、武汉",
                "departure": "厦门"
              },
              {
                "arrival": "厦门、重庆、哈尔滨、西安、长春、福州、呼和浩特、上海、深圳、成都",
                "departure": "济南"
              },
              {
                "arrival": "青岛、济南、厦门、烟台",
                "departure": "重庆"
              },
              {
                "arrival": "福州、珠海、烟台",
                "departure": "北京"
              },
              {
                "arrival": "青岛、济南",
                "departure": "哈尔滨"
              },
              {
                "arrival": "青岛、济南、厦门",
                "departure": "西安"
              },
              {
                "arrival": "上海、长春、重庆、佳木斯、南京",
                "departure": "烟台"
              },
              {
                "arrival": "烟台、济南、厦门、珠海",
                "departure": "上海"
              },
              {
                "arrival": "厦门、杭州、贵阳",
                "departure": "太原"
              },
              {
                "arrival": "重庆、杭州、建三江",
                "departure": "大连"
              },
              {
                "arrival": "北京",
                "departure": "福州"
              },
              {
                "arrival": "台州、宁波、青岛、济南",
                "departure": "海口"
              },
              {
                "arrival": "南京、青岛",
                "departure": "昆明"
              },
              {
                "arrival": "厦门、太原、西安",
                "departure": "贵阳"
              },
              {
                "arrival": "青岛、厦门",
                "departure": "成都"
              },
              {
                "arrival": "银川、喀什、兰州",
                "departure": "乌鲁木齐"
              },
              {
                "arrival": "沈阳、宁波",
                "departure": "郑州"
              },
              {
                "arrival": "济南、青岛",
                "departure": "长春"
              },
              {
                "arrival": "厦门",
                "departure": "沈阳"
              },
              {
                "arrival": "青岛、济南",
                "departure": "广州"
              },
              {
                "arrival": "青岛、厦门",
                "departure": "杭州"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "银川"
              },
              {
                "arrival": "北京",
                "departure": "珠海"
              },
              {
                "arrival": "济南",
                "departure": "呼和浩特"
              },
              {
                "arrival": "济南",
                "departure": "泉州"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "喀什"
              },
              {
                "arrival": "青岛",
                "departure": "南昌"
              },
              {
                "arrival": "青岛",
                "departure": "兰州"
              },
              {
                "arrival": "济南",
                "departure": "绵阳"
              },
              {
                "arrival": "青岛",
                "departure": "齐齐哈尔"
              },
              {
                "arrival": "呼和浩特",
                "departure": "南京"
              },
              {
                "arrival": "济南",
                "departure": "深圳"
              },
              {
                "arrival": "厦门",
                "departure": "天津"
              },
              {
                "arrival": "厦门",
                "departure": "十堰"
              },
              {
                "arrival": "珠海",
                "departure": "温州"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "TAO",
              "KRL",
              "CTU",
              "TNA",
              "NNG",
              "URC",
              "BJS",
              "KHG",
              "SHA",
              "SZX",
              "SHE",
              "MDG",
              "YNJ",
              "KWE",
              "XNN",
              "HYN",
              "CKG",
              "YNT",
              "KWL",
              "WNZ",
              "KMG",
              "HGH",
              "NGB",
              "SIA",
              "AKU",
              "ZUH",
              "CGQ",
              "TSN",
              "CAN",
              "HAK",
              "JMU",
              "HET",
              "BAV",
              "XMN",
              "HRB",
              "DLC",
              "INC"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 50000,
            "finalPriceText": "500",
            "hotRoutes": "热门航线：呼和浩特、乌鲁木齐、成都等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5784138362948&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "TAO": [
                  "MDG",
                  "HAK",
                  "KMG",
                  "CTU",
                  "HET",
                  "KWE",
                  "HGH",
                  "XMN",
                  "BJS",
                  "ZUH",
                  "SHA",
                  "CKG"
                ],
                "KRL": [
                  "CGO",
                  "SIA"
                ],
                "CTU": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "YNT"
                ],
                "TNA": [
                  "SZX",
                  "CAN",
                  "HAK",
                  "CTU",
                  "URC",
                  "KWE",
                  "XMN",
                  "XNN",
                  "MIG",
                  "CKG"
                ],
                "NNG": [
                  "TNA"
                ],
                "URC": [
                  "TYN",
                  "LHW"
                ],
                "BJS": [
                  "TAO",
                  "XMN",
                  "ZUH",
                  "YNT"
                ],
                "KHG": [
                  "URC"
                ],
                "SHA": [
                  "TAO",
                  "ZUH"
                ],
                "SZX": [
                  "SHE",
                  "TAO",
                  "TNA"
                ],
                "SHE": [
                  "SZX",
                  "KWE",
                  "NKG"
                ],
                "MDG": [
                  "TAO"
                ],
                "YNJ": [
                  "TAO"
                ],
                "KWE": [
                  "CGO"
                ],
                "XNN": [
                  "TNA"
                ],
                "HYN": [
                  "TNA"
                ],
                "CKG": [
                  "TNA",
                  "HET",
                  "DLC"
                ],
                "YNT": [
                  "CAN",
                  "CTU",
                  "XMN",
                  "BJS"
                ],
                "KWL": [
                  "TNA"
                ],
                "WNZ": [
                  "TNA"
                ],
                "KMG": [
                  "TNA",
                  "NKG"
                ],
                "HGH": [
                  "TAO",
                  "TYN",
                  "DLC"
                ],
                "NGB": [
                  "URC"
                ],
                "SIA": [
                  "KRL",
                  "XMN",
                  "AKU"
                ],
                "AKU": [
                  "SIA"
                ],
                "ZUH": [
                  "TNA",
                  "BJS",
                  "TSN"
                ],
                "CGQ": [
                  "CKG"
                ],
                "TSN": [
                  "ZUH"
                ],
                "CAN": [
                  "TNA"
                ],
                "HAK": [
                  "TNA",
                  "CGO"
                ],
                "JMU": [
                  "TAO",
                  "YNT"
                ],
                "HET": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "NKG",
                  "CKG",
                  "INC"
                ],
                "BAV": [
                  "XMN"
                ],
                "XMN": [
                  "TAO",
                  "CTU",
                  "HET",
                  "TNA",
                  "BJS",
                  "CKG",
                  "YNT"
                ],
                "HRB": [
                  "TAO"
                ],
                "DLC": [
                  "XMN",
                  "KWE"
                ],
                "INC": [
                  "TAO"
                ]
              },
              "availableDepGroupByArr": {
                "TAO": [
                  "SZX",
                  "MDG",
                  "JMU",
                  "YNJ",
                  "CTU",
                  "HET",
                  "XMN",
                  "HGH",
                  "HRB",
                  "BJS",
                  "SHA",
                  "INC"
                ],
                "KRL": [
                  "SIA"
                ],
                "CTU": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "YNT"
                ],
                "TNA": [
                  "KWL",
                  "WNZ",
                  "KMG",
                  "CTU",
                  "NNG",
                  "ZUH",
                  "SZX",
                  "CAN",
                  "HAK",
                  "HET",
                  "XMN",
                  "XNN",
                  "HYN",
                  "CKG"
                ],
                "URC": [
                  "TNA",
                  "NGB",
                  "KHG"
                ],
                "BJS": [
                  "TAO",
                  "XMN",
                  "ZUH",
                  "YNT"
                ],
                "SHA": [
                  "TAO"
                ],
                "SZX": [
                  "SHE",
                  "TNA"
                ],
                "MDG": [
                  "TAO"
                ],
                "SHE": [
                  "SZX"
                ],
                "KWE": [
                  "SHE",
                  "TAO",
                  "TNA",
                  "DLC"
                ],
                "XNN": [
                  "TNA"
                ],
                "LHW": [
                  "URC"
                ],
                "CKG": [
                  "TAO",
                  "TNA",
                  "HET",
                  "XMN",
                  "CGQ"
                ],
                "YNT": [
                  "JMU",
                  "CTU",
                  "XMN",
                  "BJS"
                ],
                "KMG": [
                  "TAO"
                ],
                "HGH": [
                  "TAO"
                ],
                "TYN": [
                  "URC",
                  "HGH"
                ],
                "MIG": [
                  "TNA"
                ],
                "CGO": [
                  "HAK",
                  "KRL",
                  "KWE"
                ],
                "SIA": [
                  "KRL",
                  "AKU"
                ],
                "NKG": [
                  "SHE",
                  "KMG",
                  "HET"
                ],
                "AKU": [
                  "SIA"
                ],
                "ZUH": [
                  "TAO",
                  "BJS",
                  "SHA",
                  "TSN"
                ],
                "TSN": [
                  "ZUH"
                ],
                "CAN": [
                  "TNA",
                  "YNT"
                ],
                "HAK": [
                  "TAO",
                  "TNA"
                ],
                "HET": [
                  "TAO",
                  "XMN",
                  "CKG"
                ],
                "XMN": [
                  "TAO",
                  "CTU",
                  "TNA",
                  "HET",
                  "BAV",
                  "BJS",
                  "DLC",
                  "SIA",
                  "YNT"
                ],
                "DLC": [
                  "HGH",
                  "CKG"
                ],
                "INC": [
                  "HET"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "经济舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次经济舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01JomGj81r2UI897hsr_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DJoTOJ1r2UI7fso1k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZPVGwt1r2UI6NNYQ0_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01X0hfA71r2UI78Ybag_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014N1Nfl1r2UI2vgbp8_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN017LyniK1r2UI4wgQGC_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Q6p1Xd1r2UI3rQlEm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01aPKXYz1r2UI3rRtwZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN016bCzDS1r2UI6NMgNZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01zNstcl1r2UI4wfopq_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01as3GZ51r2UI5O7HKm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN012iL6D91r2UI3rRpmI_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01RinYHm1r2UHyazPpW_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01IOOhoH1r2UI2vcmni_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014i0GEr1r2UI4wgxW4_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Ou65b01r2UI6NMY3j_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN017ocXD31r2UI4wfU4w_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "经济舱500元套餐",
            "packagePid": "5919063",
            "packageVId": "41363009",
            "price": 50000,
            "priceText": "500",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5784138362948,
            "skuPvId": "5919063:3266786",
            "skuRoutes": [
              {
                "arrival": "重庆、贵阳、成都、杭州、牡丹江、北京、厦门、海口、呼和浩特、昆明、上海、珠海",
                "departure": "青岛"
              },
              {
                "arrival": "重庆、海口、成都、乌鲁木齐、广州、贵阳、绵阳、深圳、厦门、西宁",
                "departure": "济南"
              },
              {
                "arrival": "北京、青岛、重庆、呼和浩特、济南、成都、烟台",
                "departure": "厦门"
              },
              {
                "arrival": "厦门、青岛、烟台、珠海",
                "departure": "北京"
              },
              {
                "arrival": "济南、青岛、厦门、烟台",
                "departure": "成都"
              },
              {
                "arrival": "厦门、重庆、银川、南京、青岛、济南",
                "departure": "呼和浩特"
              },
              {
                "arrival": "济南、大连、呼和浩特",
                "departure": "重庆"
              },
              {
                "arrival": "北京、广州、成都、厦门",
                "departure": "烟台"
              },
              {
                "arrival": "济南、郑州",
                "departure": "海口"
              },
              {
                "arrival": "贵阳、南京、深圳",
                "departure": "沈阳"
              },
              {
                "arrival": "济南、北京、天津",
                "departure": "珠海"
              },
              {
                "arrival": "厦门、贵阳",
                "departure": "大连"
              },
              {
                "arrival": "大连、青岛、太原",
                "departure": "杭州"
              },
              {
                "arrival": "沈阳、青岛、济南",
                "departure": "深圳"
              },
              {
                "arrival": "阿克苏、库尔勒、厦门",
                "departure": "西安"
              },
              {
                "arrival": "济南",
                "departure": "广州"
              },
              {
                "arrival": "青岛、烟台",
                "departure": "佳木斯"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "喀什"
              },
              {
                "arrival": "南京、济南",
                "departure": "昆明"
              },
              {
                "arrival": "郑州、西安",
                "departure": "库尔勒"
              },
              {
                "arrival": "青岛",
                "departure": "牡丹江"
              },
              {
                "arrival": "青岛、珠海",
                "departure": "上海"
              },
              {
                "arrival": "珠海",
                "departure": "天津"
              },
              {
                "arrival": "兰州、太原",
                "departure": "乌鲁木齐"
              },
              {
                "arrival": "西安",
                "departure": "阿克苏"
              },
              {
                "arrival": "厦门",
                "departure": "包头"
              },
              {
                "arrival": "重庆",
                "departure": "长春"
              },
              {
                "arrival": "青岛",
                "departure": "哈尔滨"
              },
              {
                "arrival": "济南",
                "departure": "台州"
              },
              {
                "arrival": "青岛",
                "departure": "银川"
              },
              {
                "arrival": "郑州",
                "departure": "贵阳"
              },
              {
                "arrival": "济南",
                "departure": "桂林"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "宁波"
              },
              {
                "arrival": "济南",
                "departure": "南宁"
              },
              {
                "arrival": "济南",
                "departure": "温州"
              },
              {
                "arrival": "济南",
                "departure": "西宁"
              },
              {
                "arrival": "青岛",
                "departure": "延吉"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "TAO",
              "TNA",
              "NNG",
              "URC",
              "TYN",
              "HGH",
              "CGQ",
              "ZUH",
              "SZX",
              "CAN",
              "HAK",
              "SYX",
              "XMN",
              "KWE",
              "DLC",
              "HYN",
              "SWA",
              "LYI",
              "CKG",
              "INC"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 60000,
            "finalPriceText": "600",
            "hotRoutes": "热门航线：珠海、海口、长春等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5785959379554&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "TAO": [
                  "SZX",
                  "CAN",
                  "XMN",
                  "XNN",
                  "ZUH",
                  "SHA"
                ],
                "TNA": [
                  "SZX",
                  "CAN",
                  "HAK",
                  "KMG",
                  "NNG",
                  "URC",
                  "KWE",
                  "LJG",
                  "XMN",
                  "ZUH",
                  "SWA"
                ],
                "NNG": [
                  "TNA"
                ],
                "URC": [
                  "SHE",
                  "TNA",
                  "TYN"
                ],
                "TYN": [
                  "URC",
                  "CGQ"
                ],
                "HGH": [
                  "XMN"
                ],
                "CGQ": [
                  "TYN",
                  "XMN"
                ],
                "ZUH": [
                  "BJS"
                ],
                "SZX": [
                  "TAO",
                  "TNA"
                ],
                "CAN": [
                  "TAO"
                ],
                "HAK": [
                  "TAO"
                ],
                "SYX": [
                  "CGO"
                ],
                "XMN": [
                  "TAO",
                  "TYN",
                  "DLC",
                  "CGQ",
                  "LYI"
                ],
                "KWE": [
                  "TNA"
                ],
                "DLC": [
                  "ZUH"
                ],
                "HYN": [
                  "HAK"
                ],
                "SWA": [
                  "TNA"
                ],
                "LYI": [
                  "XMN"
                ],
                "CKG": [
                  "CGQ"
                ],
                "INC": [
                  "XMN"
                ]
              },
              "availableDepGroupByArr": {
                "TAO": [
                  "SZX",
                  "CAN",
                  "HAK",
                  "XMN"
                ],
                "KMG": [
                  "TNA"
                ],
                "NNG": [
                  "TNA"
                ],
                "TNA": [
                  "SZX",
                  "NNG",
                  "URC",
                  "KWE",
                  "SWA"
                ],
                "URC": [
                  "TNA",
                  "TYN"
                ],
                "LJG": [
                  "TNA"
                ],
                "TYN": [
                  "URC",
                  "XMN",
                  "CGQ"
                ],
                "CGO": [
                  "SYX"
                ],
                "BJS": [
                  "ZUH"
                ],
                "ZUH": [
                  "TAO",
                  "TNA",
                  "DLC"
                ],
                "CGQ": [
                  "XMN",
                  "TYN",
                  "CKG"
                ],
                "SHA": [
                  "TAO"
                ],
                "SZX": [
                  "TAO",
                  "TNA"
                ],
                "CAN": [
                  "TAO",
                  "TNA"
                ],
                "SHE": [
                  "URC"
                ],
                "HAK": [
                  "TNA",
                  "HYN"
                ],
                "KWE": [
                  "TNA"
                ],
                "XMN": [
                  "TAO",
                  "TNA",
                  "HGH",
                  "CGQ",
                  "LYI",
                  "INC"
                ],
                "XNN": [
                  "TAO"
                ],
                "DLC": [
                  "XMN"
                ],
                "SWA": [
                  "TNA"
                ],
                "LYI": [
                  "XMN"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "经济舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次经济舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01JomGj81r2UI897hsr_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DJoTOJ1r2UI7fso1k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZPVGwt1r2UI6NNYQ0_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01X0hfA71r2UI78Ybag_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014N1Nfl1r2UI2vgbp8_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN017LyniK1r2UI4wgQGC_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Q6p1Xd1r2UI3rQlEm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01aPKXYz1r2UI3rRtwZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN016bCzDS1r2UI6NMgNZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01zNstcl1r2UI4wfopq_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01as3GZ51r2UI5O7HKm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN012iL6D91r2UI3rRpmI_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01RinYHm1r2UHyazPpW_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01IOOhoH1r2UI2vcmni_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014i0GEr1r2UI4wgxW4_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Ou65b01r2UI6NMY3j_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN017ocXD31r2UI4wfU4w_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "经济舱600元套餐",
            "packagePid": "5919063",
            "packageVId": "41363010",
            "price": 60000,
            "priceText": "600",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5785959379554,
            "skuPvId": "5919063:3266789",
            "skuRoutes": [
              {
                "arrival": "乌鲁木齐、珠海、广州、海口、昆明、贵阳、丽江、南宁、揭阳、深圳、厦门",
                "departure": "济南"
              },
              {
                "arrival": "济南、沈阳、太原",
                "departure": "乌鲁木齐"
              },
              {
                "arrival": "厦门、广州、上海、深圳、西宁、珠海",
                "departure": "青岛"
              },
              {
                "arrival": "大连、长春、临沂、青岛、太原",
                "departure": "厦门"
              },
              {
                "arrival": "济南",
                "departure": "贵阳"
              },
              {
                "arrival": "乌鲁木齐、长春",
                "departure": "太原"
              },
              {
                "arrival": "青岛",
                "departure": "广州"
              },
              {
                "arrival": "太原、厦门",
                "departure": "长春"
              },
              {
                "arrival": "厦门",
                "departure": "杭州"
              },
              {
                "arrival": "青岛、济南",
                "departure": "深圳"
              },
              {
                "arrival": "长春",
                "departure": "重庆"
              },
              {
                "arrival": "珠海",
                "departure": "大连"
              },
              {
                "arrival": "青岛",
                "departure": "海口"
              },
              {
                "arrival": "海口",
                "departure": "台州"
              },
              {
                "arrival": "厦门",
                "departure": "银川"
              },
              {
                "arrival": "厦门",
                "departure": "临沂"
              },
              {
                "arrival": "济南",
                "departure": "南宁"
              },
              {
                "arrival": "济南",
                "departure": "揭阳"
              },
              {
                "arrival": "郑州",
                "departure": "三亚"
              },
              {
                "arrival": "北京",
                "departure": "珠海"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "KWL",
              "TAO",
              "TNA",
              "URC",
              "CGQ",
              "ZUH",
              "SZX",
              "CAN",
              "SHE",
              "SYX",
              "HAK",
              "HET",
              "XMN",
              "KWE",
              "HRB"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 70000,
            "finalPriceText": "700",
            "hotRoutes": "热门航线：乌鲁木齐、沈阳、厦门等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5784158230045&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "KWL": [
                  "YNT"
                ],
                "TAO": [
                  "SZX",
                  "CAN",
                  "KWL",
                  "URC",
                  "SHA"
                ],
                "TNA": [
                  "SZX",
                  "URC",
                  "KWE"
                ],
                "URC": [
                  "TAO"
                ],
                "CGQ": [
                  "XMN"
                ],
                "ZUH": [
                  "DLC"
                ],
                "SZX": [
                  "TNA"
                ],
                "CAN": [
                  "YNT"
                ],
                "SHE": [
                  "URC"
                ],
                "SYX": [
                  "TAO",
                  "TNA"
                ],
                "HAK": [
                  "SHE"
                ],
                "HET": [
                  "URC"
                ],
                "XMN": [
                  "BAV",
                  "HGH",
                  "HRB",
                  "CGQ"
                ],
                "KWE": [
                  "SHE"
                ],
                "HRB": [
                  "NNG",
                  "XMN",
                  "CKG"
                ]
              },
              "availableDepGroupByArr": {
                "KWL": [
                  "TAO"
                ],
                "TAO": [
                  "SYX",
                  "URC"
                ],
                "NNG": [
                  "HRB"
                ],
                "TNA": [
                  "SZX",
                  "SYX"
                ],
                "URC": [
                  "SHE",
                  "TAO",
                  "TNA",
                  "HET"
                ],
                "HGH": [
                  "XMN"
                ],
                "CGQ": [
                  "XMN"
                ],
                "SHA": [
                  "TAO"
                ],
                "SZX": [
                  "TAO",
                  "TNA"
                ],
                "CAN": [
                  "TAO"
                ],
                "SHE": [
                  "HAK",
                  "KWE"
                ],
                "BAV": [
                  "XMN"
                ],
                "KWE": [
                  "TNA"
                ],
                "XMN": [
                  "HRB",
                  "CGQ"
                ],
                "HRB": [
                  "XMN"
                ],
                "DLC": [
                  "ZUH"
                ],
                "CKG": [
                  "HRB"
                ],
                "YNT": [
                  "CAN",
                  "KWL"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "经济舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次经济舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01JomGj81r2UI897hsr_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DJoTOJ1r2UI7fso1k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZPVGwt1r2UI6NNYQ0_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01X0hfA71r2UI78Ybag_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014N1Nfl1r2UI2vgbp8_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN017LyniK1r2UI4wgQGC_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Q6p1Xd1r2UI3rQlEm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01aPKXYz1r2UI3rRtwZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN016bCzDS1r2UI6NMgNZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01zNstcl1r2UI4wfopq_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01as3GZ51r2UI5O7HKm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN012iL6D91r2UI3rRpmI_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01RinYHm1r2UHyazPpW_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01IOOhoH1r2UI2vcmni_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014i0GEr1r2UI4wgxW4_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Ou65b01r2UI6NMY3j_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN017ocXD31r2UI4wfU4w_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "经济舱700元套餐",
            "packagePid": "5919063",
            "packageVId": "41363011",
            "price": 70000,
            "priceText": "700",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5784158230045,
            "skuPvId": "5919063:3284565",
            "skuRoutes": [
              {
                "arrival": "上海、乌鲁木齐、广州、桂林、深圳",
                "departure": "青岛"
              },
              {
                "arrival": "贵阳、深圳、乌鲁木齐",
                "departure": "济南"
              },
              {
                "arrival": "包头、长春、杭州、哈尔滨",
                "departure": "厦门"
              },
              {
                "arrival": "重庆、南宁、厦门",
                "departure": "哈尔滨"
              },
              {
                "arrival": "济南、青岛",
                "departure": "三亚"
              },
              {
                "arrival": "青岛",
                "departure": "乌鲁木齐"
              },
              {
                "arrival": "烟台",
                "departure": "广州"
              },
              {
                "arrival": "厦门",
                "departure": "长春"
              },
              {
                "arrival": "沈阳",
                "departure": "海口"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "呼和浩特"
              },
              {
                "arrival": "沈阳",
                "departure": "贵阳"
              },
              {
                "arrival": "烟台",
                "departure": "桂林"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "沈阳"
              },
              {
                "arrival": "济南",
                "departure": "深圳"
              },
              {
                "arrival": "大连",
                "departure": "珠海"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "TAO",
              "TNA",
              "URC",
              "HGH",
              "CGO",
              "CGQ",
              "SHA",
              "SZX",
              "SHE",
              "KWE",
              "XMN",
              "XNN",
              "YNT"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 80000,
            "finalPriceText": "800",
            "hotRoutes": "热门航线：青岛、昆明、深圳等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5618351029303&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "TAO": [
                  "KWE"
                ],
                "TNA": [
                  "SYX",
                  "KHG"
                ],
                "URC": [
                  "HGH",
                  "FOC",
                  "NKG"
                ],
                "HGH": [
                  "URC"
                ],
                "CGO": [
                  "HAK",
                  "KRL",
                  "URC"
                ],
                "CGQ": [
                  "CKG"
                ],
                "SHA": [
                  "TAO"
                ],
                "SZX": [
                  "YNT"
                ],
                "SHE": [
                  "KMG"
                ],
                "KWE": [
                  "CGQ"
                ],
                "XMN": [
                  "INC"
                ],
                "XNN": [
                  "TAO"
                ],
                "YNT": [
                  "SZX",
                  "KWL"
                ]
              },
              "availableDepGroupByArr": {
                "KWL": [
                  "YNT"
                ],
                "TAO": [
                  "XNN",
                  "SHA"
                ],
                "KRL": [
                  "CGO"
                ],
                "KMG": [
                  "SHE"
                ],
                "URC": [
                  "HGH",
                  "CGO"
                ],
                "HGH": [
                  "URC"
                ],
                "NKG": [
                  "URC"
                ],
                "KHG": [
                  "TNA"
                ],
                "CGQ": [
                  "KWE"
                ],
                "SZX": [
                  "YNT"
                ],
                "HAK": [
                  "CGO"
                ],
                "SYX": [
                  "TNA"
                ],
                "KWE": [
                  "TAO"
                ],
                "FOC": [
                  "URC"
                ],
                "CKG": [
                  "CGQ"
                ],
                "YNT": [
                  "SZX"
                ],
                "INC": [
                  "XMN"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "经济舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次经济舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01JomGj81r2UI897hsr_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DJoTOJ1r2UI7fso1k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZPVGwt1r2UI6NNYQ0_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01X0hfA71r2UI78Ybag_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014N1Nfl1r2UI2vgbp8_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN017LyniK1r2UI4wgQGC_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Q6p1Xd1r2UI3rQlEm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01aPKXYz1r2UI3rRtwZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN016bCzDS1r2UI6NMgNZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01zNstcl1r2UI4wfopq_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01as3GZ51r2UI5O7HKm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN012iL6D91r2UI3rRpmI_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01RinYHm1r2UHyazPpW_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01IOOhoH1r2UI2vcmni_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014i0GEr1r2UI4wgxW4_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Ou65b01r2UI6NMY3j_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN017ocXD31r2UI4wfU4w_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "经济舱800元套餐",
            "packagePid": "5919063",
            "packageVId": "41363012",
            "price": 80000,
            "priceText": "800",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5618351029303,
            "skuPvId": "5919063:3284566",
            "skuRoutes": [
              {
                "arrival": "海口、库尔勒、乌鲁木齐",
                "departure": "郑州"
              },
              {
                "arrival": "福州、杭州、南京",
                "departure": "乌鲁木齐"
              },
              {
                "arrival": "喀什、三亚",
                "departure": "济南"
              },
              {
                "arrival": "桂林、深圳",
                "departure": "烟台"
              },
              {
                "arrival": "重庆",
                "departure": "长春"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "杭州"
              },
              {
                "arrival": "长春",
                "departure": "贵阳"
              },
              {
                "arrival": "青岛",
                "departure": "上海"
              },
              {
                "arrival": "昆明",
                "departure": "沈阳"
              },
              {
                "arrival": "烟台",
                "departure": "深圳"
              },
              {
                "arrival": "贵阳",
                "departure": "青岛"
              },
              {
                "arrival": "银川",
                "departure": "厦门"
              },
              {
                "arrival": "青岛",
                "departure": "西宁"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "TAO",
              "CTU",
              "TNA",
              "HFE",
              "NNG",
              "URC",
              "BJS",
              "KHG",
              "SHA",
              "JJN",
              "KHN",
              "NDG",
              "SHE",
              "MDG",
              "KWE",
              "XNN",
              "LHW",
              "HYN",
              "SWA",
              "CKG",
              "YNT",
              "KWL",
              "HSN",
              "WNZ",
              "KMG",
              "TYN",
              "HGH",
              "NGB",
              "CSX",
              "CGO",
              "SIA",
              "MIG",
              "WUH",
              "NKG",
              "AKU",
              "CGQ",
              "ZUH",
              "TSN",
              "CAN",
              "HAK",
              "JMU",
              "HET",
              "BAV",
              "XMN",
              "HRB",
              "DLC",
              "WEH",
              "INC"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 80000,
            "finalPriceText": "800",
            "hotRoutes": "热门航线：厦门、大连、济南等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5784145194606&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "TAO": [
                  "KMG",
                  "CTU",
                  "HFE",
                  "HGH",
                  "NGB",
                  "TYN",
                  "CSX",
                  "SIA",
                  "BJS",
                  "WUH",
                  "NKG",
                  "CGQ",
                  "NDG",
                  "SHE",
                  "JMU",
                  "YNJ",
                  "HET",
                  "KWE",
                  "HRB",
                  "DLC",
                  "LHW",
                  "CKG",
                  "INC"
                ],
                "CTU": [
                  "TAO",
                  "XMN"
                ],
                "TNA": [
                  "CTU",
                  "CSX",
                  "MIG",
                  "WUH",
                  "CGQ",
                  "SHA",
                  "JJN",
                  "KHN",
                  "HET",
                  "XMN",
                  "KWE",
                  "HRB",
                  "DLC",
                  "LHW",
                  "WEH",
                  "CKG",
                  "INC"
                ],
                "HFE": [
                  "KWL",
                  "TAO",
                  "CKG",
                  "YNT"
                ],
                "NNG": [
                  "WUH"
                ],
                "URC": [
                  "LHW",
                  "KHG",
                  "INC"
                ],
                "BJS": [
                  "TAO",
                  "ZUH"
                ],
                "KHG": [
                  "URC"
                ],
                "SHA": [
                  "TNA",
                  "XMN",
                  "YNT"
                ],
                "JJN": [
                  "HSN"
                ],
                "KHN": [
                  "HAK",
                  "TNA"
                ],
                "NDG": [
                  "DLC"
                ],
                "SHE": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "TYN",
                  "CGO",
                  "XUZ",
                  "YNT"
                ],
                "MDG": [
                  "DLC"
                ],
                "KWE": [
                  "TNA",
                  "XMN",
                  "TYN",
                  "CGO",
                  "SIA"
                ],
                "XNN": [
                  "INC"
                ],
                "LHW": [
                  "TAO",
                  "TNA",
                  "URC"
                ],
                "HYN": [
                  "TNA"
                ],
                "SWA": [
                  "CSX"
                ],
                "CKG": [
                  "TAO",
                  "TNA",
                  "HFE",
                  "XMN",
                  "YNT"
                ],
                "YNT": [
                  "JMU",
                  "XMN",
                  "BJS",
                  "NKG",
                  "WUH",
                  "CGQ",
                  "SHA",
                  "CKG"
                ],
                "KWL": [
                  "HAK",
                  "TNA",
                  "XMN",
                  "CGO",
                  "NKG"
                ],
                "HSN": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "JJN"
                ],
                "WNZ": [
                  "TNA",
                  "ZUH"
                ],
                "KMG": [
                  "TAO",
                  "TNA",
                  "NKG"
                ],
                "TYN": [
                  "SHE",
                  "BAV",
                  "XMN",
                  "KWE"
                ],
                "HGH": [
                  "TAO",
                  "XMN",
                  "DLC"
                ],
                "NGB": [
                  "TAO",
                  "CGO"
                ],
                "CSX": [
                  "TAO",
                  "HAK",
                  "TNA",
                  "XMN",
                  "SWA"
                ],
                "CGO": [
                  "SHE",
                  "KWL",
                  "TAO",
                  "XMN",
                  "KWE",
                  "NGB",
                  "INC"
                ],
                "SIA": [
                  "TAO",
                  "TNA",
                  "KWE",
                  "XMN"
                ],
                "MIG": [
                  "TNA"
                ],
                "WUH": [
                  "TAO",
                  "TNA",
                  "NNG",
                  "XMN",
                  "HUZ",
                  "YNT"
                ],
                "NKG": [
                  "TAO",
                  "HET",
                  "YNT"
                ],
                "AKU": [
                  "SIA"
                ],
                "CGQ": [
                  "TAO",
                  "TNA",
                  "YNT"
                ],
                "ZUH": [
                  "HAK",
                  "TNA"
                ],
                "TSN": [
                  "HET",
                  "HRB"
                ],
                "CAN": [
                  "TNA"
                ],
                "HAK": [
                  "KHN",
                  "KWL",
                  "SHE",
                  "TAO",
                  "TNA",
                  "NGB",
                  "CSX",
                  "CGO",
                  "HYN",
                  "ZUH"
                ],
                "JMU": [
                  "TAO",
                  "YNT"
                ],
                "HET": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "NKG",
                  "CKG",
                  "TSN"
                ],
                "BAV": [
                  "TYN"
                ],
                "XMN": [
                  "HSN",
                  "TAO",
                  "TNA",
                  "HGH",
                  "CSX",
                  "SIA",
                  "WUH",
                  "SHA",
                  "SHE",
                  "HET",
                  "KWE",
                  "DLC",
                  "CKG"
                ],
                "HRB": [
                  "TAO",
                  "TNA",
                  "TSN"
                ],
                "DLC": [
                  "JSJ",
                  "NDG",
                  "MDG",
                  "TAO",
                  "TNA",
                  "XMN",
                  "CKG"
                ],
                "WEH": [
                  "TNA"
                ],
                "INC": [
                  "TAO",
                  "TNA",
                  "XNN",
                  "CGO"
                ]
              },
              "availableDepGroupByArr": {
                "TAO": [
                  "HSN",
                  "KMG",
                  "CTU",
                  "HFE",
                  "HGH",
                  "NGB",
                  "CSX",
                  "CGO",
                  "SIA",
                  "BJS",
                  "WUH",
                  "NKG",
                  "CGQ",
                  "SHE",
                  "HAK",
                  "JMU",
                  "HET",
                  "XMN",
                  "HRB",
                  "DLC",
                  "LHW",
                  "CKG",
                  "INC"
                ],
                "CTU": [
                  "TAO",
                  "TNA"
                ],
                "HFE": [
                  "TAO",
                  "CKG"
                ],
                "TNA": [
                  "SHA",
                  "KHN",
                  "SHE",
                  "KWE",
                  "LHW",
                  "HYN",
                  "CKG",
                  "KWL",
                  "HSN",
                  "WNZ",
                  "KMG",
                  "CSX",
                  "SIA",
                  "MIG",
                  "WUH",
                  "CGQ",
                  "ZUH",
                  "CAN",
                  "HAK",
                  "HET",
                  "XMN",
                  "HRB",
                  "DLC",
                  "WEH",
                  "INC"
                ],
                "NNG": [
                  "WUH"
                ],
                "BJS": [
                  "TAO",
                  "YNT"
                ],
                "KHG": [
                  "URC"
                ],
                "SHA": [
                  "TNA",
                  "XMN",
                  "YNT"
                ],
                "JJN": [
                  "HSN",
                  "TNA"
                ],
                "KHN": [
                  "HAK",
                  "TNA"
                ],
                "SHE": [
                  "TAO",
                  "HAK",
                  "XMN",
                  "TYN",
                  "CGO"
                ],
                "MDG": [
                  "DLC"
                ],
                "XNN": [
                  "INC"
                ],
                "JSJ": [
                  "DLC"
                ],
                "HSN": [
                  "XMN",
                  "JJN"
                ],
                "KMG": [
                  "TAO"
                ],
                "HGH": [
                  "TAO",
                  "XMN"
                ],
                "NGB": [
                  "TAO",
                  "HAK",
                  "CGO"
                ],
                "SIA": [
                  "TAO",
                  "XMN",
                  "KWE",
                  "AKU"
                ],
                "MIG": [
                  "TNA"
                ],
                "WUH": [
                  "TAO",
                  "TNA",
                  "NNG",
                  "XMN",
                  "YNT"
                ],
                "NKG": [
                  "KWL",
                  "TAO",
                  "KMG",
                  "HET",
                  "YNT"
                ],
                "TSN": [
                  "HET",
                  "HRB"
                ],
                "DLC": [
                  "NDG",
                  "MDG",
                  "TAO",
                  "TNA",
                  "XMN",
                  "HGH"
                ],
                "WEH": [
                  "TNA"
                ],
                "INC": [
                  "TAO",
                  "TNA",
                  "URC",
                  "XNN",
                  "CGO"
                ],
                "URC": [
                  "LHW",
                  "KHG"
                ],
                "NDG": [
                  "TAO",
                  "DLC"
                ],
                "YNJ": [
                  "TAO"
                ],
                "KWE": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "TYN",
                  "CGO",
                  "SIA"
                ],
                "LHW": [
                  "TAO",
                  "TNA",
                  "URC"
                ],
                "HYN": [
                  "HAK"
                ],
                "SWA": [
                  "CSX"
                ],
                "CKG": [
                  "TAO",
                  "TNA",
                  "HET",
                  "HFE",
                  "XMN",
                  "DLC",
                  "YNT"
                ],
                "YNT": [
                  "SHE",
                  "JMU",
                  "HFE",
                  "WUH",
                  "NKG",
                  "CGQ",
                  "SHA",
                  "CKG"
                ],
                "KWL": [
                  "HAK",
                  "HFE",
                  "CGO"
                ],
                "TYN": [
                  "SHE",
                  "TAO",
                  "BAV",
                  "KWE"
                ],
                "CSX": [
                  "TAO",
                  "HAK",
                  "TNA",
                  "XMN",
                  "SWA"
                ],
                "CGO": [
                  "SHE",
                  "KWL",
                  "HAK",
                  "KWE",
                  "NGB",
                  "INC"
                ],
                "CGQ": [
                  "TAO",
                  "TNA",
                  "YNT"
                ],
                "HUZ": [
                  "WUH"
                ],
                "ZUH": [
                  "WNZ",
                  "HAK",
                  "BJS"
                ],
                "JMU": [
                  "TAO",
                  "YNT"
                ],
                "HAK": [
                  "KHN",
                  "KWL",
                  "CSX",
                  "ZUH"
                ],
                "HET": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "NKG",
                  "TSN"
                ],
                "BAV": [
                  "TYN"
                ],
                "XMN": [
                  "KWL",
                  "HSN",
                  "CTU",
                  "TNA",
                  "TYN",
                  "HGH",
                  "CSX",
                  "CGO",
                  "SIA",
                  "WUH",
                  "SHA",
                  "SHE",
                  "HET",
                  "KWE",
                  "DLC",
                  "CKG",
                  "YNT"
                ],
                "HRB": [
                  "TAO",
                  "TNA",
                  "TSN"
                ],
                "XUZ": [
                  "SHE"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "公务舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次公务舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01JomGj81r2UI897hsr_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DJoTOJ1r2UI7fso1k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZPVGwt1r2UI6NNYQ0_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01X0hfA71r2UI78Ybag_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014N1Nfl1r2UI2vgbp8_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN017LyniK1r2UI4wgQGC_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Q6p1Xd1r2UI3rQlEm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01aPKXYz1r2UI3rRtwZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN016bCzDS1r2UI6NMgNZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01zNstcl1r2UI4wfopq_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01as3GZ51r2UI5O7HKm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN012iL6D91r2UI3rRpmI_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01RinYHm1r2UHyazPpW_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01IOOhoH1r2UI2vcmni_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014i0GEr1r2UI4wgxW4_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Ou65b01r2UI6NMY3j_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN017ocXD31r2UI4wfU4w_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "公务舱800元套餐",
            "packagePid": "5919063",
            "packageVId": "41363015",
            "price": 80000,
            "priceText": "800",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5784145194606,
            "skuPvId": "5919063:79046194",
            "skuRoutes": [
              {
                "arrival": "重庆、大连、杭州、长春、哈尔滨、沈阳、西安、昆明、贵阳、长沙、成都、武汉、呼和浩特、合肥、银川、佳木斯、兰州、齐齐哈尔、宁波、南京、北京、太原、延吉",
                "departure": "青岛"
              },
              {
                "arrival": "大连、哈尔滨、武汉、长春、长沙、重庆、银川、兰州、上海、厦门、呼和浩特、泉州、南昌、贵阳、绵阳、成都、威海",
                "departure": "济南"
              },
              {
                "arrival": "沈阳、青岛、重庆、舟山、武汉、西安、长沙、大连、呼和浩特、杭州、贵阳、上海、济南",
                "departure": "厦门"
              },
              {
                "arrival": "青岛、济南、牡丹江、重庆、建三江、齐齐哈尔、厦门",
                "departure": "大连"
              },
              {
                "arrival": "青岛、济南、厦门、合肥、烟台",
                "departure": "重庆"
              },
              {
                "arrival": "青岛、厦门、郑州、济南、烟台、太原、徐州",
                "departure": "沈阳"
              },
              {
                "arrival": "济南、青岛、厦门、烟台、惠州、南宁",
                "departure": "武汉"
              },
              {
                "arrival": "济南、郑州、长沙、台州、南昌、桂林、宁波、沈阳、青岛、珠海",
                "departure": "海口"
              },
              {
                "arrival": "南京、北京、上海、武汉、长春、重庆、佳木斯、厦门",
                "departure": "烟台"
              },
              {
                "arrival": "沈阳、青岛、厦门、银川、贵阳、桂林、宁波",
                "departure": "郑州"
              },
              {
                "arrival": "青岛、济南、海口、揭阳、厦门",
                "departure": "长沙"
              },
              {
                "arrival": "青岛、济南、天津",
                "departure": "哈尔滨"
              },
              {
                "arrival": "济南、厦门、重庆、南京、青岛、天津",
                "departure": "呼和浩特"
              },
              {
                "arrival": "济南、青岛、贵阳、厦门",
                "departure": "西安"
              },
              {
                "arrival": "济南、厦门、郑州、太原、西安",
                "departure": "贵阳"
              },
              {
                "arrival": "青岛、济南、烟台",
                "departure": "长春"
              },
              {
                "arrival": "南京、郑州、海口、济南、厦门",
                "departure": "桂林"
              },
              {
                "arrival": "厦门、包头、贵阳、沈阳",
                "departure": "太原"
              },
              {
                "arrival": "青岛、重庆、桂林、烟台",
                "departure": "合肥"
              },
              {
                "arrival": "青岛、大连、厦门",
                "departure": "杭州"
              },
              {
                "arrival": "厦门、泉州、青岛、济南",
                "departure": "舟山"
              },
              {
                "arrival": "济南、郑州、青岛、西宁",
                "departure": "银川"
              },
              {
                "arrival": "南京、青岛、济南",
                "departure": "昆明"
              },
              {
                "arrival": "济南、乌鲁木齐、青岛",
                "departure": "兰州"
              },
              {
                "arrival": "珠海、青岛",
                "departure": "北京"
              },
              {
                "arrival": "济南、烟台、厦门",
                "departure": "上海"
              },
              {
                "arrival": "青岛、厦门",
                "departure": "成都"
              },
              {
                "arrival": "喀什、银川、兰州",
                "departure": "乌鲁木齐"
              },
              {
                "arrival": "呼和浩特、青岛、烟台",
                "departure": "南京"
              },
              {
                "arrival": "青岛、烟台",
                "departure": "佳木斯"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "喀什"
              },
              {
                "arrival": "海口、济南",
                "departure": "南昌"
              },
              {
                "arrival": "大连",
                "departure": "牡丹江"
              },
              {
                "arrival": "郑州、青岛",
                "departure": "宁波"
              },
              {
                "arrival": "呼和浩特、哈尔滨",
                "departure": "天津"
              },
              {
                "arrival": "济南、珠海",
                "departure": "温州"
              },
              {
                "arrival": "海口、济南",
                "departure": "珠海"
              },
              {
                "arrival": "西安",
                "departure": "阿克苏"
              },
              {
                "arrival": "太原",
                "departure": "包头"
              },
              {
                "arrival": "济南",
                "departure": "广州"
              },
              {
                "arrival": "济南",
                "departure": "台州"
              },
              {
                "arrival": "舟山",
                "departure": "泉州"
              },
              {
                "arrival": "济南",
                "departure": "绵阳"
              },
              {
                "arrival": "大连",
                "departure": "齐齐哈尔"
              },
              {
                "arrival": "武汉",
                "departure": "南宁"
              },
              {
                "arrival": "长沙",
                "departure": "揭阳"
              },
              {
                "arrival": "济南",
                "departure": "威海"
              },
              {
                "arrival": "银川",
                "departure": "西宁"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "TAO",
              "TNA",
              "URC",
              "NGB",
              "KHG",
              "CGQ",
              "ZUH",
              "SHA",
              "SZX",
              "KWE",
              "HRB",
              "FOC",
              "WEH"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 90000,
            "finalPriceText": "900",
            "hotRoutes": "热门航线：海口、济南、威海等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5618344709790&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "TAO": [
                  "SHA"
                ],
                "TNA": [
                  "KRL",
                  "KHG"
                ],
                "URC": [
                  "XMN"
                ],
                "NGB": [
                  "HAK"
                ],
                "KHG": [
                  "TNA"
                ],
                "CGQ": [
                  "KWE"
                ],
                "ZUH": [
                  "HRB"
                ],
                "SHA": [
                  "TAO"
                ],
                "SZX": [
                  "WEH"
                ],
                "KWE": [
                  "SHE"
                ],
                "HRB": [
                  "ZUH"
                ],
                "FOC": [
                  "URC"
                ],
                "WEH": [
                  "SZX"
                ]
              },
              "availableDepGroupByArr": {
                "TAO": [
                  "SHA"
                ],
                "KRL": [
                  "TNA"
                ],
                "TNA": [
                  "KHG"
                ],
                "URC": [
                  "FOC"
                ],
                "KHG": [
                  "TNA"
                ],
                "ZUH": [
                  "HRB"
                ],
                "SHA": [
                  "TAO"
                ],
                "SZX": [
                  "WEH"
                ],
                "SHE": [
                  "KWE"
                ],
                "HAK": [
                  "NGB"
                ],
                "KWE": [
                  "CGQ"
                ],
                "XMN": [
                  "URC"
                ],
                "HRB": [
                  "ZUH"
                ],
                "WEH": [
                  "SZX"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "经济舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次经济舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01JomGj81r2UI897hsr_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DJoTOJ1r2UI7fso1k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZPVGwt1r2UI6NNYQ0_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01X0hfA71r2UI78Ybag_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014N1Nfl1r2UI2vgbp8_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN017LyniK1r2UI4wgQGC_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Q6p1Xd1r2UI3rQlEm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01aPKXYz1r2UI3rRtwZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN016bCzDS1r2UI6NMgNZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01zNstcl1r2UI4wfopq_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01as3GZ51r2UI5O7HKm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN012iL6D91r2UI3rRpmI_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01RinYHm1r2UHyazPpW_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01IOOhoH1r2UI2vcmni_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014i0GEr1r2UI4wgxW4_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Ou65b01r2UI6NMY3j_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN017ocXD31r2UI4wfU4w_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "经济舱900元套餐",
            "packagePid": "5919063",
            "packageVId": "41363013",
            "price": 90000,
            "priceText": "900",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5618344709790,
            "skuPvId": "5919063:3284567",
            "skuRoutes": [
              {
                "arrival": "济南",
                "departure": "喀什"
              },
              {
                "arrival": "上海",
                "departure": "青岛"
              },
              {
                "arrival": "喀什、库尔勒",
                "departure": "济南"
              },
              {
                "arrival": "贵阳",
                "departure": "长春"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "福州"
              },
              {
                "arrival": "珠海",
                "departure": "哈尔滨"
              },
              {
                "arrival": "沈阳",
                "departure": "贵阳"
              },
              {
                "arrival": "海口",
                "departure": "宁波"
              },
              {
                "arrival": "青岛",
                "departure": "上海"
              },
              {
                "arrival": "威海",
                "departure": "深圳"
              },
              {
                "arrival": "厦门",
                "departure": "乌鲁木齐"
              },
              {
                "arrival": "深圳",
                "departure": "威海"
              },
              {
                "arrival": "哈尔滨",
                "departure": "珠海"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "SHE",
              "TNA",
              "XMN",
              "CGO",
              "AKU",
              "SHA"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 100000,
            "finalPriceText": "1000",
            "hotRoutes": "热门航线：三亚、青岛、乌鲁木齐等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5618344709791&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "SHE": [
                  "HAK"
                ],
                "TNA": [
                  "SYX"
                ],
                "XMN": [
                  "URC"
                ],
                "CGO": [
                  "SYX"
                ],
                "AKU": [
                  "TAO"
                ],
                "SHA": [
                  "TAO"
                ]
              },
              "availableDepGroupByArr": {
                "TAO": [
                  "AKU",
                  "SHA"
                ],
                "SYX": [
                  "TNA",
                  "CGO"
                ],
                "HAK": [
                  "SHE"
                ],
                "URC": [
                  "XMN"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "经济舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次经济舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01JomGj81r2UI897hsr_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DJoTOJ1r2UI7fso1k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZPVGwt1r2UI6NNYQ0_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01X0hfA71r2UI78Ybag_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014N1Nfl1r2UI2vgbp8_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN017LyniK1r2UI4wgQGC_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Q6p1Xd1r2UI3rQlEm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01aPKXYz1r2UI3rRtwZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN016bCzDS1r2UI6NMgNZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01zNstcl1r2UI4wfopq_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01as3GZ51r2UI5O7HKm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN012iL6D91r2UI3rRpmI_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01RinYHm1r2UHyazPpW_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01IOOhoH1r2UI2vcmni_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014i0GEr1r2UI4wgxW4_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Ou65b01r2UI6NMY3j_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN017ocXD31r2UI4wfU4w_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "经济舱1000元套餐",
            "packagePid": "5919063",
            "packageVId": "41363014",
            "price": 100000,
            "priceText": "1000",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5618344709791,
            "skuPvId": "5919063:59904168",
            "skuRoutes": [
              {
                "arrival": "青岛",
                "departure": "阿克苏"
              },
              {
                "arrival": "三亚",
                "departure": "郑州"
              },
              {
                "arrival": "青岛",
                "departure": "上海"
              },
              {
                "arrival": "海口",
                "departure": "沈阳"
              },
              {
                "arrival": "三亚",
                "departure": "济南"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "厦门"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "TAO",
              "KRL",
              "CTU",
              "TNA",
              "NNG",
              "URC",
              "BJS",
              "JJN",
              "NDG",
              "MDG",
              "SHE",
              "YNJ",
              "KWE",
              "XNN",
              "FOC",
              "HYN",
              "SWA",
              "CKG",
              "YNT",
              "KWL",
              "KMG",
              "TYN",
              "HGH",
              "SIA",
              "CGO",
              "CGQ",
              "ZUH",
              "TSN",
              "CAN",
              "SYX",
              "HAK",
              "XMN",
              "HRB",
              "DLC",
              "INC"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 100000,
            "finalPriceText": "1000",
            "hotRoutes": "热门航线：青岛、贵阳、太原等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5784158230046&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "TAO": [
                  "MDG",
                  "CAN",
                  "KWL",
                  "CTU",
                  "URC",
                  "XMN",
                  "XNN"
                ],
                "KRL": [
                  "CGO",
                  "SIA"
                ],
                "CTU": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "YNT"
                ],
                "TNA": [
                  "KMG",
                  "CTU",
                  "URC",
                  "XMN",
                  "KWE",
                  "LJG",
                  "DLC",
                  "FOC",
                  "CKG"
                ],
                "NNG": [
                  "TNA"
                ],
                "URC": [
                  "TNA",
                  "TYN",
                  "XMN",
                  "LHW",
                  "KHG",
                  "NKG"
                ],
                "BJS": [
                  "ZUH",
                  "YNT"
                ],
                "JJN": [
                  "TNA"
                ],
                "NDG": [
                  "TAO"
                ],
                "MDG": [
                  "TAO"
                ],
                "SHE": [
                  "KWE"
                ],
                "YNJ": [
                  "TAO"
                ],
                "KWE": [
                  "TNA"
                ],
                "XNN": [
                  "TAO"
                ],
                "FOC": [
                  "TNA"
                ],
                "HYN": [
                  "HAK"
                ],
                "SWA": [
                  "TNA"
                ],
                "CKG": [
                  "TNA",
                  "HET",
                  "TYN",
                  "DLC",
                  "CGQ",
                  "ZUH"
                ],
                "YNT": [
                  "CAN",
                  "KWL",
                  "CTU"
                ],
                "KWL": [
                  "YNT"
                ],
                "KMG": [
                  "NKG"
                ],
                "TYN": [
                  "TAO",
                  "URC",
                  "HGH",
                  "CGQ",
                  "CKG"
                ],
                "HGH": [
                  "TYN"
                ],
                "SIA": [
                  "TAO",
                  "KRL",
                  "XMN",
                  "AKU"
                ],
                "CGO": [
                  "HAK",
                  "KRL",
                  "URC"
                ],
                "CGQ": [
                  "TNA",
                  "XMN",
                  "TYN",
                  "CKG"
                ],
                "ZUH": [
                  "TNA",
                  "CKG"
                ],
                "TSN": [
                  "XMN",
                  "ZUH"
                ],
                "CAN": [
                  "TAO"
                ],
                "SYX": [
                  "TAO",
                  "TNA",
                  "CGO"
                ],
                "HAK": [
                  "TAO"
                ],
                "XMN": [
                  "TAO",
                  "CTU",
                  "TNA",
                  "HET",
                  "TYN",
                  "KWE",
                  "CGO",
                  "NKG",
                  "TSN"
                ],
                "HRB": [
                  "CKG"
                ],
                "DLC": [
                  "TNA",
                  "HGH",
                  "KWE",
                  "XMN",
                  "CKG"
                ],
                "INC": [
                  "URC",
                  "XMN"
                ]
              },
              "availableDepGroupByArr": {
                "TAO": [
                  "NDG",
                  "CAN",
                  "MDG",
                  "SYX",
                  "HAK",
                  "YNJ",
                  "CTU",
                  "XMN",
                  "TYN",
                  "XNN",
                  "SIA"
                ],
                "KRL": [
                  "SIA",
                  "CGO"
                ],
                "CTU": [
                  "TAO",
                  "TNA",
                  "XMN",
                  "YNT"
                ],
                "TNA": [
                  "CTU",
                  "NNG",
                  "URC",
                  "CGQ",
                  "ZUH",
                  "JJN",
                  "SYX",
                  "XMN",
                  "KWE",
                  "DLC",
                  "FOC",
                  "SWA",
                  "CKG"
                ],
                "URC": [
                  "TAO",
                  "TNA",
                  "TYN",
                  "CGO",
                  "INC"
                ],
                "LJG": [
                  "TNA"
                ],
                "KHG": [
                  "URC"
                ],
                "MDG": [
                  "TAO"
                ],
                "KWE": [
                  "SHE",
                  "TNA",
                  "XMN",
                  "DLC"
                ],
                "XNN": [
                  "TAO"
                ],
                "LHW": [
                  "URC"
                ],
                "FOC": [
                  "TNA"
                ],
                "CKG": [
                  "TNA",
                  "TYN",
                  "HRB",
                  "DLC",
                  "CGQ",
                  "ZUH"
                ],
                "YNT": [
                  "KWL",
                  "CTU",
                  "BJS"
                ],
                "KWL": [
                  "TAO",
                  "YNT"
                ],
                "KMG": [
                  "TNA"
                ],
                "TYN": [
                  "URC",
                  "XMN",
                  "HGH",
                  "CGQ",
                  "CKG"
                ],
                "HGH": [
                  "TYN",
                  "DLC"
                ],
                "CGO": [
                  "SYX",
                  "KRL",
                  "XMN"
                ],
                "SIA": [
                  "KRL"
                ],
                "NKG": [
                  "KMG",
                  "URC",
                  "XMN"
                ],
                "AKU": [
                  "SIA"
                ],
                "CGQ": [
                  "TYN",
                  "CKG"
                ],
                "ZUH": [
                  "BJS",
                  "CKG",
                  "TSN"
                ],
                "TSN": [
                  "XMN"
                ],
                "CAN": [
                  "TAO",
                  "YNT"
                ],
                "HAK": [
                  "CGO",
                  "HYN"
                ],
                "HET": [
                  "XMN",
                  "CKG"
                ],
                "XMN": [
                  "TAO",
                  "CTU",
                  "TNA",
                  "URC",
                  "DLC",
                  "SIA",
                  "CGQ",
                  "TSN",
                  "INC"
                ],
                "DLC": [
                  "TNA",
                  "CKG"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "公务舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次公务舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01JomGj81r2UI897hsr_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DJoTOJ1r2UI7fso1k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZPVGwt1r2UI6NNYQ0_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01X0hfA71r2UI78Ybag_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014N1Nfl1r2UI2vgbp8_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN017LyniK1r2UI4wgQGC_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Q6p1Xd1r2UI3rQlEm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01aPKXYz1r2UI3rRtwZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN016bCzDS1r2UI6NMgNZ_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01zNstcl1r2UI4wfopq_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01as3GZ51r2UI5O7HKm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN012iL6D91r2UI3rRpmI_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01RinYHm1r2UHyazPpW_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01IOOhoH1r2UI2vcmni_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN014i0GEr1r2UI4wgxW4_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01Ou65b01r2UI6NMY3j_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN017ocXD31r2UI4wfU4w_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "公务舱1000元套餐",
            "packagePid": "5919063",
            "packageVId": "41363016",
            "price": 100000,
            "priceText": "1000",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5784158230046,
            "skuPvId": "5919063:*********",
            "skuRoutes": [
              {
                "arrival": "乌鲁木齐、重庆、成都、厦门、贵阳、大连、福州、昆明、丽江",
                "departure": "济南"
              },
              {
                "arrival": "济南、郑州、太原、呼和浩特、贵阳、南京、青岛、成都、天津",
                "departure": "厦门"
              },
              {
                "arrival": "成都、厦门、牡丹江、乌鲁木齐、广州、桂林、西宁",
                "departure": "青岛"
              },
              {
                "arrival": "济南、青岛、厦门、烟台",
                "departure": "成都"
              },
              {
                "arrival": "济南、喀什、兰州、南京、太原、厦门",
                "departure": "乌鲁木齐"
              },
              {
                "arrival": "济南、长春、大连、呼和浩特、太原、珠海",
                "departure": "重庆"
              },
              {
                "arrival": "乌鲁木齐、长春、重庆、杭州、青岛",
                "departure": "太原"
              },
              {
                "arrival": "厦门、重庆、济南、太原",
                "departure": "长春"
              },
              {
                "arrival": "重庆、杭州、贵阳、济南、厦门",
                "departure": "大连"
              },
              {
                "arrival": "青岛、阿克苏、库尔勒、厦门",
                "departure": "西安"
              },
              {
                "arrival": "济南、郑州、青岛",
                "departure": "三亚"
              },
              {
                "arrival": "青岛",
                "departure": "广州"
              },
              {
                "arrival": "海口、库尔勒、乌鲁木齐",
                "departure": "郑州"
              },
              {
                "arrival": "广州、桂林、成都",
                "departure": "烟台"
              },
              {
                "arrival": "乌鲁木齐、厦门",
                "departure": "银川"
              },
              {
                "arrival": "郑州、西安",
                "departure": "库尔勒"
              },
              {
                "arrival": "济南",
                "departure": "贵阳"
              },
              {
                "arrival": "青岛",
                "departure": "牡丹江"
              },
              {
                "arrival": "烟台、珠海",
                "departure": "北京"
              },
              {
                "arrival": "贵阳",
                "departure": "沈阳"
              },
              {
                "arrival": "厦门、珠海",
                "departure": "天津"
              },
              {
                "arrival": "重庆、济南",
                "departure": "珠海"
              },
              {
                "arrival": "济南",
                "departure": "福州"
              },
              {
                "arrival": "青岛",
                "departure": "海口"
              },
              {
                "arrival": "太原",
                "departure": "杭州"
              },
              {
                "arrival": "重庆",
                "departure": "哈尔滨"
              },
              {
                "arrival": "海口",
                "departure": "台州"
              },
              {
                "arrival": "济南",
                "departure": "泉州"
              },
              {
                "arrival": "南京",
                "departure": "昆明"
              },
              {
                "arrival": "烟台",
                "departure": "桂林"
              },
              {
                "arrival": "青岛",
                "departure": "齐齐哈尔"
              },
              {
                "arrival": "济南",
                "departure": "南宁"
              },
              {
                "arrival": "济南",
                "departure": "揭阳"
              },
              {
                "arrival": "青岛",
                "departure": "西宁"
              },
              {
                "arrival": "青岛",
                "departure": "延吉"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "TAO",
              "TNA",
              "URC",
              "NGB",
              "BJS",
              "KHG",
              "ZUH",
              "SZX",
              "KHN",
              "SHE",
              "CAN",
              "XMN",
              "KWE",
              "HRB",
              "FOC",
              "CKG",
              "YNT"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 120000,
            "finalPriceText": "1200",
            "hotRoutes": "热门航线：南宁、沈阳、深圳等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5618351029304&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "TAO": [
                  "SZX",
                  "KWE"
                ],
                "TNA": [
                  "NNG",
                  "KWE",
                  "CKG"
                ],
                "URC": [
                  "TNA",
                  "TYN",
                  "FOC"
                ],
                "NGB": [
                  "HAK",
                  "URC"
                ],
                "BJS": [
                  "XMN",
                  "FOC"
                ],
                "KHG": [
                  "URC"
                ],
                "ZUH": [
                  "KHN"
                ],
                "SZX": [
                  "SHE",
                  "TAO"
                ],
                "KHN": [
                  "ZUH"
                ],
                "SHE": [
                  "SZX",
                  "HAK",
                  "URC",
                  "NKG"
                ],
                "CAN": [
                  "TNA"
                ],
                "XMN": [
                  "TAO",
                  "XMN",
                  "TYN",
                  "HRB",
                  "DLC",
                  "CGQ",
                  "YNT",
                  "INC"
                ],
                "KWE": [
                  "SHE"
                ],
                "HRB": [
                  "NNG",
                  "XMN",
                  "ZUH"
                ],
                "FOC": [
                  "URC",
                  "BJS"
                ],
                "CKG": [
                  "TNA"
                ],
                "YNT": [
                  "BJS"
                ]
              },
              "availableDepGroupByArr": {
                "TAO": [
                  "SZX",
                  "XMN"
                ],
                "NNG": [
                  "TNA",
                  "HRB"
                ],
                "TNA": [
                  "CAN",
                  "URC",
                  "CKG"
                ],
                "URC": [
                  "SHE",
                  "NGB",
                  "FOC",
                  "KHG"
                ],
                "TYN": [
                  "URC",
                  "XMN"
                ],
                "BJS": [
                  "FOC",
                  "YNT"
                ],
                "NKG": [
                  "SHE"
                ],
                "CGQ": [
                  "XMN"
                ],
                "ZUH": [
                  "KHN",
                  "HRB"
                ],
                "SZX": [
                  "SHE",
                  "TAO"
                ],
                "KHN": [
                  "ZUH"
                ],
                "SHE": [
                  "SZX",
                  "KWE"
                ],
                "HAK": [
                  "SHE",
                  "NGB"
                ],
                "XMN": [
                  "XMN",
                  "HRB",
                  "BJS"
                ],
                "KWE": [
                  "TAO",
                  "TNA"
                ],
                "HRB": [
                  "XMN"
                ],
                "DLC": [
                  "XMN"
                ],
                "FOC": [
                  "URC",
                  "BJS"
                ],
                "YNT": [
                  "XMN"
                ],
                "CKG": [
                  "TNA"
                ],
                "INC": [
                  "XMN"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "公务舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次公务舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN010Yktua1r2UI4xTEKv_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN01DZ0GKC1r2UI5hirgA_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN01xDPx241r2UI2wQrtl_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01JNI1SA1r2UI6QhaL3_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01TARbNh1r2UI7gbv6x_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01cRG9aA1r2UI6Qg6qm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN01lyNrH51r2UI57yMTj_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ubghBR1r2UI63G3SX_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01NhfeJm1r2UI581JV5_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN018uNQRN1r2UI75xLVN_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DpdzB41r2UHyboMxf_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01BuMSKt1r2UI6OCRMd_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN01JuiIss1r2UI63GWYL_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01YLgyhP1r2UI89vifG_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN01WTaLaP1r2UI6OCm9k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZZ7IH91r2UI79K0Is_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01Q1UnIM1r2UHybmUXR_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "公务舱1200元套餐",
            "packagePid": "5919063",
            "packageVId": "41363017",
            "price": 120000,
            "priceText": "1200",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5618351029304,
            "skuPvId": "5919063:*********",
            "skuRoutes": [
              {
                "arrival": "厦门、长春、大连、哈尔滨、银川、青岛、太原、烟台",
                "departure": "厦门"
              },
              {
                "arrival": "厦门、福州",
                "departure": "北京"
              },
              {
                "arrival": "北京、乌鲁木齐",
                "departure": "福州"
              },
              {
                "arrival": "海口、南京、深圳、乌鲁木齐",
                "departure": "沈阳"
              },
              {
                "arrival": "重庆、贵阳、南宁",
                "departure": "济南"
              },
              {
                "arrival": "南宁、厦门、珠海",
                "departure": "哈尔滨"
              },
              {
                "arrival": "福州、济南、太原",
                "departure": "乌鲁木齐"
              },
              {
                "arrival": "济南",
                "departure": "重庆"
              },
              {
                "arrival": "沈阳",
                "departure": "贵阳"
              },
              {
                "arrival": "海口、乌鲁木齐",
                "departure": "宁波"
              },
              {
                "arrival": "沈阳、青岛",
                "departure": "深圳"
              },
              {
                "arrival": "贵阳、深圳",
                "departure": "青岛"
              },
              {
                "arrival": "济南",
                "departure": "广州"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "喀什"
              },
              {
                "arrival": "珠海",
                "departure": "南昌"
              },
              {
                "arrival": "北京",
                "departure": "烟台"
              },
              {
                "arrival": "南昌",
                "departure": "珠海"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          },
          {
            "advantageTags": [],
            "availableDepartureCitys": [
              "TAO",
              "TNA",
              "URC",
              "HGH",
              "CGO",
              "KHG",
              "AKU",
              "CGQ",
              "ZUH",
              "SZX",
              "KHN",
              "CAN",
              "SHE",
              "KWE",
              "XMN",
              "DLC",
              "YNT"
            ],
            "buyButtonDesc": "立即购买",
            "buyButtonGray": false,
            "buyButtonSupport": true,
            "cartButtonDesc": "加入购物车",
            "cartButtonSupport": true,
            "couponFlag": false,
            "finalPrice": 140000,
            "finalPriceText": "1400",
            "hotRoutes": "热门航线：长春、济南、广州等多地",
            "moreHotZone": false,
            "packageExplainDesc": "可选航线和日期",
            "packageExplainLink": {
              "jumpH5Url": "https://market.m.taobao.com/app/trip/rx-travel-detail-pads/pages/flight-routes?type=flightroute&skuId=5785953619486&itemId=************",
              "jumpNative": false
            },
            "packageInfo": {
              "availableArrGroupByDep": {
                "TAO": [
                  "SZX",
                  "CAN"
                ],
                "TNA": [
                  "CAN",
                  "KHG",
                  "ZUH",
                  "SWA"
                ],
                "URC": [
                  "SHE",
                  "TAO",
                  "TNA",
                  "HGH"
                ],
                "HGH": [
                  "URC"
                ],
                "CGO": [
                  "SYX"
                ],
                "KHG": [
                  "TNA"
                ],
                "AKU": [
                  "TAO"
                ],
                "CGQ": [
                  "KWE",
                  "CKG"
                ],
                "ZUH": [
                  "DLC"
                ],
                "SZX": [
                  "TAO",
                  "YNT"
                ],
                "KHN": [
                  "TNA"
                ],
                "CAN": [
                  "TNA",
                  "YNT"
                ],
                "SHE": [
                  "KMG"
                ],
                "KWE": [
                  "CGQ"
                ],
                "XMN": [
                  "CGQ"
                ],
                "DLC": [
                  "ZUH"
                ],
                "YNT": [
                  "SZX"
                ]
              },
              "availableDepGroupByArr": {
                "TAO": [
                  "SZX",
                  "URC",
                  "AKU"
                ],
                "KMG": [
                  "SHE"
                ],
                "TNA": [
                  "KHN",
                  "CAN",
                  "URC",
                  "KHG"
                ],
                "URC": [
                  "HGH"
                ],
                "HGH": [
                  "URC"
                ],
                "KHG": [
                  "TNA"
                ],
                "ZUH": [
                  "TNA",
                  "DLC"
                ],
                "CGQ": [
                  "KWE",
                  "XMN"
                ],
                "SZX": [
                  "TAO",
                  "YNT"
                ],
                "CAN": [
                  "TAO",
                  "TNA"
                ],
                "SHE": [
                  "URC"
                ],
                "SYX": [
                  "CGO"
                ],
                "KWE": [
                  "CGQ"
                ],
                "DLC": [
                  "ZUH"
                ],
                "SWA": [
                  "TNA"
                ],
                "YNT": [
                  "SZX",
                  "CAN"
                ],
                "CKG": [
                  "CGQ"
                ]
              },
              "coreInfo": {
                "detailInfo": [
                  {
                    "mainInfo": "适用人群",
                    "subInfo": "限12周岁及以上成人、2至12岁（不含）儿童可用\n须在购买之日起1天内（含购买当天）添加乘机人信息"
                  },
                  {
                    "mainInfo": "预约期限",
                    "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                  },
                  {
                    "mainInfo": "航班日期",
                    "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                  }
                ],
                "summaryInfo": [
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01sMz4Oz1uIvLLy1TIz_!!6000000006015-2-tps-104-104.png",
                    "title": "单程"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i3/O1CN016R9Vyl1fhSJ8qJ974_!!6000000004038-2-tps-52-52.png",
                    "title": "单人可用"
                  },
                  {
                    "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01iGhCLq27CarQgSVlE_!!6000000007761-2-tps-52-52.png",
                    "title": "公务舱"
                  }
                ]
              },
              "packageCanBuy": true,
              "packageRuleInfo": {
                "packageEnjoyableRights": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01M2NyrL21ruuurtKMl_!!6000000007039-2-tps-120-120.png",
                        "mainInfo": "套餐权益",
                        "subInfo": "包含指定日期及航线1次公务舱单程出行机票。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "行李额",
                        "subInfo": "托运行李额度及手提行李规格以预约时页面实际展示为准"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "mainInfo": "航司里程积分",
                        "subInfo": "不累积航司里程、积分、航段等权益"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01ILQBkt1lOZgpLMGQh_!!6000000004809-2-tps-120-120.png",
                        "mainInfo": "飞猪里程",
                        "subInfo": "购买该产品不可累积飞猪里程"
                      }
                    ]
                  },
                  "tag": "enjoyableRights",
                  "title": "可享权益"
                },
                "packageGraphicDetail": {
                  "graphicHtml": "<img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN010Yktua1r2UI4xTEKv_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN01DZ0GKC1r2UI5hirgA_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN01xDPx241r2UI2wQrtl_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01JNI1SA1r2UI6QhaL3_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01TARbNh1r2UI7gbv6x_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i1/2927925573/O1CN01cRG9aA1r2UI6Qg6qm_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN01lyNrH51r2UI57yMTj_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ubghBR1r2UI63G3SX_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01NhfeJm1r2UI581JV5_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN018uNQRN1r2UI75xLVN_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01DpdzB41r2UHyboMxf_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01BuMSKt1r2UI6OCRMd_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN01JuiIss1r2UI63GWYL_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01YLgyhP1r2UI89vifG_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i3/2927925573/O1CN01WTaLaP1r2UI6OCm9k_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i4/2927925573/O1CN01ZZ7IH91r2UI79K0Is_!!2927925573.jpg\"></img><img src=\"https://img.alicdn.com/imgextra/i2/2927925573/O1CN01Q1UnIM1r2UHybmUXR_!!2927925573.jpg\"></img>"
                },
                "packagePassengerRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i4/O1CN01jW3Zy325MYFFH53EA_!!6000000007512-2-tps-56-56.png",
                        "mainInfo": "乘机人类型与数量",
                        "subInfo": "乘机人仅支持12周岁（含）及以上成人旅客和2周岁（含）-12周岁（不含）的儿童旅客，证件类型为中华人民共和国居民身份证、港澳台居民居住证，婴儿不适用，如需添加随行婴儿，请在成人旅客完成预约后、联系飞猪客服，婴儿票价以客服实时查询为准。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01A7lKKX1zqsmKuabGZ_!!6000000006766-2-tps-56-56.png",
                        "mainInfo": "乘机人证件类型",
                        "subInfo": "支持维护乘机人证件类型为：身份证、港澳居民居住证、台湾居民居住证、回乡证、台胞证、护照\n乘机人一经添加后不支持修改"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Qh5vkn1wGII83J87n_!!6000000006280-2-tps-56-56.png",
                        "mainInfo": "添加乘机人时间限制",
                        "subInfo": "须在购买时添加乘机人信息，否则将无法购买。"
                      }
                    ]
                  },
                  "tag": "passengerRule",
                  "title": "乘机人规则"
                },
                "packagePurchaseNotes": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01t0vxhm1aJ7BUmrrMr_!!6000000003308-2-tps-56-56.png",
                        "mainInfo": "购买条件",
                        "subInfo": "本产品仅限12周岁及以上的支付宝实名认证的用户购买\n特别说明： 1、旅客购买时需完成使用人乘机信息的录入，填写乘机人姓名、联系 方式、身份证号信息等。一经录入不予修改，不得更换乘机人，不得转 赠他人使用。 2、因市场调控原因，该产品价格可能存在高于实时查询机票价格的情 况，请理解并理性购买。 3、在购买和使用本产品权益过程中，如有通过不正当手段参与活动的 行为（包括但不限于如违规占座，作弊领取、恶意套现、倒买倒卖、虚 假交易等），一经发现，山航有权取消违规用户相关订单、获得的权益 及参与本活动的资格，必要时将依法追究违规用户的法律责任。 4、依据《山东航空股份有限公司旅客行李运输总条件》第1.1.3条内容： “本条件同样适用于免费和特种票价等特殊客票的运输。当免费和特种 票价等特殊客票的客票使用条件与本条件不一致时，该客票使用条件优 先于本条件。”本产品的使用规则与《山东航空股份有限公司旅客行李 运输总条件》及其附件不一致的，以本产品的使用规则为准。 5、航线编排调整属于民航正常业务范畴，若受此类情况影响导致产品 覆盖的部分航线延误、取消、停飞或时刻与航程更改，山航不提供额外 补偿。 6、山航有权根据产品服务的实际情况，在法律允许的范围内，对本产 品规则进行变动或调整，相关变动和调整会在山航官方渠道公布，并于 公布时即时生效请认真阅读上述规则，购买完成则视为同意山航本次活 动的所有规则；如对本次活动规则有异议，请谨慎购买。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN017gYVQB1vvgSzLRZlS_!!6000000006235-2-tps-56-56.png",
                        "mainInfo": "报销凭证规则",
                        "subInfo": "在产品兑换使用后，可在宝贝订单详情页申请购买机票卡的发票，仅支持开具电子发票；兑换机票额外支付的机票税费可在兑换的机票订单详情申请发票。"
                      }
                    ]
                  },
                  "tag": "packagePurchaseNotes",
                  "title": "购买须知"
                },
                "packageRefundAndChangeRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01TeIez21wE0J86Vh1s_!!6000000006275-2-tps-52-52.png",
                        "mainInfo": "商品退款规则",
                        "subInfo": "在产品预约有效期内无兑换客票记录或因非自愿退票归还全部次数，支持全额退款。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01Arp0lz1bORPCOfxfn_!!6000000003455-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i4/2927925573/O1CN01n5w3DX1r2UI5Nvw2x_!!2927925573.jpg",
                        "mainInfo": "机票退票规则",
                        "subInfo": "自愿退票：经济舱按照山航A舱现行国内航班管理规定，公务舱按照山航I舱现行国内航班管理规定，扣除手续费后余额自动退回支付账户，不返还可预约次数。\n\n非自愿退票：非自愿退票退回未使用航段税费，返还预约次数。其余按山航现行国内不正常航班票务管理规则办理。"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN017X9wR71cV8obvMq3m_!!6000000003605-2-tps-56-56.png",
                        "image": "https://img.alicdn.com/imgextra/i2/2927925573/O1CN01udUKaX1r2UI3rED0o_!!2927925573.jpg",
                        "mainInfo": "机票改签规则",
                        "subInfo": "自愿改签：需联系飞猪客服办理自愿改签，自愿改签如有差价需补齐。具体收费标准经济舱以山航A舱现行国内航班管理规定执行，公务舱以山航I舱现行国内航班管理规定执行。\n\n非自愿改签：按照山航现行国内不正常航班管理规定执行。无论何种原因导致航班不正常，旅客自行购买外航客票产生票差不予赔偿。"
                      }
                    ]
                  },
                  "tag": "packageRefundAndChangeRule",
                  "title": "退改规则"
                },
                "packageUseRule": {
                  "data": {
                    "tableRows": [
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i1/O1CN01T22hgG1eWdLYhzfUj_!!6000000003879-2-tps-120-120.png",
                        "mainInfo": "预约有效期",
                        "subInfo": "2024年10月14日 17:00-2024年12月28日 23:40\n须至少提前3天预约出票"
                      },
                      {
                        "icon": "https://gw.alicdn.com/imgextra/i2/O1CN01e83zFd25fmsWfQC6d_!!6000000007554-2-tps-60-56.png",
                        "mainInfo": "可预约的航班日期",
                        "subInfo": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
                      }
                    ]
                  },
                  "tag": "useRule",
                  "title": "使用规则"
                }
              },
              "showStatus": 1,
              "toastInfo": {},
              "travelDatesDesc": "去程：2024年10月27日-2024年12月31日\n可约航班以实际预约时为准"
            },
            "packageName": "公务舱1400元套餐",
            "packagePid": "5919063",
            "packageVId": "41363018",
            "price": 140000,
            "priceText": "1400",
            "productDescList": [],
            "roundTripType": 0,
            "showBuyButton": true,
            "skuId": 5785953619486,
            "skuPvId": "5919063:*********",
            "skuRoutes": [
              {
                "arrival": "珠海、广州、喀什、揭阳",
                "departure": "济南"
              },
              {
                "arrival": "青岛、济南、杭州、沈阳",
                "departure": "乌鲁木齐"
              },
              {
                "arrival": "济南、烟台",
                "departure": "广州"
              },
              {
                "arrival": "重庆、贵阳",
                "departure": "长春"
              },
              {
                "arrival": "济南",
                "departure": "喀什"
              },
              {
                "arrival": "青岛、烟台",
                "departure": "深圳"
              },
              {
                "arrival": "广州、深圳",
                "departure": "青岛"
              },
              {
                "arrival": "青岛",
                "departure": "阿克苏"
              },
              {
                "arrival": "三亚",
                "departure": "郑州"
              },
              {
                "arrival": "珠海",
                "departure": "大连"
              },
              {
                "arrival": "乌鲁木齐",
                "departure": "杭州"
              },
              {
                "arrival": "济南",
                "departure": "南昌"
              },
              {
                "arrival": "长春",
                "departure": "贵阳"
              },
              {
                "arrival": "昆明",
                "departure": "沈阳"
              },
              {
                "arrival": "长春",
                "departure": "厦门"
              },
              {
                "arrival": "深圳",
                "departure": "烟台"
              },
              {
                "arrival": "大连",
                "departure": "珠海"
              }
            ],
            "soldOut": false,
            "travelDates": [
              {
                "journeyName": "去程",
                "travelDate": [
                  "2024年10月27日-2024年12月31日"
                ]
              }
            ]
          }
        ],
        "title": "套餐类型",
        "useNewFlyjpbbShow": true,
        "useNewMode": true
      },
      "tag": "packageShelf"
    }, 
     "filterSkuByRoute": {
      "data": {
        "businessLine": 1,
        "supportItemDetailFilterSkuByRoutes": true
      },
      "tag": "filterSkuByRoute"
    },
''';
import 'dart:io';

import '../../../custom_widget/detail_arrow.dart';
import '../../../utils/TextConfig.dart';
import '../../../utils/common_util.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../custom_widget/block_title.dart';
import '../../../utils/common_config.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:ficonfont/ficonfont.dart';

import '../../custom_widget/common_tag/common_tag_model.dart';
import '../../custom_widget/common_tag/common_tag_widget.dart';
import '../../custom_widget/dialog_webview.dart';
import '../hotel_package_to_calendar/widget.dart';
import 'Hotel_package_manager/Hotel_package_model.dart';
import 'hotel_shelf_v2/notifier.dart';
import 'hotel_style_new_model.dart';

/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

class HotelStyleNewWidget extends StatefulWidget {
  HotelShelfV2ChangeNotifier changeNotifier;

  HotelStyleNewWidget(this.changeNotifier, {Key? key}) : super(key: key);

  @override
  State<HotelStyleNewWidget> createState() => _HotelStyleNewWidget();
}

class _HotelStyleNewWidget extends State<HotelStyleNewWidget> {
  @override
  Widget build(BuildContext context) {
    final HotelShelfV2ChangeNotifier changeNotifier = widget.changeNotifier;
    final HotelStyleNewModel? hotelStyleNewModel =
        changeNotifier.itemDetailModel.hotelStyleNewModel;
    if (hotelStyleNewModel == null) {
      return Container();
    }
    changeNotifier
        .ctrlExposure(context, 'hotel-shelf.default', <String, dynamic>{});
    return Container(
      margin: const EdgeInsets.only(bottom: paddingBottom),
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: <Widget>[
          //套餐日历交互
          HotelPackageToCalendarWidget(
              changeNotifier.itemDetailModel.hotelPackageToCalendarModel,
              changeNotifier),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Column(
              children: <Widget>[
                // 标题
                if (hotelStyleNewModel.title != null &&
                    hotelStyleNewModel.title!.isNotEmpty)
                  BlockTitle(hotelStyleNewModel.title ?? ''),
                if (hotelStyleNewModel.canExpand)
                  ValueListenableBuilder(
                      valueListenable: hotelStyleNewModel.isExpand,
                      builder:
                          (BuildContext context, bool isExpand, Widget? child) {
                        List<HotelPackageModel> packageInfos;
                        if (isExpand) {
                          packageInfos = hotelStyleNewModel.packageList!;
                        } else {
                          packageInfos = hotelStyleNewModel.filterPackageList!;
                        }

                        return buildPackageList(packageInfos, changeNotifier,
                            hotelStyleNewModel); // hotelPackageManager: hotelPackageManager,
                      })
                else
                  buildPackageList(hotelStyleNewModel.packageList!,
                      changeNotifier, hotelStyleNewModel),
                if (hotelStyleNewModel.canExpand)
                  ValueListenableBuilder(
                      valueListenable: hotelStyleNewModel.isExpand,
                      builder:
                          (BuildContext context, bool isExpand, Widget? child) {
                        return GestureDetector(
                          onTap: () {
                            hotelStyleNewModel.isExpand.value =
                                !hotelStyleNewModel.isExpand.value;
                          },
                          child: Container(
                            height: 55,
                            color: Color(0xFFFFFFFF),
                            alignment: Alignment.center,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Container(
                                  margin: EdgeInsets.only(right: 5),
                                  child: Text(
                                    hotelStyleNewModel.isExpand.value
                                        ? '收起'
                                        : '查看更多',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w700,
                                        fontSize: 13,
                                        color: Color(0xFF333333)),
                                  ),
                                ),
                                if (!hotelStyleNewModel.isExpand.value)
                                  topArrowBig,
                                if (hotelStyleNewModel.isExpand.value)
                                  bottomArrowBig,
                              ],
                            ),
                          ),
                        ); // hotelPackageManager: hotelPackageManager,
                      }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildPackageList(
      List<HotelPackageModel> packageInfos,
      HotelShelfV2ChangeNotifier changeNotifier,
      HotelStyleNewModel hotelStyleNewModel) {
    final List<Widget> listWidget = <Widget>[];
    packageInfos.asMap().forEach((int key, HotelPackageModel item) {
      listWidget.add(Column(
        children: <Widget>[
          linearLayoutWidget(context, item, changeNotifier, key),
          if (item != packageInfos[packageInfos.length - 1] ||
              hotelStyleNewModel.canExpand)
            Container(height: 0.5, color: Color(0xfff2f3f5)),
        ],
      ));
    });
    return Column(
      children: listWidget,
    );
  }

  Widget linearLayoutWidget(
      BuildContext context,
      HotelPackageModel packageInfos,
      HotelShelfV2ChangeNotifier changeNotifier,
      int index) {
    changeNotifier.ctrlExposure(
        context, 'hotel-shelf.title_$index', <String, dynamic>{
      'index': index,
      'sku_id': packageInfos.skuId,
      'pv_id': packageInfos.skuPvId
    });
    return Container(
      padding: EdgeInsets.only(top: index == 0 ? 0 : 15, bottom: 15),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          // 标题
          if (packageInfos.packageName != null)
            GestureDetector(
              onTap: () {
                if (packageInfos.packageExplainJumpUrl != null) {
                  changeNotifier.ctrlClicked(
                      context,
                      'hotel-shelf.title_$index',
                      'hotel-shelf', <String, dynamic>{
                    'index': index,
                    'sku_id': packageInfos.skuId,
                    'pv_id': packageInfos.skuPvId
                  });
                  final DialogWebView dialogWebView = DialogWebView(
                      context: context,
                      url:
                          '${packageInfos.packageExplainJumpUrl}&hideClose=true',
                      itemDetailEngine: changeNotifier.itemDetailEngine,
                      popConfig: H5PopConfig(
                          popTitle: '套餐说明',
                          popHeight:
                              MediaQuery.of(context).size.height * 3 / 4),
                      bottomWidget: popBottomWidget(
                          context, packageInfos, changeNotifier, index));
                  dialogWebView.showPop();
                }
              },
              child: Row(
                children: <Widget>[
                  Flexible(
                    fit: FlexFit.loose,
                    child: Text(
                      packageInfos.packageName ?? '',
                      style: TextStyle(
                        fontSize: 15.0,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF0F131A),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (packageInfos.packageExplainJumpUrl != null)
                    Container(
                        margin: EdgeInsets.only(left: 5), child: rightArrowBig),
                ],
              ),
            ),
          // RichText(
          //   // maxLines: 2,
          //   //   overflow: TextOverflow.ellipsis,
          //     text: TextSpan(children: <InlineSpan>[
          //       TextSpan(
          //           text:packageInfos.packageName ?? '',
          //           style: TextStyle(
          //               height: 1.4,
          //               fontWeight: FontWeight.w600,
          //               color: Color(0xff0F131A),
          //               fontSize: 15.00)
          //       ),
          //       // 箭头
          //       if (packageInfos.packageExplainJumpUrl != null)
          //         WidgetSpan(
          //           child:  Container(
          //             margin: const EdgeInsets.only(left: 4.5,bottom: 4),
          //             child: rightArrowBig,
          //           ),),
          //     ]))
          if (packageInfos.commonTagModels != null)
            Container(
              margin: EdgeInsets.only(top: 9),
              child: Row(
                children:
                    packageInfos.commonTagModels!.map((CommonTagModel tag) {
                  return Container(
                    margin: EdgeInsets.only(right: 4),
                    child: CommonTagWidget(tag),
                  );
                }).toList(),
              ),
            ),

          // if (packageInfos.hotelTags != null)
          //   Container(
          //     margin: EdgeInsets.only(top: 9),
          //     child: Row(
          //       children: packageInfos.hotelTags!.map((HotelTags tag) {
          //         return Text(
          //           ((tag.isFirst ?? false) ? '' : ' | ') + (tag.value ?? ''),
          //           style: TextStyle(
          //               color: stringToColor(tag.textColor), fontSize: 11),
          //         );
          //       }).toList(),
          //     ),
          //   ),

          if (packageInfos.xelements != null && packageInfos.showXelements)
            Container(
              margin: EdgeInsets.only(top: 9.0),
              child: Column(
                children: packageInfos.xelements!.map((Xelements element) {
                  return Container(
                    child: Stack(
                      children: <Widget>[
                        Row(
                          children: <Widget>[
                            //线条
                            if (element !=
                                packageInfos.xelements![
                                    packageInfos.xelements!.length - 1])
                              Container(
                                  width: 2,
                                  height: 20,
                                  margin: EdgeInsets.only(
                                      left: 6.5, right: 12.5, top: 8),
                                  color: Color(0x14ff7300)),
                            if (element ==
                                packageInfos.xelements![
                                    packageInfos.xelements!.length - 1])
                              Container(
                                  width: 2,
                                  height: 8,
                                  margin:
                                      EdgeInsets.only(left: 6.5, right: 12.5),
                                  color: Color(0x14ff7300)),
                            // 子标题
                            // if (element.subTitle != null)
                            //   Expanded(
                            //     child: Text(
                            //       element.subTitle ?? '',
                            //       maxLines: 1,
                            //       overflow: TextOverflow.ellipsis,
                            //       style: TextStyle(
                            //         fontSize: 11.0,
                            //         color: Color(0xFF919499),
                            //       ),
                            //     ),
                            //   ),
                          ],
                        ),
                        // 第一部分：图标、标题、箭头和标签
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            // 图标部分
                            if (element.icon != null)
                              Container(
                                width: 16.0,
                                height: 16.0,
                                margin: EdgeInsets.only(right: 6),
                                padding: EdgeInsets.only(
                                    bottom: Platform.isIOS ? 1 : 0),
                                decoration: BoxDecoration(
                                  color: Color(0xffffffff),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Color(0xFFff7300),
                                    width: 0.5,
                                  ),
                                ),
                                alignment: Alignment.center,
                                child: Text(
                                  element.icon!,
                                  style: TextStyle(
                                    color: Color(0xFFff7300),
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            // 标题
                            if (element.title != null)
                              Expanded(
                                child: Text(
                                  element.title!,
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    fontSize: 13.0,
                                    color: Color(0xFF5c5f66),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),

          //查看预约
          if (packageInfos.lookInventoryDesc != null)
            Container(
                margin: EdgeInsets.only(top: 10),
                child: Row(children: <Widget>[
                  GestureDetector(
                    onTap: () {
                      if (packageInfos.lookInventoryLink?.jumpH5Url != null) {
                        changeNotifier.ctrlClicked(
                            context,
                            'hotel-shelf.title_$index',
                            'action-shelf', <String, dynamic>{
                          'index': index,
                          'sku_id': packageInfos.skuId,
                          'pv_id': packageInfos.skuPvId
                        });
                        FliggyNavigatorApi.getInstance().push(context,
                            packageInfos.lookInventoryLink?.jumpH5Url ?? '');
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.only(right: 6),
                      child: Text(
                        packageInfos.lookInventoryDesc ?? '',
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 12.0,
                          color: Color(0xFF0F131A),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                  if (packageInfos.lookInventoryLink?.jumpH5Url != null)
                    rightArrowSmall,
                ])),
          Container(
            margin: EdgeInsets.only(
                top: packageInfos.lookInventoryDesc != null ? 0.0 : 9),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                // 价格
                if (packageInfos.finalPriceText != null)
                  priceWidget(packageInfos, changeNotifier),
                //间距
                if (((packageInfos.cartButtonSupport ?? false) &&
                        !(packageInfos.buyButtonGray ?? false)) ||
                    ((packageInfos.showBuyButton ?? false) &&
                        (packageInfos.buyButtonSupport ?? false)))
                  SizedBox(
                    width: 9,
                  ),
                // 购物车按钮
                if ((packageInfos.cartButtonSupport ?? false) &&
                    !(packageInfos.buyButtonGray ?? false))
                  GestureDetector(
                    onTap: () {
                      changeNotifier.ctrlClicked(
                          context,
                          'hotel-shelf.cart_$index',
                          'hotel-shelf', <String, dynamic>{
                        'index': index,
                        'sku_id': packageInfos.skuId,
                        'pv_id': packageInfos.skuPvId
                      });
                      changeNotifier.addCar(
                          context, packageInfos.skuId, packageInfos.skuPvId);
                    },
                    child: Container(
                      width: 41,
                      height: 41,
                      padding: EdgeInsets.symmetric(horizontal: 3),
                      margin: EdgeInsets.only(right: 6.0),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6.00),
                          border: Border.all(color: const Color(0xFFFF7300)),
                          color: Color(0xFFFFFFFF)),
                      // https://gw.alicdn.com/imgextra/i4/O1CN01ZWeUJA1xX4NeVDmbi_!!6000000006452-2-tps-96-96.png 大促用这个
                      child: Align(
                        child: SizedBox(
                          width: 16,
                          height: 16,
                          // 绝了，这里给图片设置宽高不生效
                          child: Image.network(
                            'https://gw.alicdn.com/imgextra/i3/O1CN01ucqtme1fBOYLSvpud_!!6000000003968-2-tps-96-96.png',
                            width: 16,
                            height: 16,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                    ),
                  ),
                // 购买按钮
                if ((packageInfos.showBuyButton ?? false) &&
                    (packageInfos.buyButtonSupport ?? false))
                  GestureDetector(
                    onTap: () {
                      if (packageInfos.buyButtonGray ?? false) {
                        return;
                      }
                      changeNotifier.ctrlClicked(
                          context,
                          'hotel-shelf.buy_$index',
                          'hotel-shelf', <String, dynamic>{
                        'index': index,
                        'sku_id': packageInfos.skuId,
                        'pv_id': packageInfos.skuPvId
                      });
                      if (packageInfos.directJumpBuy ?? false) {
                        changeNotifier.gotoBuy(context, packageInfos.skuId);
                      } else {
                        changeNotifier.gotoBuySku(
                            context, packageInfos.skuId, packageInfos.skuPvId);
                      }
                    },
                    child: Container(
                      width: 41,
                      height: 41,
                      padding: EdgeInsets.symmetric(horizontal: 3),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6.00),
                          color: (packageInfos.buyButtonGray ?? false)
                              ? Color(0xFFd2d4d9)
                              : Color(0xFFFF7300),
                          image: (packageInfos.advanceButtonSupport ?? false)
                              ? DecorationImage(
                                  image: NetworkImage(
                                    'https://gw.alicdn.com/imgextra/i1/O1CN01JGXmJf1qefO8APWxr_!!6000000005521-2-tps-280-120.png',
                                  ),
                                  fit: BoxFit.cover, // 控制图片填充方式（关键属性）
                                )
                              : null),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Text(
                            packageInfos.buyButtonDesc ?? '',
                            style: TextStyle(
                              fontSize:
                                  (packageInfos.buyButtonDesc?.length ?? 0) > 2
                                      ? 11
                                      : 13.00,
                              fontWeight: FontWeight.w500,
                              color: Color(0xFFFFFFFF),
                            ),
                            maxLines: 2,
                          ),
                          if ((packageInfos.advanceButtonSupport ?? false) &&
                              packageInfos.advancePrice != null)
                            Text(
                              '¥${packageInfos.advancePrice}',
                              style: TextStyle(
                                fontSize: 10,
                                color: Color(0xFFFFFFFF),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 价格
  Widget priceWidget(HotelPackageModel packageInfos,
      HotelShelfV2ChangeNotifier changeNotifier) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        //价格
        Row(
          children: <Widget>[
            if (packageInfos.num4ExChange != null)
              Row(
                children: <Widget>[
                  Text('积分',
                      textAlign: TextAlign.end,
                      style: const TextStyle(
                          color: Color(0xFFFF5533), fontSize: 12.00)),
                  Text(packageInfos.num4ExChange!,
                      textAlign: TextAlign.left,
                      style: const TextStyle(
                        fontFamily: 'fliggy_sans102_bd',
                        package: 'ffonts',
                        color: Color(0xFFFF5533),
                        fontWeight: FontWeight.w500,
                        fontSize: 21.00,
                      )),
                  Text('+',
                      textAlign: TextAlign.end,
                      style: const TextStyle(
                          color: Color(0xFFFF5533), fontSize: 12.00)),
                ],
              ),
            if (packageInfos.num4ExChange == null &&
                packageInfos.priceTitle != null &&
                packageInfos.priceTitle!.isNotEmpty)
              Text('${packageInfos.priceTitle!} ', //cell.priceTitle!,
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                      color: Color(0xFFFF5533), fontSize: 12.00)),
            if (packageInfos.buyButtonGray ?? false)
              Container(
                margin: EdgeInsets.only(top: 3),
                child: const Text('¥',
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontFamily: 'fliggy_sans102_bd',
                        package: 'ffonts',
                        color: Color(0xFF919499),
                        fontSize: 12.00)),
              ),
            if (!(packageInfos.buyButtonGray ?? false))
              Container(
                margin: EdgeInsets.only(top: 3),
                child: const Text('¥',
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontFamily: 'fliggy_sans102_bd',
                        package: 'ffonts',
                        color: Color(0xFFFF5533),
                        fontSize: 12.00)),
              ),
            if (packageInfos.buyButtonGray ?? false)
              if (packageInfos.finalPriceText != null)
                Text(packageInfos.finalPriceText!,
                    textAlign: TextAlign.left,
                    style: const TextStyle(
                      fontFamily: 'fliggy_sans102_bd',
                      package: 'ffonts',
                      color: Color(0xFF919499),
                      fontWeight: FontWeight.w500,
                      fontSize: 21.00,
                    )),
            if (!(packageInfos.buyButtonGray ?? false))
              if (packageInfos.finalPriceText != null)
                Text(packageInfos.finalPriceText!,
                    textAlign: TextAlign.left,
                    style: const TextStyle(
                      fontFamily: 'fliggy_sans102_bd',
                      package: 'ffonts',
                      color: Color(0xFFFF5533),
                      fontWeight: FontWeight.w500,
                      fontSize: 21.00,
                    )),
            if (packageInfos.finalPriceSuffix != null)
              Text(packageInfos.finalPriceSuffix!,
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                    color: Color(0xFFFF5533),
                    fontSize: 12.00,
                  )),
          ],
        ),
        //比价
        if (packageInfos.priceCompareVO != null)
          GestureDetector(
            onTap: () {
              changeNotifier.shoDialogHeightWebPop(
                  context, packageInfos.priceCompareVO!.jumpUrl);
            },
            child: Container(
              height: 14,
              padding: EdgeInsets.only(
                left: 1.5,
                right: 1.5,
              ),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                gradient: LinearGradient(
                  colors: <Color>[Color(0x01FFE6E0), Color(0xffFFE6E0)],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Text(
                    packageInfos.priceCompareVO!.title ?? '',
                    style: TextStyle(
                      fontSize: 10,
                      color: Color(0xFFFF5533),
                      height: 1.2,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Container(
                      margin: EdgeInsets.symmetric(horizontal: 2),
                      child: Image.network(
                        'https://gw.alicdn.com/imgextra/i4/O1CN01fnmeEi27UuhVRSB2h_!!6000000007801-2-tps-39-30.png',
                        width: 11.5,
                        height: 9,
                      )),
                  Text(
                    packageInfos.priceCompareVO!.compareTitle ?? '',
                    style: TextStyle(
                      fontSize: 10,
                      color: Color(0xFFFF5533),
                      height: 1.2,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    packageInfos.priceCompareVO!.saveMoneyText ?? '',
                    style: TextStyle(
                      fontSize: 10,
                      color: Color(0xFFFF5533),
                      height: 1.2,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (packageInfos.priceCompareVO!.jumpUrl != null)
                    Container(
                        margin: EdgeInsets.only(left: 2),
                        child: Image.network(
                          'https://gw.alicdn.com/imgextra/i2/O1CN01nnAuyR290LYnfngpl_!!6000000008005-2-tps-18-19.png',
                          width: 4.5,
                          height: 4.5,
                        ))
                ],
              ),
            ),
          ),
      ],
    );
  }

  /// 半浮层里面的底部按钮
  Widget popBottomWidget(BuildContext context, HotelPackageModel packageInfos,
      HotelShelfV2ChangeNotifier changeNotifier, int index) {
    return Container(
      height: 70,
      width: 375,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          // 左边价格
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: <Widget>[
              Container(
                  child: const Text('¥',
                      textAlign: TextAlign.left,
                      style:
                          TextStyle(color: Color(0xFFFF5533), fontSize: 12.00)),
                  margin: Platform.isAndroid
                      ? const EdgeInsets.fromLTRB(0, 0, 0, 3.00)
                      : const EdgeInsets.fromLTRB(0, 0, 0, 1.00)),
              Text(packageInfos.finalPriceText!,
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                      color: Color(0xFFFF5533),
                      fontWeight: FontWeight.w500,
                      fontSize: 21.00,
                      height: 1)),
              Container(
                padding: const EdgeInsets.only(bottom: 3),
                child: const Text('起',
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        color: Color(0xFFFF5533), fontSize: 12.00, height: 1)),
              ),
            ],
          ),

          // 右边按钮
          GestureDetector(
              onTap: () {
                changeNotifier.ctrlClicked(context, 'hotel-shelf.buy_$index',
                    'hotel-shelf', <String, dynamic>{
                  'index': index,
                  'isPop': true,
                  'sku_id': packageInfos.skuId,
                  'pv_id': packageInfos.skuPvId
                });
                if (packageInfos.buyButtonGray ?? false) {
                  return;
                }

                if (packageInfos.directJumpBuy ?? false) {
                  changeNotifier.gotoBuy(context, packageInfos.skuId);
                } else {
                  changeNotifier.gotoBuySku(
                      context, packageInfos.skuId, packageInfos.skuPvId);
                }
              },
              child: Container(
                  child: Center(
                    child: Text(
                        changeNotifier.itemDetailModel.buyBannerDataModel
                                ?.buyButtonDesc ??
                            '立即购买',
                        textAlign: TextAlign.left,
                        style: TextStyle(
                            color: Color(0xFFFFFFFF),
                            fontSize: 16.00,
                            height: 1)),
                  ),
                  width: 201.00,
                  height: 42.00,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(21.00),
                      color: const Color(0xFFFE560A))))
        ],
      ),
    );
  }
}

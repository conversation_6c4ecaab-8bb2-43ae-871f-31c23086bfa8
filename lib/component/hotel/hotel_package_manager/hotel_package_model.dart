/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description
import 'package:fjson_exports/fjson_exports.dart';
import '../../../custom_widget/common_tag/common_tag_model.dart';
import '../../../utils/safe_access.dart';
import '../../hotel_package_to_calendar/model.dart';

class HotelPackageModel {
  bool showXelements = true;
  String? buyButtonDesc;
  bool? buyButtonGray;
  bool? buyButtonSupport;
  bool? cartButtonSupport;
  bool? couponFlag;
  bool? directJumpBuy;
  int? finalPrice;
  String? finalPriceText;
  String? finalPriceSuffix;
  String? lookInventoryDesc;
  LookInventoryLink? lookInventoryLink;
  bool? moreHotZone;
  String? packageExplainDesc;
  String? packageExplainJumpUrl;
  List<TextVO> packageNameTitles = <TextVO>[];
  String? packageName;
  String? priceTitle;
  int? price;
  String? priceText;

  // List<Null>? productDescList;
  bool? showBuyButton;
  String? skuId;
  String? skuPvId;
  bool? soldOut;

  /// 是否是大促
  bool? advance;

  // 少hotelTags，dx没有，找不到样例

  List<Xelements>? xelements;

  List<HotelTags>? hotelTags;
  List<HighLightItem>? highLightList;
  List<CommonTagModel>? commonTagModels;
  bool? advanceButtonSupport;
  String? advancePrice;

  //积分
  String? num4ExChange;

  PriceCompareVO? priceCompareVO;

  HotelPackageModel.fromJson(Map<String, dynamic> json,
      {String? promotionPriceTitle}) {
    advanceButtonSupport =
        safeNonNullBool(json['advanceButton']?['advanceButtonSupport']);
    buyButtonDesc = (advanceButtonSupport ?? false)
        ? (json['advanceButton']?['advanceButtonDesc'])
        : (json['buyButtonDesc'] ?? '');

    advancePrice = json['advanceButton']?['advancePrice'];
    buyButtonGray = safeNonNullBool(json['buyButtonGray']);
    buyButtonSupport = safeNonNullBool(json['buyButtonSupport']);
    cartButtonSupport = safeNonNullBool(json['cartButtonSupport']);
    couponFlag = safeNonNullBool(json['couponFlag']);
    num4ExChange = json['num4ExChange'];
    directJumpBuy = safeNonNullBool(json['directJumpBuy']);
    finalPrice = safeInt(json['finalPrice']);
    finalPriceText = json['finalPriceText'];
    finalPriceSuffix = json['finalPriceSuffix'];
    lookInventoryDesc = json['lookInventoryDesc'];
    lookInventoryLink = json['lookInventoryLink'] != null
        ? LookInventoryLink.fromJson(json['lookInventoryLink'])
        : null;
    moreHotZone = safeNonNullBool(json['moreHotZone']);
    packageExplainDesc = json['packageExplainDesc'];
    packageExplainJumpUrl = json['packageExplainLink']?['jumpH5Url'];
    packageName = json['packageName'];
    priceTitle = json['priceTitle'] ?? promotionPriceTitle;
    price = safeInt(json['price']);
    priceText = json['priceText'];
    // if (json['productDescList'] != null) {
    //   productDescList = <Null>[];
    //   json['productDescList'].forEach((v) {
    //     productDescList!.add(Null.fromJson(v));
    //   });
    // }
    showBuyButton = safeNonNullBool(json['showBuyButton']);
    skuId = SafeAccess.safeParseString(json['skuId']);
    skuPvId = json['skuPvId'];
    soldOut = safeNonNullBool(json['soldOut']);

    // 大促氛围按钮相关，看不到样例，先自己写了
    advance = json['advanceButton']?['advanceButtonSupport'] ?? false;

    if (json['xelements'] != null) {
      xelements = <Xelements>[];
      json['xelements'].forEach((dynamic v) {
        xelements!.add(Xelements.fromJson(v));
      });
    }
    if (json['hotelTags'] != null) {
      hotelTags = <HotelTags>[];
      json['hotelTags'].forEach((dynamic v) {
        hotelTags!.add(HotelTags.fromJson(v));
      });
    }
    if (json['highLightList'] != null) {
      highLightList = <HighLightItem>[];
      commonTagModels = <CommonTagModel>[];
      json['highLightList'].forEach((dynamic v) {
        final HighLightItem highLightItem = HighLightItem.fromJson(v);
        highLightList!.add(highLightItem);
        final CommonTagModel commonTagModel = CommonTagModel(
            text: highLightItem.title,
            textColor: highLightItem.textColor,
            prefixText: highLightItem.highlightPrefix);
        commonTagModels!.add(commonTagModel);
      });
    }
    if (json['priceCompareVO'] != null) {
      priceCompareVO = PriceCompareVO.fromJson(json['priceCompareVO']);
    }
  }
}

class LookInventoryLink {
  String? jumpH5Url;
  bool? jumpNative;

  LookInventoryLink({this.jumpH5Url, this.jumpNative});

  LookInventoryLink.fromJson(Map<String, dynamic> json) {
    jumpH5Url = json['jumpH5Url'];
    jumpNative = json['jumpNative'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['jumpH5Url'] = this.jumpH5Url;
    data['jumpNative'] = this.jumpNative;
    return data;
  }
}

class Xelements {
  String? descColor;
  String? elementPosition;
  String? icon;
  String? subTitleColor;
  String? title;
  String? subTitle;
  bool? titleBold;
  String? titleColor;
  bool? subTitleBold;

  Xelements(
      {this.descColor,
      this.elementPosition,
      this.icon,
      this.subTitleColor,
      this.title,
      this.titleBold,
      this.titleColor,
      this.subTitleBold});

  Xelements.fromJson(Map<String, dynamic> json) {
    descColor = json['descColor'];
    elementPosition = json['elementPosition'];
    icon = json['icon'];
    subTitleColor = json['subTitleColor'];
    title = json['title'];
    subTitle = json['subTitle'];
    titleBold = json['titleBold'];
    titleColor = json['titleColor'];
    subTitleBold = json['subTitleBold'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['descColor'] = this.descColor;
    data['elementPosition'] = this.elementPosition;
    data['icon'] = this.icon;
    data['subTitleColor'] = this.subTitleColor;
    data['title'] = this.title;
    data['titleBold'] = this.titleBold;
    data['titleColor'] = this.titleColor;
    data['subTitleBold'] = this.subTitleBold;
    data['subTitle'] = this.subTitle;
    return data;
  }
}

class HotelTags {
  bool? isFirst;
  String? textColor;
  String? value;

  HotelTags({this.isFirst, this.textColor, this.value});

  HotelTags.fromJson(Map<String, dynamic> json) {
    isFirst = safeNonNullBool(json['isFirst']);
    textColor = json['textColor'] ?? '#FF333333';
    value = json['value'];
  }
}

class HighLightItem {
  String? backgroundColor;
  String? icon;
  String? textColor;
  String? title;
  String? highlightPrefix;

  HighLightItem.fromJson(Map<String, dynamic> json) {
    backgroundColor = json['backgroundColor'];
    icon = json['icon'];
    textColor = json['textColor'];
    title = json['title'];
    highlightPrefix = json['highlightPrefix'];
  }
}

class PriceCompareVO {
  String? compareTitle;
  String? jumpUrl;
  String? title;
  String? saveMoneyText;
  String? icon;

  PriceCompareVO.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    icon = json['icon'];
    compareTitle = json['compareTitle'];
    jumpUrl = json['jumpUrl'];
    saveMoneyText = json['saveMoneyText'];
  }
}

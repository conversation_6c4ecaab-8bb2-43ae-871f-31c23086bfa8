
import 'package:fjson_exports/fjson_exports.dart';
import 'package:flutter/material.dart';

import 'hotel_package_model.dart';

/// <AUTHOR>
/// @date Created on 2024/12/6
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

/// 机票货架管理，涉及od的筛选和联动，原始数据的存放，以及筛选后的数据的存放
class HotelPackageManager {
  HotelPackageManager();

  /// 原始数据
  List<HotelPackageModel>? packageList;

  /// 筛选后的数据
  List<HotelPackageModel>? filterPackageList;

  String? title;

  /// 是否可以展开
  bool canExpand = false;

  int expandCount = 4;

  /// 是否展开
  ValueNotifier<bool> isExpand = ValueNotifier<bool>(false);


  factory HotelPackageManager.fromJson(Map json) {
    final HotelPackageManager model = HotelPackageManager();

    // 初始化货架数据
    if (json.containsKey('packageShelf')) {
      final Map packageShelf = safeNonNullMap(json['packageShelf'], (dynamic e) => e);
      final Map packageShelfData = safeNonNullMap(packageShelf['data'], (dynamic e) => e);
      final List packageInfos = safeNonNullList(packageShelfData['packageInfos'], (dynamic e) => e);
      model.packageList = [];
      for (dynamic package in packageInfos) {
        model.packageList!.add(HotelPackageModel.fromJson(package));
      }

      model.expandCount = safeInt(packageShelfData['expandCount']) ?? 4;

      if (model.packageList!.length > model.expandCount) {
        model.canExpand = true;
        model.filterPackageList = model.packageList!.sublist(0, model.expandCount);
      } else {
        model.canExpand = false;
        model.filterPackageList = model.packageList;
      }

      model.title = packageShelfData['title'] ?? '';

    }


    // 初始时没有筛选，筛选的package就是下发的

    // model.filterPackageList = model.packageList;
    return model;
  }
}


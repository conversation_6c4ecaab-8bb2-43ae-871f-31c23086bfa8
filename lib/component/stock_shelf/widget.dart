import 'dart:io';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../custom_widget/block_title.dart';
import '../../custom_widget/detail_arrow.dart';
import '../../custom_widget/dialog_webview.dart';
import '../../utils/TextConfig.dart';
import '../../utils/common_config.dart';
import '../hotel_package_to_calendar/widget.dart';
import 'model.dart';
import 'notifier.dart';
import 'package:ficonfont/ficonfont.dart';
import 'package:fliggy_router/fliggy_router.dart';

class StockShelfWidget extends StatelessWidget {
  const StockShelfWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final StockShelfChangeNotifier changeNotifier =
        Provider.of<StockShelfChangeNotifier>(context);

    StockShelfModel? model = changeNotifier.itemDetailModel.stockShelfModel;

    if (model == null) {
      return SizedBox.shrink();
    }
    changeNotifier.ctrlExposure(context, 'dnw-packages-choose.default', null);
    return Container(
      margin: const EdgeInsets.only(bottom: paddingBottom),
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: <Widget>[
          //套餐日历交互
          HotelPackageToCalendarWidget(changeNotifier.itemDetailModel.hotelPackageToCalendarModel,changeNotifier),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12),


            child: Column(
              children: <Widget>[

                // 标题
                if (model.title != null && model.title!.isNotEmpty)
                  BlockTitle(model.title ?? ''),

                if (model.canExpand)
                  ValueListenableBuilder(
                      valueListenable: model.isExpand,
                      builder: (BuildContext context, bool isExpand, Widget? child) {
                        List<PackageInfos> packageInfos;
                        if (isExpand) {
                          packageInfos = model!.packageInfos!;
                        } else {
                          packageInfos = model!.filterPackageList!;
                        }

                        return buildPackageList(context, packageInfos,
                            changeNotifier); // hotelPackageManager: hotelPackageManager,
                      })
                else
                  buildPackageList(context, model!.packageInfos!, changeNotifier),

                if (model.canExpand)
                  ValueListenableBuilder(
                      valueListenable: model.isExpand,
                      builder: (BuildContext context, bool isExpand, Widget? child) {
                        return GestureDetector(
                          onTap: () {
                            model.isExpand.value = !model.isExpand.value;
                          },
                          child: Container(
                            height: 55,
                            color: Color(0xFFFFFFFF),
                            alignment: Alignment.center,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Container(
                                  margin: EdgeInsets.only(right: 5),
                                  child: Text(
                                    model.isExpand.value ? '收起' : '查看更多',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.w700,
                                        fontSize: 13,
                                        color: Color(0xFF333333)),
                                  ),
                                ),
                                if (!model.isExpand.value) topArrowBig,
                                if (model.isExpand.value) bottomArrowBig,
                              ],
                            ),
                          ),
                        ); // hotelPackageManager: hotelPackageManager,
                      }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildPackageList(BuildContext context, List<PackageInfos> packageInfos,
      StockShelfChangeNotifier changeNotifier) {
    final List<Widget> listWidget = <Widget>[];
    packageInfos.asMap().forEach((int key, PackageInfos item) {
      listWidget.add(linearLayoutWidget(context, item, changeNotifier,key));
    });
    return Column(
      children: listWidget,
    );
  }

  Widget linearLayoutWidget(BuildContext context, PackageInfos packageInfos,
      StockShelfChangeNotifier changeNotifier,int index) {
    changeNotifier.ctrlExposure(context, 'dnw-packages-choose.title_$index', <String,dynamic>{'index':index,'sku_id':packageInfos.skuId,'pv_id':packageInfos.skuPvId});
    return GestureDetector(
      onTap: () {
        if( packageInfos.packageExplainLink?.jumpH5Url == null){
          return;
        }
        changeNotifier.ctrlClicked(context, 'dnw-packages-choose.action_$index','dnw-packages-choose', <String,dynamic>{'index':index,'sku_id':packageInfos.skuId,'pv_id':packageInfos.skuPvId});
        final DialogWebView dialogWebView = DialogWebView(
            context: context,
            url: '${packageInfos.packageExplainLink?.jumpH5Url}&hideClose=true',
            itemDetailEngine: changeNotifier.itemDetailEngine,
            popConfig: H5PopConfig(
                popTitle: '套餐说明', popHeight: 600),
            bottomWidget: popBottomWidget(
                context,
                packageInfos,
                changeNotifier
            )
        );
        dialogWebView.showPop();
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 10.0),
        padding: EdgeInsets.only(top: 15, bottom: 15),
        color: Color(0x00FFFFFF),// 我以为没有颜色，是container没有宽高，原来是没有颜色，没有元素的时候都不能点击，
        // decoration: BoxDecoration(
        //   color: Color(0xFFFDFDFD), // 背景颜色
        //   borderRadius: BorderRadius.circular(2), // 设置圆角
        // ),
        // width: 333,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          // mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            // 标题
            if (packageInfos.packageName != null)
              Container(
                constraints: BoxConstraints(
                  maxWidth: 325,
                ),
                child: Text(
                  packageInfos.packageName ?? '',
                  style: TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

            // if (packageInfos.hotelTags != null)
            //   Container(
            //     margin: EdgeInsets.only(top: 10),
            //     child: Row(
            //       children: packageInfos.hotelTags!.map((tag) {
            //         return Text(
            //           ((tag.isFirst ?? false) ? '' : ' | ') + (tag.value ?? ''),
            //           style: TextStyle(color: Color(0xFF333333), fontSize: 12),
            //         );
            //       }).toList(),
            //     ),
            //   ),

            // if (packageInfos.xelements != null)
            //   Column(
            //     children: packageInfos.xelements!.map((element) {
            //       return Container(
            //         padding: EdgeInsets.only(top: 9.0),
            //         child: Column(
            //           crossAxisAlignment: CrossAxisAlignment.start,
            //           children: <Widget>[
            //             // 第一部分：图标、标题、箭头和标签
            //             Row(
            //               crossAxisAlignment: CrossAxisAlignment.center,
            //               children: <Widget>[
            //                 // 图标部分
            //                 if (element.icon != null)
            //                   Container(
            //                     width: 15.0,
            //                     height: 15.0,
            //                     margin: EdgeInsets.only(right: 3.0),
            //                     child: Stack(
            //                       children: <Widget>[
            //                         Container(
            //                           width: 15.0,
            //                           height: 15.0,
            //                           decoration: BoxDecoration(
            //                             color: Color(0xffffffff),
            //                             borderRadius: BorderRadius.circular(7.5),
            //                             border: Border.all(
            //                               color: Color(0xFFFF8C1A),
            //                               width: 0.5,
            //                             ),
            //                           ),
            //                           child: Center(
            //                             child: Text(
            //                               element.icon!,
            //                               style: TextStyle(
            //                                 color: Color(0xFFFF8C1A),
            //                                 fontSize: 11,
            //                                 fontWeight: FontWeight.bold,
            //                               ),
            //                             ),
            //                           ),
            //                         ),
            //                         // Positioned(
            //                         //   right: 0,
            //                         //   top: 0,
            //                         //   child: Visibility(
            //                         //     visible: !(element.hideIconGapLine ||
            //                         //         element.icon != null),
            //                         //     child: Container(
            //                         //       width: 1.0,
            //                         //       height: 15.0,
            //                         //       color: Color(0xFF000000), // 根据需求调整颜色
            //                         //     ),
            //                         //   ),
            //                         // ),
            //                       ],
            //                     ),
            //                   ),
            //                 // 标题
            //                 if (element.title != null)
            //                   Text(
            //                     element.title! + '  ' + (element.subTitle ?? ''),
            //                     maxLines: 1,
            //                     overflow: TextOverflow.ellipsis,
            //                     style: TextStyle(
            //                       fontSize: 10.0,
            //                       color: Color(0xFF919499),
            //                     ),
            //                   ),
            //               ],
            //             ),
            //
            //
            //           ],
            //         ),
            //       );
            //     }).toList(),
            //   ),

            Padding(
              padding: EdgeInsets.only(top: 9.0),
              child: Row(
                children: <Widget>[
                  // 左边价格
                  if (packageInfos.finalPriceText != null)
                    priceWidget(packageInfos),
                  // Text(
                  //   '¥${packageInfos.finalPriceText}',
                  //   style: TextStyle(
                  //       fontSize: 18.0,
                  //       color: Color(0xFFFF401A),
                  //       fontWeight: FontWeight.bold,
                  //       height: 1),
                  // ),

                  // 价格说明
                  if (packageInfos.packageExplainDesc != null)
                    GestureDetector(
                      onTap: () {
                        changeNotifier.ctrlClicked(context, 'dnw-packages-choose.action_$index','dnw-packages-choose', <String,dynamic>{'index':index,'sku_id':packageInfos.skuId,'pv_id':packageInfos.skuPvId});
                        final DialogWebView dialogWebView = DialogWebView(
                            context: context,
                            url: packageInfos.packageExplainLink?.jumpH5Url ?? '',
                            itemDetailEngine: changeNotifier.itemDetailEngine,
                            popConfig: H5PopConfig(
                                popTitle: '套餐说明', popHeight: 600),
                            bottomWidget: popBottomWidget(
                                context,
                                packageInfos,
                                changeNotifier
                            )
                        );
                        dialogWebView.showPop();
                        // FliggyNavigatorApi.getInstance().push(context,
                        //     packageInfos.packageExplainLink?.jumpH5Url ?? '');
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 3),
                        child: Text(
                          packageInfos.packageExplainDesc ?? '',
                          style: TextStyle(
                              fontSize: 11.0,
                              color: Color(0xFF00A2FF),
                              height: 1),
                        ),
                      ),
                    ),

                  // 实在也没有蓝色箭头了，展示不出来就再换吧
                  Ficon(
                    0xeb15,
                    10,
                    Color(0xFF00A2FF),
                  ),
                  Spacer(),
                  // 右边购买按钮

                  if ((packageInfos.cartButtonSupport ?? false) &&
                      !(packageInfos.buyButtonGray ?? false))
                    GestureDetector(
                      onTap: () {
                        /// 加入购物车，但是现在确实没有这样的，不知道怎么搞
                        if (packageInfos.buyButtonGray ?? false) {
                          return;
                        }
                        changeNotifier.ctrlClicked(context, 'dnw-packages-choose.cart_$index','dnw-packages-choose', <String,dynamic>{'index':index,'sku_id':packageInfos.skuId,'pv_id':packageInfos.skuPvId});
                        changeNotifier.addCar(context, packageInfos.skuId?.toString(), packageInfos.skuPvId);
                      },
                      child: Container(
                        width: 39,
                        height: 39,
                        padding: EdgeInsets.symmetric(horizontal: 3),
                        margin: EdgeInsets.only(right: 10.0),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6.00),
                            border: Border.all(color: const Color(0xFFFF7300)),
                            color: Color(0xFFFFFFFF)),
                        // https://gw.alicdn.com/imgextra/i4/O1CN01ZWeUJA1xX4NeVDmbi_!!6000000006452-2-tps-96-96.png 大促用这个
                        child: Align(
                          child: Container(
                            width: 16,
                            height: 16,
                            // 绝了，这里给图片设置宽高不生效
                            child: Image.network(
                              'https://gw.alicdn.com/imgextra/i3/O1CN01ucqtme1fBOYLSvpud_!!6000000003968-2-tps-96-96.png',
                              width: 16,
                              height: 16,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                    ),

                  if ((packageInfos.showBuyButton ?? false) && (packageInfos.buyButtonSupport??false))
                    GestureDetector(
                      onTap: () {
                        if (packageInfos.buyButtonGray ?? false) {
                          return;
                        }
                        changeNotifier.ctrlClicked(context, 'dnw-packages-choose.buy_$index','dnw-packages-choose', <String,dynamic>{'index':index,'sku_id':packageInfos.skuId,'pv_id':packageInfos.skuPvId});
                        if(packageInfos.directJumpBuy??false){
                          changeNotifier.gotoBuy(context, packageInfos.skuId?.toString());
                        }else{
                          changeNotifier.gotoBuySku(context, packageInfos.skuId?.toString(), packageInfos.skuPvId);
                        }

                      },
                      child: Container(
                        width: 39,
                        height: 39,
                        padding: EdgeInsets.symmetric(horizontal: 3),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(6.00),
                          color: (packageInfos.buyButtonGray ?? false)
                              ? Color(0xFFE6E6E6)
                              : Color(0xFFFF7300),
                        ),
                        child: Align(
                          child: SizedBox(
                            width: 28,
                            child: Center(
                              child: Text(
                                packageInfos.buyButtonDesc ?? '',
                                style: TextStyle(
                                  height: 1,
                                  fontSize:
                                  (packageInfos.buyButtonDesc?.length ?? 0) >
                                      1
                                      ? 14
                                      : 18.00,
                                  fontWeight: FontWeightExt.bold,
                                  color: (packageInfos.buyButtonGray ?? false)
                                      ? Color(0xFF999999)
                                      : Color(0xFFFFFFFF),
                                ),
                                maxLines: 2,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 价格
  Widget priceWidget(PackageInfos packageInfos) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: <Widget>[
        // if (packageInfos.priceTitle != null &&
        //     packageInfos.priceTitle!.isNotEmpty)
        //   Container(
        //       child: Text('${packageInfos.priceTitle!} ', //cell.priceTitle!,
        //           textAlign: TextAlign.left,
        //           style: const TextStyle(
        //             // fontFamily: 'fliggy_sans102_bd',
        //             // package: 'ffonts',
        //               color: Color(0xFFFF5533),
        //               fontSize: 12.00)),
        //       margin: EdgeInsets.only(bottom: Platform.isAndroid ? 3 : 2.00)),
        Container(
            child: const Text('¥',
                textAlign: TextAlign.left,
                style: TextStyle(
                    fontFamily: 'fliggy_sans102_bd',
                    package: 'ffonts',
                    color: Color(0xFFFF5533),
                    fontSize: 12.00)),
            margin: EdgeInsets.only(bottom: Platform.isAndroid ? 3 : 2.00)),
        if (packageInfos.finalPriceText != null)
          Text(packageInfos.finalPriceText!,
              textAlign: TextAlign.left,
              style: const TextStyle(
                  fontFamily: 'fliggy_sans102_bd',
                  package: 'ffonts',
                  color: Color(0xFFFF5533),
                  fontWeight: FontWeight.w500,
                  fontSize: 21.00,
                  height: 1)),
      ],
    );
  }

  /// 半浮层里面的底部按钮
  Widget popBottomWidget(BuildContext context, PackageInfos cell,
      StockShelfChangeNotifier changeNotifier) {
    return Container(
      height: 70,
      width: 375,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          // 左边价格
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: <Widget>[
              Container(
                  child: const Text('¥',
                      textAlign: TextAlign.left,
                      style:
                          TextStyle(color: Color(0xFFFF5533), fontSize: 12.00)),
                  margin: Platform.isAndroid
                      ? const EdgeInsets.fromLTRB(0, 0, 0, 3.00)
                      : const EdgeInsets.fromLTRB(0, 0, 0, 1.00)),
              Text(cell.finalPriceText!,
                  textAlign: TextAlign.left,
                  style: const TextStyle(
                      color: Color(0xFFFF5533),
                      fontWeight: FontWeight.w500,
                      fontSize: 21.00,
                      height: 1)),
              Container(
                padding: const EdgeInsets.only(bottom: 3),
                child: const Text('起',
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        color: Color(0xFFFF5533), fontSize: 12.00, height: 1)),
              ),
            ],
          ),

          // 右边按钮
          GestureDetector(
              onTap: () {
                /// todo :点击购买事件
                changeNotifier.gotoBuy(
                    context, cell.skuId?.toString());
              },
              child: Container(
                  child: const Center(
                    child: Text('立即购买',
                        textAlign: TextAlign.left,
                        style: TextStyle(
                            color: Color(0xFFFFFFFF),
                            fontSize: 16.00,
                            height: 1)),
                  ),
                  width: 201.00,
                  height: 42.00,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(21.00),
                      color: const Color(0xFFFE560A))))
        ],
      ),
    );
  }
}

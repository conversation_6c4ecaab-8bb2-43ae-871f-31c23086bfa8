import 'package:flutter/cupertino.dart';

import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'model.dart';

class AlitripPromChangeNotifier extends ComponentChangeNotifier {
  AlitripPromDataModel? get alitripPromDataModel =>
      itemDetailModel.alitripPromDataModel;

  AlitripPromChangeNotifier(ComponentContext context) : super(context);

  ///跳转
  void widgetClick(BuildContext context, String? url) {
    if (url != null && url.isNotEmpty) {
      FliggyNavigatorApi.getInstance().push(context, url, anim: Anim.slide);
    }
  }
}

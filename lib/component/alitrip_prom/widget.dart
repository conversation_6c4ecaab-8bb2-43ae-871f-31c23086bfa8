import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fround_image/fround_image.dart';
import '../../custom_widget/detail_arrow.dart';
import '../../custom_widget/null_widget.dart';
import '../../utils/common_config.dart';
import 'model.dart';
import 'notifier.dart';

class AlitripPromWidget extends StatelessWidget {
  const AlitripPromWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final AlitripPromChangeNotifier changeNotifier =
        Provider.of<AlitripPromChangeNotifier>(context);
    changeNotifier.fromJson();
    final AlitripPromDataModel? alitripPromDataModel =
        changeNotifier.alitripPromDataModel;
    return alitripPromDataModel == null
        ? nullWidget
        : Container(
            height: 47.00,
            margin: const EdgeInsets.only(bottom: itemDivider),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(cardBorderRadius),
            ),
            clipBehavior: Clip.hardEdge,
            child: Stack(children: <Widget>[
              if (alitripPromDataModel.backgroundPicUrl != null)
                FRoundImage.network(alitripPromDataModel.backgroundPicUrl!,
                    width: 360, height: 47, fit: BoxFit.fill),
              SizedBox(
                height: 47.00,
                child: _buildOneItem(
                    context, changeNotifier, alitripPromDataModel),
              ),
            ]),
          );
  }

  Widget _buildOneItem(
      BuildContext context,
      AlitripPromChangeNotifier changeNotifier,
      AlitripPromDataModel alitripPromDataModel) {
    return GestureDetector(
      onTap: () {
        changeNotifier.widgetClick(context, alitripPromDataModel.marketUrl);
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(9, 0, 9, 0),
        child: Row(children: <Widget>[
          Image.network(
            alitripPromDataModel.promPicUrl!,
            height: 28,
          ),
          Expanded(
            child: Container(
              margin: const EdgeInsets.fromLTRB(6, 0, 0, 0),
              child: Text(alitripPromDataModel.promDesc!,
                  textAlign: TextAlign.left,
                  style: TextStyle(
                      color: alitripPromDataModel.promDescColor,
                      fontSize: 13.00)),
            ),
          ),
          Text(alitripPromDataModel.promRightDesc!,
              textAlign: TextAlign.left,
              style: TextStyle(
                  color: alitripPromDataModel.promDescColor, fontSize: 10.00)),
          if (alitripPromDataModel.marketUrl != '')
            Container(
              margin: const EdgeInsets.fromLTRB(4, 0, 0, 0),
              child: rightArrowSmallWhite,
            )
        ]),
      ),
    );
  }
}

import 'dart:ui';

import '../../utils/common_util.dart';
import '../../utils/safe_access.dart';

class AlitripPromDataModel {
  String? marketUrl;
  String? promDesc;
  String? promRightDesc;
  String? promPicUrl;
  String? backgroundPicUrl;
  Color? promDescColor;

  AlitripPromDataModel.fromJson(Map<dynamic, dynamic> dataModel) {
   final Map<String,dynamic> alitripPromTag = SafeAccess.safeParseMap(dataModel['alitripPromTag']);
   final Map<String,dynamic> json = SafeAccess.safeParseMap(alitripPromTag['data']);

    marketUrl = SafeAccess.safeParseString(json['marketUrl']);
    promDesc = SafeAccess.safeParseString(json['promDesc']);
    promRightDesc = SafeAccess.safeParseString(json['promRightDesc']);
    promDescColor = stringToColor(SafeAccess.safeParseString(json['promDescColor']));
    promPicUrl = SafeAccess.safeParseString(json['promPicUrl']);
    backgroundPicUrl = SafeAccess.safeParseString(json['backgroundPicUrl']);
  }
}

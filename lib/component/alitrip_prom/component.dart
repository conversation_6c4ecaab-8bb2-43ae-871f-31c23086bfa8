import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 大促引导
class AlitripPromComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<AlitripPromChangeNotifier>.value(
      value: AlitripPromChangeNotifier(context),
      child: const AlitripPromWidget(),
    );
  }
}
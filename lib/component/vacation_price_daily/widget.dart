import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class VacationPriceDailyWidget extends StatelessWidget {
  const VacationPriceDailyWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VacationPriceDailyChangeNotifier changeNotifier =
        Provider.of<VacationPriceDailyChangeNotifier>(context);
    return Container();
  }
}
import '../../utils/safe_access.dart';
import 'package:fjson_exports/fjson_exports.dart';

class BookingNoticeModel {
  // String? imgUrl;
  // String? processTitle;
  //
  // String? title;
  //
  // List<String>? descList;

  String? bookingProcessTitle;
  String? imageUrl;
  String? bookingNoticeTitle;
  List<DescItem>? descList;
  Map<String,dynamic>? popup;

  BookingNoticeModel({
    this.bookingProcessTitle,
    this.imageUrl,
    this.bookingNoticeTitle,
    this.descList,
  });

  BookingNoticeModel.fromJson(Map<String, dynamic> json) {
    if (json.containsKey('bookingNotice')) {
      final Map<String, dynamic> bookingNotice = json['bookingNotice'];
      final Map<String, dynamic> bookingNoticeData = bookingNotice['data'];
      final List descListJson = bookingNoticeData['descList'];
      descList = [];
      for (final descItemJson in descListJson) {
        descList!.add(DescItem.fromJson(descItemJson));
      }

      bookingNoticeTitle = bookingNoticeData['title'];
      popup = bookingNoticeData['popup'];
    }

    if (json.containsKey('bookingProcess')) {
      final Map<String, dynamic> bookingProcess = json['bookingProcess'];
      final Map<String, dynamic> bookingProcessData = bookingProcess['data'];
      bookingProcessTitle = bookingProcessData['title'];
      imageUrl = bookingProcessData['imageUrl'];
    }
  }
}

class DescItem {
  final String? mainInfo;
  final String? subInfo;
  final String? jumpUrl;
  final String? jumpType;
  final String? jumpText;
  final String? event;

  DescItem({
    this.mainInfo,
    this.subInfo,
    this.jumpUrl,
    this.jumpType,
    this.jumpText,
    this.event,
  });

  DescItem.fromJson(Map<String, dynamic> json)
      : mainInfo = safeString(json['mainInfo']),
        subInfo = safeString(json['subInfo']),
        jumpUrl = safeString(json['jumpUrl']),
        jumpType = safeString(json['jumpType']),
        jumpText = safeString(json['jumpText']),
        event = safeString(json['event']);
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../custom_widget/detail_arrow.dart';
import '../../custom_widget/dialog_flutter.dart';
import '../../utils/common_config.dart';
import '../mind_rule/model.dart';
import '../mind_rule/popWidget/mindRulePop.dart';
import 'model.dart';
import 'notifier.dart';
import 'package:fliggy_router/fliggy_router.dart';

class BookingNoticeWidget extends StatelessWidget {
  const BookingNoticeWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final BookingNoticeChangeNotifier changeNotifier =
        Provider.of<BookingNoticeChangeNotifier>(context);

    final BookingNoticeModel? model =
        changeNotifier.itemDetailModel.bookingNoticeModel;

    if (model == null) {
      return SizedBox.shrink();
    }

    return Container(
      width: 357,
      decoration: BoxDecoration(
        color: Color(0xffffffff),
        borderRadius: BorderRadius.circular(6),
      ),
      margin: const EdgeInsets.only(bottom: itemDivider),
      padding: EdgeInsets.only(left: 12, bottom: 12, right: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // Booking Process Title
          if (model?.bookingProcessTitle != null)
            Padding(
              padding: const EdgeInsets.only(top: 12.0, bottom: 4.5),
              child: Text(
                model?.bookingProcessTitle ?? '',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF0F131A),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          // // ImageView
          SizedBox(
            // margin: EdgeInsets.only(top: imageTopMargin, bottom: showDivider ? 8.0 : 0),
            width: 333,
            child: Image.network(
              model.imageUrl ??
                  'https://gw.alicdn.com/tfs/TB1Cplak639YK4jSZPcXXXrUFXa-666-159.png',
              fit: BoxFit.fitWidth,
              loadingBuilder: (BuildContext context, Widget child,
                  ImageChunkEvent? progress) {
                if (progress == null) {
                  return child;
                }
                return Center(
                  child: CircularProgressIndicator(
                    value: progress.expectedTotalBytes != null
                        ? progress.cumulativeBytesLoaded /
                            progress.expectedTotalBytes!
                        : null,
                  ),
                );
              },
            ),
          ),
          // // Divider
          if (model.bookingNoticeTitle != null)
            Container(
              width: 333,
              height: 1,
              color: Color(0xFFF2F3F5),
            ),
          // // Booking Notice Title
          if (model.bookingNoticeTitle != null)
            Padding(
              padding: const EdgeInsets.only(top: 10.0, bottom: 10),
              child: Text(
                model.bookingNoticeTitle!,
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF0F131A),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

          if (model.descList != null && model.descList!.isNotEmpty)
            ...model.descList!
                .map((DescItem desc) => _buildTableRow(context ,desc,model))
                .toList(),

          /// 这条是补的最下面的分割线
          Container(
            height: 0.5,
            width: 333,
            color: Color(0xFFEBEDF0),
          ),
        ],
      ),
    );
  }

  Widget _buildTableRow(BuildContext context,DescItem descItem,BookingNoticeModel? model) {
    return Column(
      children: <Widget>[
        Container(
          height: 0.5,
          width: 333,
          color: Color(0xFFEBEDF0),
        ),
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              Container(
                width: 0.5,
                color: Color(0xFFEBEDF0),
              ),
              Container(
                width: 90,
                color: Color(0xFFF7F8FA),
                child: Center(
                  child: Text(
                    descItem.mainInfo ?? '',
                    style: TextStyle(fontSize: 12),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              Container(
                width: 0.5,
                color: Color(0xFFEBEDF0),
              ),
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 15, horizontal: 10.5),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      if (descItem.subInfo != null)
                        Text(
                          descItem.subInfo!,
                          style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF92949A),
                              height: 1.3),
                        ),
                      // if (descItem.textList != null)
                      //   ...descItem.textList!.map((text) => Text(
                      //     text,
                      //     style: TextStyle(fontSize: 13, color: Color(0xFF92949A), height: 1.3),
                      //   )).toList(),
                    ],
                  ),
                ),
              ),
              if (descItem.jumpText != null && descItem.jumpUrl != null)
                GestureDetector(
                  onTap: () {
                    FliggyNavigatorApi.getInstance().push(
                        context, descItem.jumpUrl!,
                        anim: Anim.slide);
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Row(
                      children: <Widget>[
                        Text(
                          descItem.jumpText!,
                          style: TextStyle(fontSize: 12),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Container(margin:EdgeInsets.only(left: 3),child: rightArrowSmall),
                      ],
                    ),
                  ),
                ),
              if (descItem.event != null && descItem.jumpType != null)
                GestureDetector(
                  onTap: () {
                    final Map<String,dynamic>? popData = model?.popup?[descItem.event];
                    if(popData==null){
                      return;
                    }
                   final MindRulePopupModel mindRulePopupModel = MindRulePopupModel();
                    mindRulePopupModel.explainTitle = popData?['explainTitle']??'';
                    mindRulePopupModel.explain = popData?['explainContent']??'';
                    final Widget contentWidget =
                    MindRulePopWidget(mindRulePopupModel);
                    final DialogFlutterView dialogFlutterView =
                    DialogFlutterView(
                      context: context,
                      contentWidget: contentWidget,
                      popConfig: FlutterPopConfig(
                          popTitle: mindRulePopupModel.explainTitle,
                          popBtnTitle: '确定',
                          popHeight:
                          MediaQuery
                              .of(context)
                              .size
                              .height * 1 / 2),
                    );
                    dialogFlutterView.showPop();
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    child: Row(
                      children: <Widget>[
                        Text(
                          descItem.jumpText!,
                          style: TextStyle(fontSize: 12),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Container(margin:EdgeInsets.only(left: 3),child: rightArrowSmall),
                      ],
                    ),
                  ),
                ),
              Container(
                width: 0.5,
                color: Color(0xFFEBEDF0), //
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _handleJumpUrlEvent(String url) {
    // Implement your navigation to the URL here
    print('Navigating to URL: $url');
  }

  void _handlePopEvent(String event) {
    // Implement your pop event handling here
    print('Handling pop event: $event');
  }
}

import 'dart:io';

import 'package:fbridge/fbridge.dart';
import 'package:fbroadcast/fbroadcast.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../custom_widget/null_widget.dart';
import '../../track/exposure_container.dart';
import '../../utils/TextConfig.dart';
import '../../utils/common_config.dart';
import 'model.dart';
import 'notifier.dart';
import 'package:fround_image/fround_image.dart';
class BottomBarWidget extends StatefulWidget {
  const BottomBarWidget({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _BottomBarState();
}

class _BottomBarState extends State<BottomBarWidget> {
  BottomBarChangeNotifier? changeNotifier;
  BuyBannerDataModel? buyBannerDataModel;

  // 是否收藏
  bool? hasSetCollect;

  // 是否请求过收藏按钮，只在首次进入使用
  bool? hasRequestCollect;

  @override
  void initState() {
    super.initState();
    hasSetCollect = false;
    hasRequestCollect = false;
  }

  @override
  Widget build(BuildContext context) {
    changeNotifier ??= Provider.of<BottomBarChangeNotifier>(context);
    buyBannerDataModel = changeNotifier?.buyBannerDataModel;
    // 获取safe底部高度
    double safeBottomHeight = MediaQuery.of(context).padding.bottom;
    if (safeBottomHeight > 18) {
      safeBottomHeight = 18;
    }
    return buyBannerDataModel == null
        ? nullWidget
        : Container(
            // height: 56,
            width: 375,
            padding: EdgeInsets.fromLTRB(
                paddingLeft, 6, paddingRight, 6 + safeBottomHeight),
            decoration: const BoxDecoration(
              color: Color(0xffffffff),
              boxShadow: <BoxShadow>[
                BoxShadow(
                  color: Color.fromRGBO(0, 0, 0, 0.03), // 阴影的颜色及透明度
                  spreadRadius: 2.5, // 阴影的扩散范围
                  blurRadius: 0.5, // 阴影的模糊范围
                ),
              ],
            ),
            // child: nullWidget,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                _buildAcitons(),
                _buildBuy(),
              ],
            ),
          );
  }

  /// 左侧按钮，咨询、店铺、收藏
  Widget _buildAcitons() {
    final PromoType promoType = changeNotifier!.getPromoType();
    final List<Widget> actions = <Widget>[];
    final bool hasMoreData = hasMoreDataLogic();
    final bool isSaveMoney = promoType == PromoType.saveMoney;
    final bool isMemberMall = promoType == PromoType.memberMall;
    if (isMemberMall) {
      actions.add(_buildMall());
      actions.add(_buildWangWang(itemType: promoType));
    } else {
      if (!isSaveMoney || !hasMoreData) {
        actions.add(_buildWangWang(itemType: promoType));
      }
      if (!isSaveMoney || !hasMoreData) {
        actions.add(_buildShop());
      }
      actions.add(_buildCollect(context));
    }
    return Container(
      child: Row(
        children: actions,
      ),
    );
  }

  /// 左侧单个按钮，上边图片加下边标题
  Widget _buildAction(String title, String? icon, void Function()? action,
      {bool needBorderRadius = false}) {
    return Container(
        padding: EdgeInsets.only(right: 14, left: 14),
        child: GestureDetector(
          onTap: action,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              icon != null
                  ? ClipRRect(
                      borderRadius: needBorderRadius
                          ? BorderRadius.circular(10.5)
                          : BorderRadius.circular(0),
                      child: FRoundImage.network(
                        icon,
                        fit: BoxFit.cover,
                        width: 21,
                        height: 21,
                      ))
                  : Container(),
              SizedBox(height: 3),
              Text(title,
                  style: TextStyle(color: Color(0xFF5C5F66), fontSize: 10)),
            ],
          ),
        ));
  }

  /// 右侧按钮，加购、立即购买、开通会员、大促预热
  Widget _buildBuy() {
    final List<Widget> buttons = <Widget>[];
    final PromoType itemType = changeNotifier!.getPromoType();

    String buttonText = '正在加载...'; //'立即购买';
    String? spmd = '';
    String? buttonSubText;
    bool enable = false;
    dynamic? style;
    dynamic? singleColor;
    Color? textColor;
    Function? action;
    // 获取购买按钮数据
    final Map<String, dynamic> buyButtonsData =
        changeNotifier!.getBuyButtonData(itemType);
    // 获取buyButtonsData内的buttonList数组数据，并遍历buttonList数组，找到status等于buyButtonsData['currentStatus']的数据
    final List<Map<String, dynamic>>? buttonList = buyButtonsData['buttonList'];
    if (buttonList != null) {
      for (final Map<String, dynamic> button in buttonList) {
        if (button['status'] == buyButtonsData['currentStatus']) {
          spmd = buyButtonsData['spmD'];
          buttonText = button['text'] ?? button['defaultText'] ?? '立即购买';
          enable = button['enable'] != null &&
              button['enable'] == true &&
              button['actionType'] != null &&
              button['actionType'] == 'true';
          style = changeNotifier!.getStyle(button)['backgroundImage'];
          textColor = changeNotifier!.getStyle(button)['textColor'];
          buttonSubText = button['buttonSubText'];
          // 处理action
          action = () {
            changeNotifier!.remindAction(button['actionType'], context,
                (int status) {
              // 切换状态
              setState(() {
                buyBannerDataModel!.preSellWarmButton?['currentStatus'] =
                    status;
              });
            });
          };
          break;
        }
      }
    } else {
      spmd = buyButtonsData['spmD'];
      buttonText = buyButtonsData['buttonText'] ??
          buyButtonsData['defaultText'] ??
          '立即购买';
      enable =
          buyButtonsData['enable'] != null && buyButtonsData['enable'] == true;
      style = buyButtonsData['style'];
      buttonSubText = buyButtonsData['buttonSubText'];
      action = buyButtonsData['action'];
    }
    if (buttonText.isEmpty) {
      buttonText = '立即购买';
    }
    if (buyBannerDataModel!.isLoading) {
      buttonText = '正在加载...';
    }
    final bool hideCart = <PromoType>[
          PromoType.outside,
          PromoType.preWarm,
          PromoType.preSell,
          PromoType.redeem,
          PromoType.single,
          PromoType.singleMember,
          PromoType.memberMall
        ].contains(itemType) ||
        buyBannerDataModel!.isLoading;
    final String? cartSubTitle = changeNotifier!.itemDetailModel.data['price']
        ?['buttonPriceTxt']?['cartPriceSubtitle'];
    String cartTitle = buyBannerDataModel?.carDesc ?? '加入购物车';
    if (cartTitle.isEmpty) {
      cartTitle = '加入购物车';
    }
    final bool carButtonSupport = buyBannerDataModel?.carButtonSupport ?? false;
    final bool buyButtonSupport =
        (buyBannerDataModel?.buyButtonSupport ?? false) &&
            action != null &&
            !buyBannerDataModel!.isLoading;
    if (hideCart) {
      buttons.add(Expanded(
          child: getActionButton(
        colors: style ??
            <Color>[
              // 渐变颜色数组
              Color(0xffFE560A), // 开始颜色
              Color(0xffFF7A00), // 结束颜色
            ],
        title: buttonText,
        subTitle: buttonSubText,
        textColor: textColor ??
            (buyButtonSupport ? Color(0xFFFFFFFF) : Color(0x80FFFFFF)),
        radius: BorderRadius.all(
          Radius.circular(20),
        ),
        spmD: 'buy',
        action: () {
          if (action != null) {
            action();
          }
        },
      )));
      // 隐藏购物车
      return Expanded(
        child: Row(mainAxisAlignment: MainAxisAlignment.end, children: buttons),
      );
    } else {
      buttons.add(Expanded(
          child: getActionButton(
        textColor: carButtonSupport ? textColor : const Color(0x80FFFFFF),
        colors: <Color>[
          const Color(0xffFFC500), // 开始颜色
          const Color(0xffFF9402),
        ],
        radius: const BorderRadius.only(
            topLeft: Radius.circular(20), bottomLeft: Radius.circular(20)),
        title: cartTitle,
        subTitle: cartSubTitle,
        spmD: 'cart',
        action: () {
          if (carButtonSupport) {
            changeNotifier?.addCarClick('cart');
          }
        },
      )));
      buttons.add(Expanded(
          child: getActionButton(
        colors: style ??
            <Color>[
              // 渐变颜色数组
              Color(0xffFE560A), // 开始颜色
              Color(0xffFF7A00), // 结束颜色
            ],
        title: buttonText,
        subTitle: buttonSubText,
        textColor: textColor ??
            (buyButtonSupport ? Color(0xFFFFFFFF) : Color(0x80FFFFFF)),
        radius: BorderRadius.only(
            topRight: Radius.circular(20), bottomRight: Radius.circular(20)),
        spmD: 'buy',
        action: () {
          if (action != null) {
            action();
          }
        },
      )));
      return Expanded(
        child: Row(mainAxisAlignment: MainAxisAlignment.end, children: buttons),
      );
    }
  }

  // 右侧单个按钮
  Widget getActionButton(
      {EdgeInsetsGeometry? padding,
      required List<Color> colors,
      Color? textColor,
      required BorderRadiusGeometry radius,
      required String title,
      String? subTitle,
      String? spmD,
      required void Function()? action}) {
    final String spmCD = 'bottomBar.$spmD';
    final String controlName = 'bottomBar_$spmD';

    changeNotifier!.ctrlExposure(context, spmCD, <String, String>{});

    return GestureDetector(
        onTap: () {
          changeNotifier!
              .ctrlClicked(context, spmCD, controlName, <String, String>{});
          if (action != null) {
            action();
          }
        },
        child: Container(
          height: 42,
          padding: EdgeInsets.only(top: Platform.isIOS?1.5:0),
          decoration: BoxDecoration(
            borderRadius: radius,
            gradient: LinearGradient(
              colors: colors,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              FittedBox(
                  child: Text(title,
                      style: TextStyle(
                          color: textColor ?? Color(0xFFFFFFFF),
                          fontSize: buyBannerDataModel!.isLoading
                              ? 16
                              : title.length > 6
                                  ? 12
                                  : title.length > 5
                                      ? 14
                                      : 16,
                          fontWeight: FontWeightExt.bold,
                          height: 1))),
              if (subTitle != null && subTitle != '')
                FittedBox(
                    child: Text(subTitle,
                        style: TextStyle(
                            color: textColor ?? Color(0xFFFFFFFF),
                            fontSize: 10))),
            ],
          ),
        ));
  }

  Widget getSingleColorActionButton(
      {EdgeInsetsGeometry? padding,
      required Color color,
      required Color textColor,
      required BorderRadiusGeometry radius,
      required String title,
      String? subTitle,
      required void Function()? action}) {
    return GestureDetector(
        onTap: action,
        child: Container(
          height: 40,
          padding: padding ?? EdgeInsets.only(left: 8, right: 8),
          decoration: BoxDecoration(borderRadius: radius, color: color),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              FittedBox(
                  child: Text(title,
                      style: TextStyle(
                          color: textColor,
                          fontSize: title.length > 6
                              ? 12
                              : title.length > 5
                                  ? 14
                                  : 16))),
              if (subTitle != null)
                FittedBox(
                    child: Text(subTitle,
                        style: TextStyle(color: textColor, fontSize: 10))),
            ],
          ),
        ));
  }

  ///旺旺按钮
  Widget _buildWangWang({required PromoType itemType}) {
    final String? sellerName = buyBannerDataModel?.sellerName;
    final SellerContact? sellerContact = buyBannerDataModel?.sellerContact;
    final bool hasMoreData = hasMoreDataLogic();

    final bool visible = !hasMoreData ||
        (sellerContact?.alimeFromUrl != null || sellerName != null);
    final Map<String, dynamic>? consultData =
        changeNotifier!.itemDetailModel.data['sellerConsult']?['data'] ??
            <String, dynamic>{};
    if (!visible) {
      return nullWidget;
    }
    final String consultIcon = consultData?['serviceIcon'] != null
        ? '${consultData?['serviceIcon']}_60x60.jpg'
        : '';

    final bool newRoute =
        changeNotifier!.itemDetailModel.itemModel?.routeNewDetail ?? false;

    bool isConsultIcon = false;
    String? wwIcon;

    if (itemType == PromoType.memberMall) {
      wwIcon = bottomIconS['mallWangang'];
    } else if (consultIcon.isNotEmpty) {
      wwIcon = consultIcon;
      isConsultIcon = true;
    } else if (newRoute == true) {
      wwIcon = bottomIconS['consult'];
      isConsultIcon = true;
    } else {
      wwIcon = bottomIconS['ww'];
    }

    final String title = consultData?['consultTitle'] ??
        (newRoute != null && newRoute == true ? '咨询' : '客服');

    changeNotifier!.ctrlExposure(context, 'bottomBar.ww', <String, String>{});

    return _buildAction(title, wwIcon, () {
      changeNotifier!.ctrlClicked(
          context, 'bottomBar.ww', 'bottombar_ww', <String, String>{});
      changeNotifier!.openWW(context);
    }, needBorderRadius: isConsultIcon);
  }

  ///收藏按钮
  Widget _buildCollect(BuildContext context) {
    final String? itemId = changeNotifier?.itemDetailModel.itemModel?.itemId;
    if (itemId == null) {
      return Container();
    }
    // 检查一次是否收藏
    if (hasRequestCollect == false) {
      final Map<String, dynamic> params = <String, dynamic>{
        'bizId': itemId,
        'bizType': 'item'
      };
      // 通过mtop检查是否收藏
      changeNotifier
          ?.requestIsItemCollected(context, params: params)
          .then((Map<String, dynamic> result) {
        hasRequestCollect = true;
        //刷新页面
        if (result['result']?['status'] != null &&
            result['result']['status'] == 1) {
          setState(() {
            hasSetCollect = true;
          });
        } else {
          setState(() {
            hasSetCollect = false;
          });
        }
      }).catchError((dynamic error) {
        setState(() {
          hasRequestCollect = true;
          hasSetCollect = false;
        });
      });
    }

    changeNotifier
        ?.ctrlExposure(context, 'bottomBar.add_favor', <String, String>{});

    return _buildAction('收藏',
        hasSetCollect! ? bottomIconS['collected'] : bottomIconS['collect'], () {
      changeNotifier?.ctrlClicked(context, 'bottomBar.add_favor',
          'bottombar_add_favor', <String, String>{});
      if (hasSetCollect! == false) {
        final Map<String, dynamic> params = <String, dynamic>{
          'bizId': itemId,
          'bizType': 'item'
        };
        final Map<String, dynamic>? data = changeNotifier?.itemDetailModel.data;
        // 收藏mtop，跟h5逻辑一致
        if (data != null) {
          if (data['title']?['data']?['itemTitle'] != null) {
            params['title'] = data['title']?['data']?['itemTitle'];
          }
          if (data['price']?['couponPrice']?['priceText'] != null) {
            params['price'] =
                double.parse(data['price']?['couponPrice']?['priceText']) * 100;
          }
          if (data['price']?['price']?['priceText'] != null) {
            params['originalPrice'] =
                double.parse(data['price']?['price']?['priceText']) * 100;
          }
          if (data['title']?['data']?['shareInfo']?['image_url'] != null) {
            params['picUrl'] =
                data['title']?['data']?['shareInfo']?['image_url'];
          } else if (data['banner']?['data']?['pics']?[0] != null) {
            params['picUrl'] = data['banner']?['data']?['pics']?[0];
          }
          params['jumpUrl'] =
              'https://market.m.taobao.com/app/trip/rx-travel-detail/pages/index?id=$itemId&titleBarHidden=2&disableNav=YES';
          params['client'] = 'fliggy';
          changeNotifier
              ?.requestAddItemCollect(context, params: params)
              .then((Map<String, dynamic> result) {
            //刷新页面
            if (result['result'] != null &&
                result['result']['success'] == true) {
              FBroadcast.instance(changeNotifier?.uniqueId)
                  .broadcast('cardAndCollectPop', value: <String, dynamic>{
                'message': result['result']['msg'] ?? '已加入到我的收藏'
              });
              setState(() {
                hasSetCollect = true;
              });
            }
          }).catchError((dynamic error) {
            print(error);
          });
        }
      } else {
        final Map<String, dynamic> params = <String, dynamic>{
          'bizId': itemId,
          'bizType': 'item'
        };
        changeNotifier
            ?.requestRemoveItemCollect(context, params: params)
            .then((Map<String, dynamic> result) {
          //刷新页面
          if (result['result'] != null && result['result']['success'] == true) {
            FBridgeApi.newInstance(context).callSafe(
              'toast',
              <String, dynamic>{'message': result['result']['msg'] ?? '已取消收藏'},
            );
            setState(() {
              hasSetCollect = false;
            });
          }
        }).catchError((dynamic error) {
          print(error);
        });
      }
    });
  }

  ///店铺按钮
  Widget _buildShop() {
    final Map<String, dynamic>? seller =
        changeNotifier!.itemDetailModel.data['seller'];
    final String? taoShopUrl = seller?['taoShopUrl'];
    final bool? closeShop = seller?['closeShop'];
    final bool hasMoreData = hasMoreDataLogic();

    final bool visible =
        !hasMoreData || taoShopUrl != null && closeShop == false;

    if (visible) {
      changeNotifier!
          .ctrlExposure(context, 'bottomBar.shop', <String, String>{});

      return _buildAction('店铺', bottomIconS['shop'], () {
        changeNotifier?.ctrlClicked(
            context, 'bottomBar.shop', 'bottombar_shop', <String, String>{});
        if (taoShopUrl != null) {
          changeNotifier?.pushToPage(context, taoShopUrl, name: 'shop');
        }
      });
    } else {
      return Container();
    }
  }

  ///商城按钮
  Widget _buildMall() {
    final String? mallHome = changeNotifier!.itemDetailModel
        .data['memberHomepage']['data']['memberMallHomepageUrl'];
    final Widget widget = _buildAction('商城', bottomIconS['mallHome'], () {
      if (mallHome != null) {
        changeNotifier?.pushToPage(context, mallHome);
      }
    });
    return widget;
  }

  bool hasMoreDataLogic() {
    return changeNotifier!.itemDetailModel.itemModel?.hasMoreData != null &&
        changeNotifier!.itemDetailModel.itemModel!.hasMoreData!;
  }
}

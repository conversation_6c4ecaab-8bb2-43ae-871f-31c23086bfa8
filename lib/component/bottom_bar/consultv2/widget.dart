/*    sellerConsultV2: {
      data: {
        consultDesc: '咨询服务',
        consultTitle: '咨询',
        guideList: [
          {
            guideTitle: '您好，我可以向您介绍云南旅游的基本情况您好，我可以向您介绍云南旅游的基本情况您好，我可以向您介绍云南旅游的基本情况您好，我可以向您介绍云南旅游的基本情况',
            guideWords: [
              {
                guideId: 1,
                guideQuestion: '可以解说下云南的旅游攻略吗',
                guideUrl:
                  'https://market.m.taobao.com/app/tb-chatting/tbms-chat/pages/index?appkey=25067504&targetType=7&targetUid=2206451543671&targetId=cntaobao西安顺鑫旅游莲湖专营店&bizType=11001&fliggyParams=biztype,shopDetail;autoSend,false;guideId,1;cityName,云南',
                guideWord: '旅游攻略',
              },
              {
                guideId: 1,
                guideQuestion: '可以解说下云南的旅游攻略吗',
                guideUrl:
                  'https://market.m.taobao.com/app/tb-chatting/tbms-chat/pages/index?appkey=25067504&targetType=7&targetUid=2206451543671&targetId=cntaobao西安顺鑫旅游莲湖专营店&bizType=11001&fliggyParams=biztype,shopDetail;autoSend,false;guideId,1;cityName,云南',
                guideWord: '旅游攻略',
              },
              {
                guideId: 1,
                guideQuestion: '可以解说下云南的旅游攻略吗',
                guideUrl:
                  'https://market.m.taobao.com/app/tb-chatting/tbms-chat/pages/index?appkey=25067504&targetType=7&targetUid=2206451543671&targetId=cntaobao西安顺鑫旅游莲湖专营店&bizType=11001&fliggyParams=biztype,shopDetail;autoSend,false;guideId,1;cityName,云南',
                guideWord: '旅游攻略',
              },
              {
                guideId: 1,
                guideQuestion: '可以解说下云南的旅游攻略吗',
                guideUrl:
                  'https://market.m.taobao.com/app/tb-chatting/tbms-chat/pages/index?appkey=25067504&targetType=7&targetUid=2206451543671&targetId=cntaobao西安顺鑫旅游莲湖专营店&bizType=11001&fliggyParams=biztype,shopDetail;autoSend,false;guideId,1;cityName,云南',
                guideWord: '旅游攻略',
              },
              {
                guideId: 1,
                guideQuestion: '可以解说下云南的旅游攻略吗',
                guideUrl:
                  'https://market.m.taobao.com/app/tb-chatting/tbms-chat/pages/index?appkey=25067504&targetType=7&targetUid=2206451543671&targetId=cntaobao西安顺鑫旅游莲湖专营店&bizType=11001&fliggyParams=biztype,shopDetail;autoSend,false;guideId,1;cityName,云南',
                guideWord: '旅游攻略',
              },
              {
                guideId: 1,
                guideQuestion: '可以解说下云南的旅游攻略吗',
                guideUrl:
                  'https://market.m.taobao.com/app/tb-chatting/tbms-chat/pages/index?appkey=25067504&targetType=7&targetUid=2206451543671&targetId=cntaobao西安顺鑫旅游莲湖专营店&bizType=11001&fliggyParams=biztype,shopDetail;autoSend,false;guideId,1;cityName,云南',
                guideWord: '旅游攻略',
              },
            ],
            module: 'journey',
          },
          {
            guideTitle: '您好，我可以向您介绍云南旅游的基本情况',
            guideWords: [
              {
                guideId: 1,
                guideQuestion: '可以解说下云南的旅游攻略吗',
                guideUrl:
                  'https://market.m.taobao.com/app/tb-chatting/tbms-chat/pages/index?appkey=25067504&targetType=7&targetUid=2206451543671&targetId=cntaobao西安顺鑫旅游莲湖专营店&bizType=11001&fliggyParams=biztype,shopDetail;autoSend,false;guideId,1;cityName,云南',
                guideWord: '旅游攻略2',
              },
            ],
            module: 'fee',
          },
          {
            guideTitle: '您好，我可以向您介绍云南旅游的基本情况',
            guideWords: [
              {
                guideId: 1,
                guideQuestion: '可以解说下云南的旅游攻略吗',
                guideUrl:
                  'https://market.m.taobao.com/app/tb-chatting/tbms-chat/pages/index?appkey=25067504&targetType=7&targetUid=2206451543671&targetId=cntaobao西安顺鑫旅游莲湖专营店&bizType=11001&fliggyParams=biztype,shopDetail;autoSend,false;guideId,1;cityName,云南',
                guideWord: '旅游攻略3',
              },
            ],
            module: 'recommend',
          },
          {
            guideTitle: '您好，我可以向您介绍云南旅游的基本情况',
            guideWords: [
            ],
            module: 'package',
          },
        ],
        itemId: ************,
        nick: '西安顺鑫旅游莲湖专营店',
        serviceDesc: '立即提问了解更多行程内容 有问必答',
        serviceIcon: 'https://gw.alicdn.com/imgextra/i3/O1CN012ZO3cD1ilyllYBUZA_!!6000000004454-2-tps-200-200.png',
        shopId: 241619447,
        showContactLayer: true,
      },
    },*/

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../custom_widget/null_widget.dart';
import 'model.dart';
import 'notifier.dart';

class ConsultV2Widget extends StatelessWidget {
  const ConsultV2Widget({Key? key}) : super(key: key);


  @override
  Widget build(BuildContext context) {
    final BottomBarConsultV2ChangeNotifier changeNotifier = Provider.of<BottomBarConsultV2ChangeNotifier>(context);
    final BottomBarConsultV2Model? buyBannerConsultV2Model = changeNotifier.itemDetailModel.buyBannerConsultV2Model;

    if (buyBannerConsultV2Model == null ) {
      return const SizedBox.shrink();
    }

    return nullWidget;
    // return ValueListenableBuilder<double>(valueListenable: changeNotifier.itemDetailModel.bottomConsultV2Offset, builder: (BuildContext context, double offet, Widget? child){
    //   // if (offet > 1000) {
    //   //   return Container(
    //   //     height: 50,
    //   //     width: 375,
    //   //     color: Color(0x50FF0000),
    //   //     child: Text(
    //   //         '测试会动的咨询条'
    //   //     ),
    //   //   );
    //   // } else {
    //   //   return const SizedBox.shrink();
    //   // }
    // });
  }
}

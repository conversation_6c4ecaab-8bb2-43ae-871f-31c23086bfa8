import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../render/component/component.dart';
import '../../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 底部购物条
class BottomBarConsultV2Component extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<BottomBarConsultV2ChangeNotifier>.value(
      value: BottomBarConsultV2ChangeNotifier(context),
      child: const ConsultV2Widget(),
    );
  }
}


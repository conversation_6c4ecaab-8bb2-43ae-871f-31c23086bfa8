import 'package:fliggy_router/fliggy_router.dart';
import '../../utils/safe_access.dart';
import 'package:fliggy_mtop/fliggy_mtop.dart';
import 'package:flutter/cupertino.dart';
class BottomBarService {
  /// 设置提醒mtop
  static Future<Map<String, dynamic>> requestRemind(BuildContext context,
      {required Map<String, dynamic> params}) async {
    const String apiName = 'mtop.trip.fcecore.api.appointment.reserve';
    const String apiVersion = '1.0';
    final Map<String, dynamic> _requestParams = <String, dynamic>{
    'loading': true,
    'silent': true,
    'needLogin': true,
    };
    _requestParams.addEntries(params.entries);

    final MtopRequestModel request = MtopRequestModel.buildRequset(
        api: apiName,
        method: 'POST',
        needLogin: true,
        version: apiVersion,
        params: _requestParams);
    Map<String, dynamic> requestdata = <String, dynamic>{};
    await FliggyMtopApi.getInstance().send(context, request,
        successHandler: (MtopResponseModel responseModel) {
      requestdata =
          SafeAccess.safeParseMap(responseModel.data);
      requestdata.addAll(<String, dynamic>{'success': true});
    }, errorHandler: (MtopResponseModel responseModel) {
      print(responseModel);
    });
    return requestdata;
  }
  /// 取消收藏mtop
  static Future<Map<String, dynamic>> requestRemoveItemCollect(BuildContext context,
      {required Map<String, dynamic> params}) async {
    const String apiName = 'mtop.trip.merge.shopping.del';
    const String apiVersion = '1.0';
    final Map<String, dynamic> _requestParams = <String, dynamic>{
    'loading': true,
    'needLogin': true,
    };
    _requestParams.addEntries(params.entries);

    final MtopRequestModel request = MtopRequestModel.buildRequset(
        api: apiName,
        method: 'POST',
        needLogin: true,
        version: apiVersion,
        params: _requestParams);
    Map<String, dynamic> requestdata = <String, dynamic>{};
    await FliggyMtopApi.getInstance().send(context, request,
        successHandler: (MtopResponseModel responseModel) {
      requestdata =
          SafeAccess.safeParseMap(responseModel.data);
      requestdata.addAll(<String, dynamic>{'success': true});
    }, errorHandler: (MtopResponseModel responseModel) {
      print(responseModel);
    });
    return requestdata;
  }
  /// 收藏mtop
  static Future<Map<String, dynamic>> requestAddItemCollect(BuildContext context,
      {required Map<String, dynamic> params}) async {
    const String apiName = 'mtop.trip.merge.shopping.add';
    const String apiVersion = '1.0';
    final Map<String, dynamic> _requestParams = <String, dynamic>{
    'loading': true,
    'needLogin': true,
    };
    _requestParams.addEntries(params.entries);

    final MtopRequestModel request = MtopRequestModel.buildRequset(
        api: apiName,
        method: 'POST',
        needLogin: true,
        version: apiVersion,
        params: _requestParams);
    Map<String, dynamic> requestdata = <String, dynamic>{};
    await FliggyMtopApi.getInstance().send(context, request,
        successHandler: (MtopResponseModel responseModel) {
      requestdata =
          SafeAccess.safeParseMap(responseModel.data);
    }, errorHandler: (MtopResponseModel responseModel) {
      print(responseModel);
    });
    return requestdata;
  }
  /// 是否收藏mtop
  static Future<Map<String, dynamic>> requestIsItemCollected(BuildContext context,
      {required Map<String, dynamic> params}) async {
    const String apiName = 'mtop.trip.merge.shopping.query';
    const String apiVersion = '1.0';
    final Map<String, dynamic> _requestParams = <String, dynamic>{
    'loading': true,
    'needLogin': true,
    };
    _requestParams.addEntries(params.entries);
    final MtopRequestModel request = MtopRequestModel.buildRequset(
        api: apiName,
        method: 'POST',
        needLogin: true,
        version: apiVersion,
        params: _requestParams);
    Map<String, dynamic> requestdata = <String, dynamic>{};
    await FliggyMtopApi.getInstance().send(context, request,
        successHandler: (MtopResponseModel responseModel) {
      requestdata =
          SafeAccess.safeParseMap(responseModel.data);
      requestdata.addAll(<String, dynamic>{'success': true});
    }, errorHandler: (MtopResponseModel responseModel) {
      print(responseModel);
    });
    return requestdata;
  }
}
  
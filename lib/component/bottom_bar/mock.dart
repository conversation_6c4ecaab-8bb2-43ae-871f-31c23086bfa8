// ignore_for_file: strict_raw_type, always_specify_types

Map mockData = {
  'buyBanner': {
      'tag': 'buyBanner',
      'data': {
        'buyButtonSupport': true,
        'shopIcon': 'shopIcon',
        'carDesc': '加入购物车',
        'carType': 0,
        'sellerId': '2856437246',
        'sellerContact': {
          'itemPic': 'https://img.alicdn.com/bao/uploaded/i1/6000000004163/O1CN01nLj3mB1gchnJzoiFr_!!6000000004163-0-itemdesc.jpg',
          'itemUrl': 'https://h5.m.taobao.com/trip/travel-detail/index/index.html?id=779622447397',
          'sellerId': '2856437246',
          'alimeFromUrl': 'https://ai-alimebot.fliggy.com/intl/index.htm?from=fz4umqb6NC0e&sellerId=2856437246&itemId=779622447397&orderId=779622447397&cateId=*********',
          'sellerNick': '飞猪度假官方旗舰店',
          'itemTitle': '[上海迪士尼度假区-亲子套票]2大1小/1大1小可选'
        },
        'sellerName': '飞猪度假官方旗舰店',
        'singleBuy': false,
        'internationalItem': false,
        'h5Iframe': false,
        'carButtonStyle': 'NORMAL_CAR',
        'buyButtonStyle': 'NORMAL_BUY_V1',
        'atmosphereStatus': 0,
        'skuBizDomain': 0,
        'carButtonSupport': false,
        'saveIcon': 'SaveIcon',
        'buttonShowState': 0,
        'shopJumpInfo': {
          'showBuyButton': true,
          'jumpNative': false,
          'jumpH5Url': 'https://fliggyshop.m.taobao.com/wireless/route?wx_navbar_transparent=true&wx_navbar_hidden=true&titleBarHidden=2&disableNav=YES&bizCode=hotel&bizId=2856437246&routePath=index',
          'count': 0,
          'exExtendParams': {},
          'extJumpUrlParams': {}
        },
        'buyButtonDesc': '立即购买',
        'buyJumpInfo': {
          'newBuy': true,
          'exExtendParams': {},
          'showBuyButton': true,
          'jumpH5Url': 'https://tripbuy.fliggy.com/router/confirmRouterH5.htm?fpt=abfpt_sword(7a470b3ada3cd574)pageStrategyId(62)index_type(normal)abfpt_fsd(flutter)fsk(4888764570ec6fc)&categoryId=*********',
          'extJumpUrlParams': {},
          'buyUrlType': 0,
          'count': 0,
          'jumpNative': false,
          'newJumpH5Url': 'https://market.m.taobao.com/app/trip/h5-buy-new/pages/confirm/index.html?_fli_webview=true&ttidable=true&_fli_online=true&fpt=abfpt_sword(7a470b3ada3cd574)pageStrategyId(62)index_type(normal)abfpt_fsd(flutter)fsk(4888764570ec6fc)&categoryId=*********&subBizType=ticket'
        },
        'skuH5Url': 'https://market.m.taobao.com/app/trip/rx-sku-picker/pages/pad?nativeData=1&id=779622447397',
        'carBizType': 2
      }
    }
};

Map mockDataPreSell = {
  'buyBanner': {
      'tag': 'buyBanner',
      'data': {
  'currentStatus': '',
  'preSellWarmButton': {
    'activityId': '12345678_735966460502',
    'activityStatus': 1,
    'activityType': 'DETAIL',
    'buttonList': [
      {
        'actionType': 1,
        'bgLeftColor': '#bb31ff',
        'bgRightColor': '#9113ff',
        'buttonStyle': 'NORMAL_BUY',
        'enable': true,
        'status': 1,
        'text': '设置开抢提醒',
        'textColor': '#ffffff'
      },
      {
        'bgLeftColor': '#80bb31ff',
        'bgRightColor': '#809113ff',
        'buttonStyle': 'DISABLE_BUY',
        'enable': false,
        'status': 2,
        'text': '已设置提醒',
        'textColor': '#80ffffff'
      }
    ],
    'currentStatus': 1,
    'remindTime': '2023-10-01 00:00:00',
    'subSceneId': '02',
    'templateId': 'INTF_PT_detail_remind',
    'toastSuccessTips': '添加成功,将通过app提醒'
  }
  }
}
};
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 底部购物条
class BottomBarComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<BottomBarChangeNotifier>.value(
      value: BottomBarChangeNotifier(context),
      child: const BottomBarWidget(),
    );
  }
}
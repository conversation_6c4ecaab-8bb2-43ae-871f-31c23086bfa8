import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../render/component/component.dart';
import '../../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 底部咨询条
class BottomBarConsultComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<BottomBarConsultChangeNotifier>.value(
      value: BottomBarConsultChangeNotifier(context),
      child: const ConsultWidget(),
    );
  }
}


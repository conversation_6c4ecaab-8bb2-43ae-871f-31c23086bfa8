
import 'package:flutter/material.dart';

import '../../../render/component/component_change_notifier.dart';
import '../../../render/component/component_context.dart';
import 'package:fliggy_router/fliggy_router.dart';

import 'model.dart';

class BottomBarConsultChangeNotifier extends ComponentChangeNotifier {
  BottomBarConsultChangeNotifier(ComponentContext componentContext) : super(componentContext);

  BottomBarConsultModel? get buyBannerDataModel => itemDetailModel.buyBannerConsultModel;
  ///打开旺旺
  void openWW(BuildContext context) {
      final String nick = buyBannerDataModel!.nick ?? itemDetailModel.data['seller']?['sellerNick'] ?? '';
      if (nick.isEmpty) {
        return;
      }
      final String? itemId = itemDetailModel.itemModel!.itemId;
      if (itemId == null) {
        return;
      }
      String url = 'https://market.m.taobao.com/app/tb-chatting/tbms-chat/pages/index';
      // https://aliyuque.antfin.com/xxhz/vlmdhm/zupml7?singleDoc#
      url += '?appkey=25067504';
      url += '&targetId=cntaobao$nick';
      url += '&targetType=3';
      url += '&bizType=11001';
      url += '&itemId=$itemId';
      url += '&itemid=$itemId'; // https://aliyuque.antfin.com/trip_plat/pu6xpg/qc93o27aq9gy9zou?singleDoc#
      url += '&source=DETAIL';
      pushToPage(context, url);
      // todo query数据
  }

  ///跳转页面
  void pushToPage(BuildContext context, String pageName,
      {String? name}) {
    // todo 埋点
    FliggyNavigatorApi.getInstance().push(context, pageName, anim: Anim.slide);
  }
}

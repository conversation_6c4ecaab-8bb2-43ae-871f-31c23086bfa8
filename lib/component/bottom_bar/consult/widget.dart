import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'model.dart';
import 'notifier.dart';
import 'package:fround_image/fround_image.dart';
import 'package:ficonfont/ficonfont.dart' as FiconFont;

class ConsultWidget extends StatefulWidget {
  const ConsultWidget({Key? key}) : super(key: key);

  @override
  State<ConsultWidget> createState() => _ConsultWidgetState();
}

class _ConsultWidgetState extends State<ConsultWidget> {
  /// 是否手动关闭了
  bool clickClose = false;

  @override
  Widget build(BuildContext context) {
    final BottomBarConsultChangeNotifier changeNotifier =
        Provider.of<BottomBarConsultChangeNotifier>(context);
    final BottomBarConsultModel? buyBannerConsultModel =
        changeNotifier.itemDetailModel.buyBannerConsultModel;

    if (buyBannerConsultModel == null ||
        (buyBannerConsultModel!.showContactLayer != null &&
            !buyBannerConsultModel!.showContactLayer!)) {
      return const SizedBox.shrink();
    }

    return ValueListenableBuilder<double>(
        valueListenable: changeNotifier.itemDetailModel.bottomConsultV2Offset,
        builder: (BuildContext context, double offet, Widget? child) {
          if (offet > 1000 && !clickClose) {
            const String spmCD = 'consult.chat';
            const String controlName = 'consult_chat';

            changeNotifier!.ctrlExposure(context, spmCD, <String, String>{});

            return GestureDetector(
              onTap: () {
                changeNotifier.openWW(context);
                changeNotifier.ctrlClicked(
                    context, spmCD, controlName, <String, String>{});
              },
              child: Container(
                width: 375,
                height: 50,
                // padding: EdgeInsets.all(16),
                color: Color(0xFFFFFFFF),
                // decoration: BoxDecoration(
                //   color: Colors.grey[200],
                //   borderRadius: BorderRadius.circular(20),
                // ),
                child: Row(
                  children: <Widget>[
                    // 头像
                    if (buyBannerConsultModel?.serviceIcon != null)
                      Container(
                        margin: EdgeInsets.only(left: 15),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15)),
                        clipBehavior: Clip.hardEdge,
                        width: 30,
                        height: 30,
                        child: FRoundImage.network(
                            buyBannerConsultModel?.serviceIcon! ?? ''),
                      ),
                    SizedBox(width: 12),
                    // 文字内容
                    // if (buyBannerConsultModel?.serviceDesc != null)
                    Container(
                      height: 30,
                      width: 228,
                      decoration: BoxDecoration(
                          color: Color(0xFFDDDDDD),
                          borderRadius: BorderRadius.circular(15)),
                      child: Padding(
                        padding: EdgeInsets.only(top: 7, left: 9),
                        child: Text(
                          buyBannerConsultModel?.serviceDesc ?? '随时问我~',
                          style: TextStyle(
                            color: Color(0xFF919499),
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                    // 咨询服务图标
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        FRoundImage.network(
                          'https://gw.alicdn.com/imgextra/i1/O1CN010u5i7i1yiLlrC59DO_!!6000000006612-2-tps-200-200.png',
                          height: 15,
                        ),
                        Text(
                          buyBannerConsultModel?.consultDesc ?? '咨询服务',
                          style: const TextStyle(
                            color: Color(0xFF919499),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(width: 8),
                    // 关闭按钮
                    Align(
                        alignment: Alignment.topCenter,
                        child: GestureDetector(
                          onTap: () {
                            clickClose = true;
                            setState(() {});
                          },
                          child: Container(
                            // height: 30,
                            // width: 30,
                            child: FiconFont.Ficon(
                                0xe9a8,
                                // 0xeb22,
                                18,
                                const Color(0xFF919499)),
                          ),
                        ))
                  ],
                ),
              ),
            );
          } else {
            return const SizedBox.shrink();
          }
        });
  }
}

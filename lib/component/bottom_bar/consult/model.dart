import 'package:fjson_exports/fjson_exports.dart';

class BottomBarConsultModel {
  BottomBarConsultModel();

  factory BottomBarConsultModel.fromJson (Map<String, dynamic> data) {
    final BottomBarConsultModel bottomBarConsultModel = BottomBarConsultModel();

    final Map<String, dynamic> sellerConsult = safeNonNullMap(data['sellerConsult'], (dynamic e) => e);
    final Map<String, dynamic> sellerConsultData = safeNonNullMap(sellerConsult['data'], (dynamic e) => e);
    bottomBarConsultModel.consultDesc = safeString(sellerConsultData['consultDesc']);
    bottomBarConsultModel.consultTitle = safeString(sellerConsultData['consultTitle']);
    bottomBarConsultModel.itemId = safeString(sellerConsultData['itemId']);
    bottomBarConsultModel.nick = safeString(sellerConsultData['nick']);
    bottomBarConsultModel.serviceDesc = safeString(sellerConsultData['serviceDesc']);
    bottomBarConsultModel.serviceIcon = safeString(sellerConsultData['serviceIcon']);
    bottomBarConsultModel.shopId = safeInt(sellerConsultData['shopId']);
    bottomBarConsultModel.showContactLayer = safeBool(sellerConsultData['showContactLayer']);

    return bottomBarConsultModel;
  }


  String? consultDesc;
  String? consultTitle;
  String? itemId;
  String? nick;
  String? serviceDesc;
  String? serviceIcon;
  int? shopId;
  bool? showContactLayer;

}

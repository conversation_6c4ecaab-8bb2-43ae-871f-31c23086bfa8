import '../../utils/safe_access.dart';

class BuyBannerDataModel {
  int? atmosphereStatus;
  int? buttonShowState;
  String? buyButtonDesc;
  String? buyButtonStyle;
  bool? buyButtonSupport;
  BuyJumpInfo? buyJumpInfo;
  int? carBizType;
  String? carButtonStyle;
  bool? carButtonSupport;
  String? carDesc;
  CarItemJumpInfo? carItemJumpInfo;
  int? carType;
  bool? h5Iframe;
  bool? internationalItem;
  String? saveIcon;
  SellerContact? sellerContact;
  String? sellerId;
  String? sellerName;
  String? shopIcon;
  ShopJumpInfo? shopJumpInfo;
  bool? singleBuy;
  int? skuBizDomain;
  String? skuH5Url;
  // 预售按钮
  String advanceButtonDesc = '';
  bool advanceButtonSupport = false;
  // 预热按钮展示信息
  Map<String, dynamic>? preSellWarmButton;
  Map<String, dynamic>? bottomButtonInfo;

  String? memberButtonDesc;
  String? memberButtonSubDesc;
  bool memberButtonSupport = false;
  String? memberButtonUrl;
  bool isLoading = false;
  ///sku提示小黄条
  String? yellowTips;
  String? yellowTipsJumpUrl;


  BuyBannerDataModel.fromJson(Map<dynamic, dynamic> dataModel) {
    if (dataModel.containsKey('isCache')) {
      isLoading = true;
    }
    final Map<String,dynamic>? buyBanner =  dataModel['buyBanner'];
    final Map<String,dynamic> json = SafeAccess.safeParseMap(buyBanner?['data']);
    atmosphereStatus = SafeAccess.safeParseInt(json['atmosphereStatus']);
    buttonShowState = SafeAccess.safeParseInt(json['buttonShowState']);
    buyButtonDesc = SafeAccess.safeParseString(json['buyButtonDesc']);
    buyButtonStyle = SafeAccess.safeParseString(json['buyButtonStyle']);
    buyButtonSupport = SafeAccess.safeParseBoolean(json['buyButtonSupport']);
    buyJumpInfo = json['buyJumpInfo'] != null
        ? BuyJumpInfo.fromJson(SafeAccess.safeParseMap(json['buyJumpInfo']))
        : null;
    carBizType = SafeAccess.safeParseInt(json['carBizType']);
    carButtonStyle = SafeAccess.safeParseString(json['carButtonStyle']);
    carButtonSupport = SafeAccess.safeParseBoolean(json['carButtonSupport']);
    carDesc = SafeAccess.safeParseString(json['carDesc']);
    carItemJumpInfo = json['carItemJumpInfo'] != null
        ? CarItemJumpInfo.fromJson(
            SafeAccess.safeParseMap(json['carItemJumpInfo']))
        : null;
    carType = SafeAccess.safeParseInt(json['carType']);
    h5Iframe = SafeAccess.safeParseBoolean(json['h5Iframe']);
    internationalItem = SafeAccess.safeParseBoolean(json['internationalItem']);
    saveIcon = SafeAccess.safeParseString(json['saveIcon']);
    sellerContact = json['sellerContact'] != null
        ? SellerContact.fromJson(SafeAccess.safeParseMap(json['sellerContact']))
        : null;
    sellerId = SafeAccess.safeParseString(json['sellerId']);
    sellerName = SafeAccess.safeParseString(json['sellerName']);
    shopIcon = SafeAccess.safeParseString(json['shopIcon']);
    shopJumpInfo = json['shopJumpInfo'] != null
        ? ShopJumpInfo.fromJson(SafeAccess.safeParseMap(json['shopJumpInfo']))
        : null;
    singleBuy = SafeAccess.safeParseBoolean(json['singleBuy']);
    skuBizDomain = SafeAccess.safeParseInt(json['skuBizDomain']);
    skuH5Url = json['skuH5Url'];
    advanceButtonDesc = SafeAccess.safeParseString(json['advanceButtonDesc']);
    advanceButtonSupport = SafeAccess.safeParseBoolean(json['advanceButtonSupport']);
    preSellWarmButton = json['preSellWarmButton'] != null
        ? SafeAccess.safeParseMap(json['preSellWarmButton'])
        : null;
    bottomButtonInfo = json['bottomButtonInfo'] != null
            ? SafeAccess.safeParseMap(json['bottomButtonInfo'])
            : null;
  }
}

class BuyJumpInfo {
  int? buyUrlType;
  int? count;
  String? jumpH5Url;
  bool? jumpNative;
  bool? newBuy;
  String? newJumpH5Url;
  bool? showBuyButton;

  BuyJumpInfo.fromJson(Map<String, dynamic> json) {
    buyUrlType = SafeAccess.safeParseInt(json['buyUrlType']);
    count = SafeAccess.safeParseInt(json['count']);
    jumpH5Url = SafeAccess.safeParseString(json['jumpH5Url']);
    jumpNative = SafeAccess.safeParseBoolean(json['jumpNative']);
    newBuy = SafeAccess.safeParseBoolean(json['newBuy']);
    newJumpH5Url = SafeAccess.safeParseString(json['newJumpH5Url']);
    showBuyButton = SafeAccess.safeParseBoolean(json['showBuyButton']);
  }
}

class CarItemJumpInfo {
  int? count;
  String? jumpH5Url;
  bool? showBuyButton;

  CarItemJumpInfo.fromJson(Map<String, dynamic> json) {
    count = SafeAccess.safeParseInt(json['count']);
    jumpH5Url = SafeAccess.safeParseString(json['jumpH5Url']);
    showBuyButton = SafeAccess.safeParseBoolean(json['showBuyButton']);
  }
}

class SellerContact {
  String? itemPic;
  String? itemTitle;
  String? itemUrl;
  String? sellerId;
  String? sellerNick;
  String? alimeFromUrl;

  SellerContact.fromJson(Map<String, dynamic> json) {
    itemPic = SafeAccess.safeParseString(json['itemPic']);
    itemTitle = SafeAccess.safeParseString(json['itemTitle']);
    itemUrl = SafeAccess.safeParseString(json['itemUrl']);
    sellerId = SafeAccess.safeParseString(json['sellerId']);
    sellerNick = SafeAccess.safeParseString(json['sellerNick']);
    alimeFromUrl = SafeAccess.safeParseString(json['alimeFromUrl']);
  }
}

class ShopJumpInfo {
  int? count;
  String? jumpH5Url;
  bool? jumpNative;
  bool? showBuyButton;

  ShopJumpInfo.fromJson(Map<String, dynamic> json) {
    count = SafeAccess.safeParseInt(json['count']);
    jumpH5Url = SafeAccess.safeParseString(json['jumpH5Url']);
    jumpNative = SafeAccess.safeParseBoolean(json['jumpNative']);
    showBuyButton = SafeAccess.safeParseBoolean(json['showBuyButton']);
  }
}
/// 活动类型
enum PromoType {
  normal,// 基础
  preWarm,// 大促预热
  preSell,  // 大促预售
  member,  // 会员专享
  singleMember,  // 独立会员按钮
  redeem,  // 99优惠
  single,  // 单按钮
  saveMoney,  // 省钱卡
  outside,  // 微信定制
  memberMall  // 会员商城
}

/// 获取活动类型
PromoType getPromoType(String type) {
  switch (type) {
    case 'preWarm':
      return PromoType.preWarm;
    case 'preSell':
      return PromoType.preSell;
    case 'member':
      return PromoType.member;
    case 'singleMember':
      return PromoType.singleMember;
    case 'redeem':
      return PromoType.redeem;
    case 'single':
      return PromoType.single;
    case 'saveMoney':
      return PromoType.saveMoney;
    case 'outside':
      return PromoType.outside;
    case 'memberMall':
      return PromoType.memberMall;
    default:
      return PromoType.normal;
  }
}
/// 底部icon
Map<String, String> bottomIconS = <String ,String>{
  // 会员商品客服

  'mallWangang': 'https://gw.alicdn.com/imgextra/i4/O1CN01mxbxWN1RGyicqOWul_!!6000000002085-0-tps-59-54.jpg',
  // 会员商品首页
  'mallHome': 'https://gw.alicdn.com/imgextra/i2/O1CN01xJUApI1svHC7GuOKj_!!6000000005828-0-tps-54-54.jpg',
  // 店铺
  'shop': 'https://gw.alicdn.com/imgextra/i3/O1CN019XWhA01GCtTYJQs5b_!!6000000000587-2-tps-96-96.png',
  // 客服
  'ww': 'https://gw.alicdn.com/imgextra/i1/O1CN01eNScCM1Xg4AcAdyw3_!!6000000002952-2-tps-96-96.png',
  // 收藏
  'collect': 'https://gw.alicdn.com/imgextra/i1/O1CN0161z8031P89vERKoyr_!!6000000001795-2-tps-96-96.png',
  'collected': 'https://gw.alicdn.com/imgextra/i3/O1CN01lP3PvP1ZnVqEN2ZyA_!!6000000003239-2-tps-96-96.png',
  // 线路咨询
  'consult': 'https://gw.alicdn.com/imgextra/i1/O1CN01MfbAgS1NT6W3MTyst_!!6000000001570-2-tps-63-93.png_60x60.jpg',
  // // 分享
  // share: 'https://gw.alicdn.com/imgextra/i2/O1CN01wOklGi23Tl7Tr6MXx_!!6000000007257-2-tps-64-64.png',
  // // 评论
  // comment: 'https://gw.alicdn.com/imgextra/i3/O1CN014o8UJv1tlwgpT2qdT_!!6000000005943-2-tps-64-64.png',
  // // 评分
  // score: 'https://gw.alicdn.com/imgextra/i4/O1CN01gthR4C1NfTO3dkh7B_!!6000000001597-2-tps-64-64.png',
};

// 提醒按钮
enum SpikeBtnStatus {
  reminderNotSet,    // "未设置提醒"
  reminderSet,       // "已设置提醒"
  exchange,          // "立即兑换（有券）"
  exchangeWithoutCoupon,  // "立即兑换（无券）"
  exchangeStopped,   // "兑换结束"
}

/// 获取活动类型
SpikeBtnStatus getSpikeBtnStatus(int type) {
  switch (type) {
    case 1:
      return SpikeBtnStatus.reminderNotSet;
    case 2:
      return SpikeBtnStatus.reminderSet;
    case 3:
      return SpikeBtnStatus.exchange;
    case 4:
      return SpikeBtnStatus.exchangeWithoutCoupon;
    case 5:
      return SpikeBtnStatus.exchangeStopped;
    default:
      return SpikeBtnStatus.reminderNotSet;
  }
}
/// 按钮事件类型
enum BuyAction {
  mtop,
  popupWindow,
  buyNow,
}
BuyAction getAction(int type) {
  switch (type) {
    case 1:
      return BuyAction.mtop;
    case 2:
      return BuyAction.popupWindow;
    case 3:
      return BuyAction.buyNow;
    default:
      return BuyAction.mtop;
  }
}




import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../render/component/component.dart';
import '../../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 底部购物条
class BottomBarHitComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<BottomBarHitChangeNotifier>.value(
      value: BottomBarHitChangeNotifier(context),
      child: const BottomBarHitWidget(),
    );
  }
}


import 'package:fbridge/fbridge.dart';
import 'package:fbroadcast/fbroadcast.dart';
import 'package:ffloat/ffloat.dart';
import 'package:flutter/cupertino.dart';

import '../../../data/model/item_model.dart';
import '../../../render/component/component_change_notifier.dart';
import '../../../render/component/component_context.dart';
import '../../normal_title/model.dart';
import '../../rate_and_share/model.dart';
import 'model.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:fjson_exports/fjson_exports.dart';
import 'package:intl/intl.dart';

class BottomBarHitChangeNotifier extends ComponentChangeNotifier {
  BottomBarHitChangeNotifier(ComponentContext componentContext)
      : super(componentContext);

  BottomBarHitModel? get bottomBarHitModel => itemDetailModel.bottomBarHitModel;

  NormalTitleDataModel? get titleModel => itemDetailModel.normalTitleDataModel;

  late Function(BuildContext context) click;

  // 检查日历+检查预定
  void checkRemind(BuildContext context) {
    final Map<String, dynamic> globalData = itemDetailModel.data;

    final Map<String, dynamic> buyBanner =
        safeNonNullMap(globalData['buyBanner'], (dynamic e) => e);
    final Map<String, dynamic> buyBannerData =
        safeNonNullMap(buyBanner['data'], (dynamic e) => e);
    final Map<String, dynamic> subscribeVO =
        safeNonNullMap(buyBannerData['subscribeVO'], (dynamic e) => e);
    // 检查服务端的订阅状态
    if (subscribeVO.containsKey('subscribStatus')) {
      bottomBarHitModel?.backEndRemind =
          safeNonNullBool(subscribeVO['subscribStatus']);
    }

    final List<DateTime> datalist = getCurrentAndNextMonthTime();
    // 检查本地日历状态,检查一个月以内的日期
    FBridgeApi.newInstance(null)
        .callSafe('check_batch_calendar', <String, dynamic>{
      'batch': <dynamic>[
        <String, dynamic>{
          'calendar_start_datetime': formateDate(datalist[0]),
          'calendar_end_datetime': formateDate(datalist[1]),
          'calendar_title': subscribeVO['calendarTitle']
        }
      ],
      'request_permission': true
    }).then((Map<dynamic, dynamic> value) {
      final List<dynamic> batch =
          safeNonNullList(value['batch'], (dynamic e) => e);
      if (batch.isEmpty) {
        bottomBarHitModel!.calendarRemind = false;
        bottomBarHitModel!.calendarMatch = false;
        setButtonText(context);
        return;
      }

      final Map<String, bool> checkResult = checkCalendarEvents(
          batch, formateDate(datalist[0]), formateDate(datalist[1]));
      if (checkResult.containsKey('hasSet')) {
        bottomBarHitModel!.calendarRemind = checkResult['hasSet']!;
      }
      if (checkResult.containsKey('allMatch')) {
        bottomBarHitModel!.calendarMatch = checkResult['allMatch']!;
      }

      setButtonText(context);
    });
    setButtonText(context);
  }

  // 根据数据修改展示文案
  void setButtonText(BuildContext context) {
    if (!bottomBarHitModel!.remindData && !bottomBarHitModel!.subscribe) {
      bottomBarHitModel?.buttonText.value = '';
      return;
    }
    if (!bottomBarHitModel!.backEndRemind &&
        !bottomBarHitModel!.calendarRemind) {
      const String spmCD = 'bottomBar.subscriberBanner_subscribe';
      const String controlName = 'subscriberBanner_subscribe';
      bottomBarHitModel?.buttonText.value = '提醒我';
      bottomBarHitModel?.icon.value = 'https://gw.alicdn.com/imgextra/i2/O1CN01Qv8vKe1X7iPq4tTvL_!!6000000002877-2-tps-29-36.png';
      click = click = (BuildContext context) {
        jumpRemind(context);
        ctrlClicked(context, spmCD, controlName, <String, String>{});
      };
      ctrlExposure(context, spmCD, <String, String>{});
    } else if ((bottomBarHitModel!.calendarRemind &&
            bottomBarHitModel!.calendarRemind) ||
        bottomBarHitModel!.backEndRemind) {
      const String spmCD = 'bottomBar.subscriberBanner_cancel';
      const String controlName = 'subscriberBanner_cancel';
      bottomBarHitModel?.buttonText.value = '取消提醒';
      bottomBarHitModel?.icon.value = '';
      click = click = (BuildContext context) {
        cancelRemind(context);
        ctrlClicked(context, spmCD, controlName, <String, String>{});
      };
      ctrlExposure(context, spmCD, <String, String>{});
    } else {
      bottomBarHitModel?.buttonText.value = '更新提醒';
      bottomBarHitModel?.icon.value = '';
      const String spmCD = 'bottomBar.subscriberBanner_update';
      const String controlName = 'subscriberBanner_update';
      ctrlExposure(context, spmCD, <String, String>{});
    }
  }


  // 订阅
  void jumpRemind(BuildContext context, {String? customMessage}) {
    FliggyNavigatorApi.getInstance()
        .push(context, bottomBarHitModel?.url ?? '', anim: Anim.none)
        .then((Map<dynamic, dynamic> value) {
      print(value);
      final Map<String, dynamic> back =
          safeNonNullMap(value['backdata'], (dynamic e) => e);
      final Map<String, dynamic> backdata =
          safeNonNullMap(back['data'], (dynamic e) => e);
      final bool isSubscribeSuccess =
          safeNonNullBool(backdata['isSubscribeSuccess']);
      checkRemind(context);

      if (customMessage != null) {
        if (isSubscribeSuccess) {
          FBridgeApi.newInstance(null).toast(customMessage);
          return;
        }
      }
      final bool isBizSuccess = safeNonNullBool(backdata['isBizSuccess']);
      if (isBizSuccess) {
        // 打开右上角提醒
        // final FFloatController? floatController = itemDetailModel.floatController;
        // floatController?.show();
        FBroadcast.instance(uniqueId).broadcast('cardAndCollectPop',
            value: <String, dynamic>{'message': '已收藏该商品\n将准时提醒你开抢'});
        return;
      }

      final String subscribeMsg = safeNonNullString(backdata['subscribeMsg']);
      if (isSubscribeSuccess && subscribeMsg.isNotEmpty) {
        FBridgeApi.newInstance(null).toast(subscribeMsg);
      }
    });
  }

  // 取消订阅
  void cancelRemind(BuildContext context) {
    final List<DateTime> dataRange = getCurrentAndNextMonthTime();
    FBridgeApi.newInstance(context)
        .callSafe('cancel_component_subscription', <String, dynamic>{
      'subscription_id': bottomBarHitModel?.subscriptionId,
      'child_code': bottomBarHitModel?.codes,
      'types': '1,2',
      'need_biz': false,
      'calendar_event_title': bottomBarHitModel?.calendarTitle,
      'calendar_event_start_ms': dataRange[0].millisecondsSinceEpoch,
      'calendar_event_end_ms': dataRange[1].millisecondsSinceEpoch,
    }).then((Map<dynamic, dynamic> value) {
      print(value);
      final bool isCancelCalendar = safeNonNullBool(value['isCancelCalendar']);
      final bool isSubscribeSuccess =
          safeNonNullBool(value['isSubscribeSuccess']);
      if (isCancelCalendar && isSubscribeSuccess) {
        bottomBarHitModel?.calendarRemind = false;
        bottomBarHitModel?.calendarMatch = false;
        bottomBarHitModel?.backEndRemind = false;
        setButtonText(context);
        FBridgeApi.newInstance(null).toast('已取消提醒');
      } else {
        // todo:埋点
        FBridgeApi.newInstance(null).toast('取消失败，请重试');
      }
    });
  }

  // 更新订阅
  void onUpdateRemind (BuildContext context) {

  }

  // 日历检查的回调
  Map<String, bool> checkCalendarEvents(
      List<dynamic>? batch, String start, String end) {
    final List<dynamic> checkDays = batch ?? <dynamic>[];
    bool allMatch = true; // 先设置为 true，遍历全部，再和是否有设置过与运算
    bool hasSet = false;

    // 检查是否当前系统日历内的和之前设置的一样
    // 这个过滤完两个数据永远是 true...,暂时不影响,H5 也是这样的,后面再优化吧
    for (final dynamic day in checkDays) {
      final Map<String, dynamic> n = safeNonNullMap(day, (dynamic e) => e);
      // 过滤脏数据
      if (n == null ||
          n['calendar_start_datetime'] == null ||
          n['calendar_end_datetime'] == null) {
        continue;
      }
      hasSet = true;
      allMatch = allMatch &&
          n['calendar_start_datetime'] == start &&
          n['calendar_end_datetime'] == end;
    }

    return <String, bool>{
      'allMatch': allMatch && hasSet,
      'hasSet': hasSet,
    };
  }

  // 获取日历检查的时间
  List<DateTime> getCurrentAndNextMonthTime() {
    // 获取当前时间
    final DateTime now = DateTime.now();

    // 获取一个月后的时间
    final DateTime nextMonth = now.add(Duration(days: 30)); // 使用30天作为一个月的近似值

    return <DateTime>[now, nextMonth];
  }

  /// 将日期格式化成 yyyyMMddhhmmss
  String formateDate(DateTime date) {
    final DateFormat formatter = DateFormat('yyyyMMddHHmmss');
    return formatter.format(date);
  }
}

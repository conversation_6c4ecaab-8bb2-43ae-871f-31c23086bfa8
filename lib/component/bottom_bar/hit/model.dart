import 'package:fjson_exports/fjson_exports.dart';
import 'package:flutter/material.dart';

import '../../../utils/safe_access.dart';

class BottomBarHitModel {
  BottomBarHitModel();

  ValueNotifier<String> icon = ValueNotifier<String>('');


  ValueNotifier<String> buttonText = ValueNotifier<String>('');

  // String buttonText = '';

  Color bgColor = SafeAccess.hexColor('#FFF7F2');
  
  Color textColor = const Color(0xFFFFFFFF);

  String title = '';

  String? calendarStartTime;


  String? calendarShowTitle;

  /// 
  String? calendarEndTime;

  String? url;

  /// 日历中是否有提醒
  bool calendarRemind = false;

  /// 日历中提醒是否对应商品提醒
  bool calendarMatch = false;

  String? calendarTitle;

  /// 取消订阅时使用
  String codes = '';

  /// 取消订阅时使用
  int subscriptionId = 0;

  /// 服务端是否有订阅
  bool backEndRemind = false;

  /// 是否需要展示订阅按钮
  bool remindData = false;
  bool subscribe = false;

  factory BottomBarHitModel.fromJson(Map<String, dynamic> data) {
    final BottomBarHitModel bottomBarHitModel = BottomBarHitModel();

    final Map<String, dynamic> trade =
        safeNonNullMap(data['trade'], (dynamic e) => e);

    final Map<String, dynamic> hintBanner =
        safeNonNullMap(trade['hintBanner'], (dynamic e) => e);

    if (hintBanner.containsKey('buttonTextColor')) {
      bottomBarHitModel.bgColor =
          SafeAccess.hexColor(safeNonNullString(hintBanner['buttonTextColor']));
    }


    if (hintBanner.containsKey('subscribe')) {
      if (safeNonNullBool(hintBanner['subscribe'])) {
        bottomBarHitModel.subscribe = true;
        bottomBarHitModel.bgColor = const Color(0xFFffa402).withOpacity(0.6);
      } else {
        bottomBarHitModel.bgColor = SafeAccess.hexColor(safeString(hintBanner['bgColor']));
        bottomBarHitModel.textColor = SafeAccess.hexColor(safeString(hintBanner['textColor']));
      }
    }

    if (hintBanner.containsKey('text')) {
      bottomBarHitModel.title = safeNonNullString(hintBanner['text']);
    }

    final Map<String, dynamic> buyBanner =
        safeNonNullMap(data['buyBanner'], (dynamic e) => e);
    final Map<String, dynamic> buyBannerData =
        safeNonNullMap(buyBanner['data'], (dynamic e) => e);
    final Map<String, dynamic> subscribeVO =
        safeNonNullMap(buyBannerData['subscribeVO'], (dynamic e) => e);

    bottomBarHitModel.remindData = safeNonNullBool(buyBannerData['subscribeVO']);

    if (subscribeVO.containsKey('url')) {
      bottomBarHitModel.url = safeNonNullString(subscribeVO['url']);
    }

    bottomBarHitModel.calendarTitle = safeString(subscribeVO['calendarTitle']);

    bottomBarHitModel.calendarStartTime = safeString(subscribeVO['calendarStartTime']);

    bottomBarHitModel.calendarShowTitle = safeString(subscribeVO['calendarShowTitle']);

    bottomBarHitModel.calendarEndTime = safeString(subscribeVO['calendarEndTime']);

    bottomBarHitModel.codes = safeNonNullString(subscribeVO['codes']);
    bottomBarHitModel.subscriptionId = safeNonNullInt(subscribeVO['subscriptionId']);
    return bottomBarHitModel;
  }
}

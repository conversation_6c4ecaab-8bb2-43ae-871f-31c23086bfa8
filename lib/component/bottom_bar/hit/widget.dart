import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'model.dart';
import 'notifier.dart';
import 'package:fround_image/fround_image.dart';

class BottomBarHitWidget extends StatelessWidget {
  const BottomBarHitWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final BottomBarHitChangeNotifier changeNotifier =
        Provider.of<BottomBarHitChangeNotifier>(context);
    final BottomBarHitModel? buyBannerHitModel =
        changeNotifier.itemDetailModel.bottomBarHitModel;

    if (buyBannerHitModel == null) {
      return const SizedBox.shrink();
    }

    // 检查状态
    changeNotifier.checkRemind(context);
    return Container(
      height: 50,
      width: 375,
      color: buyBannerHitModel.bgColor,//.withOpacity(0.75),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Text(
            buyBannerHitModel.title,
            style: TextStyle(color: buyBannerHitModel.textColor,//Color(0xFFFFFFFF),
                fontSize: 14),
          ),
          GestureDetector(
              onTap: () {
                changeNotifier.click(context);
              },
              child: buyBannerHitModel.buttonText.value.isNotEmpty
                  ? Container(
                      height: 26,
                      padding: EdgeInsets.symmetric(horizontal: 15),
                      margin: EdgeInsets.only(left: 20),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(13),
                        color: const Color(0xFFFFFFFF),
                      ),
                      child: Row(
                        children: <Widget>[
                          ValueListenableBuilder<String>(
                            valueListenable: buyBannerHitModel.icon,
                            builder: (BuildContext context, String text,
                                Widget? child) {
                              if (buyBannerHitModel.icon.value.isEmpty) {
                                return const SizedBox.shrink();
                              }
                              return FRoundImage.network(
                                buyBannerHitModel.icon.value,
                                height: 12,
                                width: 10,
                              );
                            },
                          ),
                          ValueListenableBuilder<String>(
                            valueListenable: buyBannerHitModel.buttonText,
                            builder: (BuildContext context, String text,
                                Widget? child) {
                              return Padding(
                                  padding: EdgeInsets.only(
                                      left: buyBannerHitModel.icon.value.isEmpty
                                          ? 0
                                          : 6),
                                  child: Text(
                                    text,
                                    style: TextStyle(
                                      fontSize: 13,
                                    ),
                                  ));
                            },
                          ),
                        ],
                      ),
                    )
                  : const SizedBox.shrink())
        ],
      ),
    );
  }
}

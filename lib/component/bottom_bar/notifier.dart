
import 'package:fbridge/fbridge.dart';
import 'package:flutter/cupertino.dart';

import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import '../../sku/sku_data_manager.dart';
import '../../utils/common_util.dart';
import 'model.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'service.dart';

class BottomBarChangeNotifier extends ComponentChangeNotifier {
  BuyBannerDataModel? get buyBannerDataModel =>
      itemDetailModel.buyBannerDataModel;

  //mock专用 BuyBannerDataModel.fromJson(mockDataPreSell);
  BottomBarChangeNotifier(ComponentContext context) : super(context);

  ///立即购买
  void buyNowClick(String? spmD) {
    if (buyBannerDataModel?.buyButtonSupport ?? false) {
      itemDetailEngine.skuManager.buyNow(skuType: SkuDataManager.skuBuyKey,enterTypeFrom: SkuDataManager.buyBannerBuy);
    }
  }

  ///加入购物车
  void addCarClick(String? spmD) {
    itemDetailEngine.skuManager.buyNow(skuType: SkuDataManager.skuCartKey,enterTypeFrom: SkuDataManager.buyBannerAddCart);
  }

  ///跳转页面
  void pushToPage(BuildContext context, String pageName,
      {PromoType? promoType, String? name}) {
    // todo 埋点
    FliggyNavigatorApi.getInstance().push(context, pageName);
  }

  ///打开旺旺
  void openWW(BuildContext context) {
    final String? wwUrl = buyBannerDataModel!.sellerContact?.alimeFromUrl;
    if (wwUrl != null && wwUrl.length > 5) {
      pushToPage(context, wwUrl);
    } else {
      final String nick = buyBannerDataModel!.sellerName ??
          itemDetailModel.data['seller']?['sellerNick'] ??
          '';
      if (nick.isEmpty) {
        return;
      }
      final String? itemId = itemDetailModel.itemModel!.itemId;
      if (itemId == null) {
        return;
      }
      String url =
          'https://market.m.taobao.com/app/tb-chatting/tbms-chat/pages/index';
      url += '?appkey=25067504';
      url += '&targetId=cntaobao$nick';
      url += '&targetType=7';
      url += '&bizType=11001';
      url += '&itemId=$itemId';
      pushToPage(context, url);
      // todo query数据
    }
    // 打开旺旺
    // openWW(opt = {}) {
    //   const wwUrl = pageApis.getGlobalData('buyBanner.data.sellerContact.alimeFromUrl');
    //   if (wwUrl && !env.isByteDanceMicroApp) {
    //     pageApis.openUrl(wwUrl);
    //   } else {
    //     const nick = pageApis.getGlobalData('buyBanner.data.sellerName') || pageApis.getGlobalData('seller.sellerNick');
    //     const { itemId } = pageApis.getItemInfo();
    //     if (!nick) {
    //       return;
    //     }
    //     const query = getParam();
    //     pageApis.openUrl({
    //       ...(opt || {}),
    //       url: 'https://market.m.taobao.com/app/tb-chatting/tbms-chat/pages/index',
    //       // 手淘轻应用内，禁止走轻应用路由否则进不去native旺旺
    //       disableTaobaoLiteRoute: true,
    //       params: {
    //         appkey: '25067504',
    //         targetId: `cntaobao${nick}`,
    //         targetType: 7,
    //         bizType: '11001',
    //         itemId,
    //         ttid: query.ttid,
    //         fpt: query.fpt,
    //       },
    //     });
    //   }
    // },
  }

  /// 获取当前页面促销类型
  PromoType getPromoType() {
    final Map<String, dynamic>? itemFeatureMap =
        itemDetailModel.data['trackParams']?['itemFeatureMap'] ??
            <String, dynamic>{};
    if (itemFeatureMap!.isEmpty) {
      return PromoType.normal;
    }
    if (itemDetailModel.itemModel != null &&
        itemDetailModel.itemModel!.memberMallItem != null &&
        itemDetailModel.itemModel!.memberMallItem! == true) {
      return PromoType.memberMall;
    }

    // 大促预热
    if (isTrue(itemFeatureMap['preSellWarmBottomBtn']) &&
        !isTrue(itemFeatureMap['f3MemberBottomBtn'])) {
      return PromoType.preWarm;
    }

    // 大促预售
    if (isTrue(itemFeatureMap['preSellBottomBtn']) &&
        !isTrue(itemFeatureMap['f3MemberBottomBtn'])) {
      return PromoType.preSell;
    }

    // 独立会员按钮
    if (isTrue(itemFeatureMap['singleMemberBottomBtn'])) {
      return PromoType.singleMember;
    }

    // 会员专享
    if (isTrue(itemFeatureMap['memberBottomBtn']) ||
        isTrue(itemFeatureMap['f3MemberBottomBtn'])) {
      return PromoType.member;
    }

    // 99优惠
    if (isTrue(itemFeatureMap['fliggySeckillingItem']) ||
        isTrue(itemFeatureMap['dreamCouponExchangeItem'])) {
      return PromoType.redeem;
    }

    // 省钱卡
    if (isTrue(itemFeatureMap['fliggyServiceCardItem'])) {
      return PromoType.saveMoney;
    }

    // 单按钮
    if (isTrue(itemFeatureMap['singleBottomBtn'])) {
      return PromoType.single;
    }
    return PromoType.normal;
  }

  bool isTrue(dynamic a) {
    return '$a' == 'true';
  }

  /// 获取购买按钮信息
  Map<String, dynamic> getBuyButtonData(PromoType promoType) {
    if (buyBannerDataModel == null) {
      return <String, dynamic>{};
    }
    final bool buyButtonSupport =
        buyBannerDataModel?.buyButtonSupport != null &&
            buyBannerDataModel!.buyButtonSupport! == true;
    switch (promoType) {
      case PromoType.preWarm:
        {
          final String? buttonSubText = itemDetailModel.data['price']
              ?['buttonPriceTxt']?['advancePriceSubtitle'];
          final List<Color> colors = <Color>[
            Color(0xffB930FF),
            Color(0xff9113ff),
          ];
          final Map<String, dynamic> data = <String, dynamic>{
            'spmD': 'pre-warm',
            'defaultText': '付定金',
            'buttonList': buyBannerDataModel!.preSellWarmButton?['buttonList'],
            'currentStatus':
                buyBannerDataModel!.preSellWarmButton?['currentStatus'],
            'buttonSubText': buttonSubText,
            'style': colors
          };
          return data;
        }
      case PromoType.preSell: //大促预售
        {
          final String? buttonSubText = itemDetailModel.data['price']
              ?['buttonPriceTxt']?['advancePriceSubtitle'];
          final String buyButtonDesc = buyBannerDataModel!.advanceButtonDesc;
          final List<Color> colors = <Color>[
            // 渐变颜色数组
            Color(0xffbb31ff), // 开始颜色
            Color(0xff9113ff), // 结束颜色
          ];
          final Map<String, dynamic> data = <String, dynamic>{
            'spmD': 'pre-sell',
            'buttonText': buyButtonDesc.isEmpty ? '付定金' : buyButtonDesc,
            'buttonSubText': buttonSubText,
            'enable': buyBannerDataModel!.advanceButtonSupport,
            'style': colors,
            'action': () {
              buyNowClick('pre-sell');
            }
          };
          return data;
        }
      case PromoType.redeem:
        {
          final Map<String, dynamic> data = <String, dynamic>{
            'spmD': 'redeem',
            'buttonList': buyBannerDataModel!.bottomButtonInfo?['buttonList'],
            'currentStatus':
                buyBannerDataModel!.bottomButtonInfo?['currentStatus'],
          };
          return data;
        }
      case PromoType.singleMember:
      case PromoType.member:
        {
          final String memberButtonDesc =
              buyBannerDataModel!.memberButtonDesc ?? '一键开通会员';
          final String memberButtonSubDesc =
              buyBannerDataModel!.memberButtonSubDesc ?? '';

          final Map<String, dynamic> data = <String, dynamic>{
            'spmD': 'member',
            'buttonText': memberButtonDesc,
            'buttonSubText': memberButtonSubDesc,
            'enable': buyBannerDataModel!.memberButtonSupport,
            'openUrl': buyBannerDataModel!.memberButtonUrl,
            if (buyBannerDataModel!.memberButtonUrl != null)
              'action': (BuildContext context) {
                pushToPage(context, buyBannerDataModel!.memberButtonUrl!);
              },
          };
          return data;
        }
      case PromoType.memberMall:
        {
          final String? buttonSubText = itemDetailModel.data['price']
              ?['buttonPriceTxt']?['buyPriceSubtitle'];
          final String buyButtonDesc =
              buyBannerDataModel!.buyButtonDesc ?? '立即购买';

          final String spmD = buyButtonSupport ? 'buy' : 'unbuy';
          final Color? singleColor =
              buyButtonSupport ? null : Color(0x80ffffff);
          final Map<String, dynamic> data = <String, dynamic>{
            'spmD': spmD,
            'buttonText': buyButtonDesc,
            'buttonSubText': buttonSubText,
            'enable': buyButtonSupport,
            'singleColor': singleColor,
            'action': () {
              buyNowClick(spmD);
            }
          };
          return data;
        }
      // ignore: no_default_cases
      default:
        {
          final String? buttonSubText = itemDetailModel.data['price']
              ?['buttonPriceTxt']?['buyPriceSubtitle'];
          final String buyButtonDesc =
              buyBannerDataModel!.buyButtonDesc ?? '立即购买';
          final String spmD = buyButtonSupport ? 'buy' : 'unbuy';

          final Map<String, dynamic> data = <String, dynamic>{
            'spmD': spmD,
            'buttonText': buyButtonDesc,
            'buttonSubText': buttonSubText,
            'action': () {
              buyNowClick(spmD);
            }
          };
          return data;
        }
    }
  }

  Map<String, dynamic> getStyle(Map<String, dynamic> data) {
    if (data['bgLeftColor'] != null && data['bgRightColor'] != null) {
      return <String, dynamic>{
        'backgroundImage': <Color>[
          stringToColor(data['bgLeftColor']),
          stringToColor(data['bgRightColor'])
        ],
        'color': data['textColor'] != null
            ? stringToColor(data['textColor'])
            : stringToColor('#ffffff'),
      };
    }
    return <String, dynamic>{};
  }

  /// 返回提醒function，处理提醒loading，等逻辑
  void remindAction(int actionType, BuildContext context,
      void Function(int status) onSuccess) {
    if (getAction(actionType) == BuyAction.mtop) {
      // 秒杀数据
      final Map<String, dynamic>? secKill =
          itemDetailModel.data['seckill']?['data'];
      // 双十一预售预热
      final Map<String, dynamic>? preSellWarm =
          buyBannerDataModel?.preSellWarmButton;
      final Map<String, dynamic>? subMsgArgs = secKill?['subMsgArgs'];

      final Map<String, dynamic> remindData =
          subMsgArgs ?? preSellWarm ?? <String, dynamic>{};

      if (remindData.isEmpty) {
        FBridgeApi.newInstance(context).callSafe(
          'toast',
          <String, dynamic>{'message': '设置提醒失败，请稍后再试'},
        );
        return;
      }
      requestRemind(context, params: remindData)
          .then((Map<String, dynamic> result) => () {
                if (secKill?['toastTips'] != null) {
                  FBridgeApi.newInstance(context).callSafe(
                    'toast',
                    <String, dynamic>{'message': secKill?['toastTips']},
                  );
                } else {
                  FBridgeApi.newInstance(context).callSafe(
                    'toast',
                    <String, dynamic>{'message': '设置提醒成功'},
                  );
                }
                onSuccess(SpikeBtnStatus.reminderSet.index);
              })
          .catchError((dynamic error) {
        FBridgeApi.newInstance(context).callSafe(
          'toast',
          <String, dynamic>{'message': error.toString()},
        );
      });
    } else if (getAction(actionType) == BuyAction.buyNow) {
      buyNowClick('buy-now');
    }
  }

  /// 设置提醒mtop
  Future<Map<String, dynamic>> requestRemind(BuildContext context,
      {required Map<String, dynamic> params}) async {
    return BottomBarService.requestRemind(context, params: params);
  }

  /// 取消收藏mtop
  Future<Map<String, dynamic>> requestRemoveItemCollect(BuildContext context,
      {required Map<String, dynamic> params}) async {
    return BottomBarService.requestRemoveItemCollect(context, params: params);
  }

  /// 收藏mtop
  Future<Map<String, dynamic>> requestAddItemCollect(BuildContext context,
      {required Map<String, dynamic> params}) async {
    return BottomBarService.requestAddItemCollect(context, params: params);
  }

  /// 是否收藏mtop
  Future<Map<String, dynamic>> requestIsItemCollected(BuildContext context,
      {required Map<String, dynamic> params}) async {
    return BottomBarService.requestIsItemCollected(context, params: params);
  }
}

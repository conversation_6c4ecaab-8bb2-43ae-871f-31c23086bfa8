import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class HotelFacilityWidget extends StatelessWidget {
  const HotelFacilityWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final HotelFacilityChangeNotifier changeNotifier =
        Provider.of<HotelFacilityChangeNotifier>(context);
    return Container();
  }
}
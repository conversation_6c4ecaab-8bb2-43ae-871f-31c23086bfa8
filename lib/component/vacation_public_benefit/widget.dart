import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../utils/TextConfig.dart';
import '../../utils/common_config.dart';
import '../../utils/safe_access.dart';
import 'notifier.dart';
import 'package:flutter_common/api/empty.dart';
import 'package:fround_image/fround_image.dart';
class VacationPublicBenefitWidget extends StatelessWidget {
  const VacationPublicBenefitWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VacationPublicBenefitChangeNotifier changeNotifier =
        Provider.of<VacationPublicBenefitChangeNotifier>(context);
    changeNotifier.fromJson();
    final Map<dynamic, dynamic>? moduleData = changeNotifier.moduleData;
    if (moduleData == null) {
      return empty;
    }
    return Container(
        margin: const EdgeInsets.only(bottom: itemDivider),
        decoration: BoxDecoration(
            color: Color(0xffffffff),
            borderRadius: BorderRadius.circular(cardBorderRadius)),
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 12.0),
      child:Row(children: <Widget>[
      FRoundImage.network(
          moduleData['picUrl'],
          width: 44,
          height: 44,
        ),
      const SizedBox(width: 9.0),
      Expanded(child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
        Text(moduleData['title'],style: TextStyle(fontSize: 16,fontWeight: FontWeightExt.bold),),
        _buildContent(moduleData['charityDesc']),
      ],))
    ],));
  }
  Widget _buildContent(List<dynamic> datas) {
    final List <InlineSpan> list = <InlineSpan>[];
    for (final Map<dynamic, dynamic> data in datas) {
      if (data['value'] == null) {
        continue;
      }
      list.add(TextSpan( text:data['value'],style: TextStyle(fontSize: 14,color: data['style'] == null ? Color(0xFFcccccc) : SafeAccess.safeParseColor(data['style']['color']))));
    }
    return RichText(
            text: TextSpan(
              children: list));
  }
}

// ignore_for_file: always_specify_types

const Map<String, dynamic> mockData = {
  'charity': {
    'data': {
      'charityDesc': [
        {
          'type': 'text',
          'value': '商家承诺每笔成交将为'
        },
        {
          'style': {
            'color': '#3D3D3D'
          },
          'type': 'text',
          'value': '乡村阳光童趣园项目'
        },
        {
          'type': 'text',
          'value': '捐赠'
        },
        {
          'style': {
            'color': '#3D3D3D'
          },
          'type': 'text',
          'value': '0.1元'
        },
        {
          'type': 'text',
          'value': '。该商品已累计捐赠'
        },
        {
          'type': 'text',
          'value': '0'
        },
        {
          'type': 'text',
          'value': '笔。'
        }
      ],
      'picUrl': 'https://gw.alicdn.com/imgextra/i4/O1CN014Fhavz1whlnxjOMMT_!!6000000006340-2-tps-85-84.png',
      'title': '公益宝贝'
    }
  }
};

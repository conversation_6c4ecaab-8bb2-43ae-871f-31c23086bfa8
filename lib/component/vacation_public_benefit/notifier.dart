import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import './mock.dart';
class VacationPublicBenefitChangeNotifier extends ComponentChangeNotifier {
  VacationPublicBenefitChangeNotifier(ComponentContext context) : super(context);
  Map<dynamic, dynamic>? moduleData;

  @override
  void fromJson() {
    //mock数据
    // moduleData = mockData['charity']['data'];
    // return;
    if (dataModel == null || dataModel['charity'] == null || dataModel['charity']['data'] == null) {
      return;
    }
    moduleData = dataModel['charity']['data'];
  }
}

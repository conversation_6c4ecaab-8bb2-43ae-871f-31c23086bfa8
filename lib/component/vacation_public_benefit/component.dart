import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description 公益宝贝
class VacationPublicBenefitComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<VacationPublicBenefitChangeNotifier>.value(
      value: VacationPublicBenefitChangeNotifier(context),
      child: const VacationPublicBenefitWidget(),
    );
  }
}
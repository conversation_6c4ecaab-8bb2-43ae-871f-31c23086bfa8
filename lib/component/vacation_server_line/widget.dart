import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ficonfont/ficonfont.dart';
import '../../custom_widget/item_rich_text.dart';
import '../../custom_widget/null_widget.dart';
import '../../custom_widget/service_widget.dart';
import '../../utils/common_config.dart';
import 'model.dart';
import 'notifier.dart';

class VacationServerLineWidget extends StatelessWidget {
  const VacationServerLineWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final VacationServerLineChangeNotifier changeNotifier =
        Provider.of<VacationServerLineChangeNotifier>(context);
    final VacationServerLineDataModel? vacationServerLineDataModel =
        changeNotifier.vacationServerLineDataModel;
    return vacationServerLineDataModel == null ||
            !vacationServerLineDataModel.visible
        ? nullWidget
        : Container(
            margin: const EdgeInsets.only(bottom: itemDivider),
            padding: const EdgeInsets.fromLTRB(
                paddingLeft, paddingTop, paddingRight, paddingBottom),
            decoration: BoxDecoration(
                color: Color(0xffffffff),
                borderRadius: BorderRadius.circular(cardBorderRadius)),
            child: ServiceWidget(
              '服务',
              vacationServerLineDataModel.serviceModels!,
              jumpClick: () {
                changeNotifier.widgetClick(context);
              },
            ),
          );
  }

  Widget _buildRich(Tags tag) {
    final List<ItemRichTextModel> list = <ItemRichTextModel>[];

    if (tag.icon != '') {
      final ItemRichTextModel itemRichTextModel = ItemRichTextModel(
          iconUrl: tag.icon,
          type: ItemRichType.icon,
          style: const ItemRichStyle(height: 14));
      list.add(itemRichTextModel);
    }
    if (tag.subText != '') {
      final ItemRichTextModel itemRichTextModel = ItemRichTextModel(
          text: tag.subText,
          style: const ItemRichStyle(
              textColor: Color.fromARGB(255, 153, 153, 153)));
      list.add(itemRichTextModel);
    }
    if (tag.text != null) {
      final ItemRichTextModel itemRichTextModel = ItemRichTextModel(
          text: tag.text,
          style:
              const ItemRichStyle(textColor: Color.fromARGB(255, 15, 19, 26)));
      list.add(itemRichTextModel);
    }

    return ItemRichWidget(
      list,
    );
  }
}

import 'package:flutter/cupertino.dart';

import '../../custom_widget/dialog_flutter.dart';
import '../../custom_widget/service_widget.dart';
import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';
import '../../utils/safe_access.dart';
import 'model.dart';

class VacationServerLineChangeNotifier extends ComponentChangeNotifier {
  VacationServerLineDataModel? get vacationServerLineDataModel =>
      itemDetailModel.vacationServerLineDataModel;

  VacationServerLineChangeNotifier(ComponentContext context) : super(context);

  ///服务详情
  void widgetClick(BuildContext context) {
    if (vacationServerLineDataModel!.servicePopModels != null &&
        vacationServerLineDataModel!.servicePopModels!.isNotEmpty) {
      final Widget contentWidget = ServicePopHelper.buildPopWidget(vacationServerLineDataModel!.servicePopModels!);
      final DialogFlutterView dialogFlutterView = DialogFlutterView(
          context: context,
          contentWidget: contentWidget,
          popConfig: FlutterPopConfig(
              popTitle: '服务说明',
              popBtnTitle: '确定',
              popHeight: MediaQuery.of(context).size.height * 3 / 4));
      dialogFlutterView.showPop();
    }
  }
}

import '../../custom_widget/service_widget.dart';
import '../../utils/safe_access.dart';

class VacationServerLineDataModel {
  bool get visible => serviceModels != null && serviceModels!.isNotEmpty;
  List<ServicePopModel>? servicePopModels;
  List<ServiceModel>? serviceModels;
  List<Tags>? tags;

  VacationServerLineDataModel.fromJson(Map<String, dynamic> dataModel) {
    final Map<String, dynamic> serviceGuarantee =
        SafeAccess.safeParseMap(dataModel['serviceGuarantee']);
    final Map<String, dynamic> json =
        SafeAccess.safeParseMap(serviceGuarantee['data']);
    if (json['details'] != null) {
      servicePopModels = <ServicePopModel>[];
      SafeAccess.safeParseList(json['details']).forEach((dynamic v) {
        servicePopModels!
            .add(ServicePopModel.fromJson(SafeAccess.safeParseMap(v)));
      });
    }
    if (json['tags'] != null) {
      tags = <Tags>[];
      SafeAccess.safeParseList(json['tags']).forEach((dynamic v) {
        tags!.add(Tags.fromJson(SafeAccess.safeParseMap(v)));
      });
      _createServiceModels();
    }
  }

  void _createServiceModels() {
    serviceModels = <ServiceModel>[];
    for (final Tags tag in tags!) {
      final ServiceModel serviceModel =
          ServiceModel(tag.text, icon: tag.icon, subText: tag.subText);
      serviceModels!.add(serviceModel);
    }
  }
}

class Tags {
  String? subText;
  String? icon;
  String? text;

  Tags.fromJson(Map<String, dynamic> json) {
    subText = SafeAccess.safeParseString(json['subText']);
    icon = SafeAccess.safeParseString(json['icon']);
    text = SafeAccess.safeParseString(json['text']);
  }
}

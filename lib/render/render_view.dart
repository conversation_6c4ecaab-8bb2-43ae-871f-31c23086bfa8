import 'package:fbridge/fbridge.dart';
import 'package:flutter/material.dart';
import '../component/component_key_constant.dart';
import '../custom_widget/null_widget.dart';
import '../page/item_detail_engine.dart';
import 'package:power_scroll_view/power_scroll_view.dart';
import 'package:provider/provider.dart';
import '../page/item_detail_change_notifier.dart';
import 'component/component_widget.dart';
import 'package:fround_image/fround_image.dart';

import 'item_detail_overlay_widget.dart';

class RenderView extends StatelessWidget {
  const RenderView(this.itemDetailEngine, {Key? key}) : super(key: key);

  final ItemDetailEngine itemDetailEngine;

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<ItemDetailChangeNotifier>.value(
      value: itemDetailEngine.pageChangeNotifier,
      child: AppView(itemDetailEngine),
    );
  }
}

//应用视图层，当前主要负责监控入口节点（根节点）的变更。
class AppView extends StatelessWidget {
  AppView(this.itemDetailEngine, {Key? key}) : super(key: key);
  final ItemDetailEngine itemDetailEngine;
  final Map<String, Widget> _cacheComponents = <String, Widget>{};

  Widget createComponentWidget(ItemDetailEngine itemDetailEngine, String id) {
    if (_cacheComponents[id] == null) {
      _cacheComponents[id] = ComponentWidget(itemDetailEngine, id);
    }
    return _cacheComponents[id]!;
  }

  @override
  Widget build(BuildContext context) {
    final ItemDetailChangeNotifier dm =
        Provider.of<ItemDetailChangeNotifier>(context);
    return Stack(
      children: <Widget>[
        Container(
          child: PowerScrollView(
            <PowerSection>[
              PowerSection(
                  contentLayout: PowerLayoutList(
                childCount: () => dm.itemDetailModel.body.length,
                widgetBuilder: (BuildContext context, PowerIndex index) {
                  return createComponentWidget(
                      itemDetailEngine, dm.itemDetailModel.body[index.index]);
                },
                style: PowerLayoutStyleBase(
                  padding: const EdgeInsets.only(bottom: 10.0),
                ),
              ))
            ],
            controller: itemDetailEngine.powerScrollController,
            dataManager: PowerDataManager(),
            physics: const ClampingScrollPhysics(),
//                    pageChangeNotifier: pageChangeNotifier
          ),
        ),

        /// 头部
        Column(
            children: dm.itemDetailModel.header
                .map<Widget>((String node) =>
                    createComponentWidget(itemDetailEngine, node))
                .toList()),

        /// 底部按钮
        Positioned(
            bottom: 0,
            child: Column(
                children: dm.itemDetailModel.footer
                    .map<Widget>((String node) =>
                        createComponentWidget(itemDetailEngine, node))
                    .toList())),

        /// 右下角的返回顶部按钮
        ValueListenableBuilder<double>(
            valueListenable:
                itemDetailEngine.itemDetailModel.bottomConsultV2Offset,
            builder: (BuildContext context, double offset, Widget? child) {
              if (offset > 400) {
                return Positioned(
                  bottom: 140.0,
                  right: 12.0,
                  child: Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    clipBehavior: Clip.hardEdge,
                    child: GestureDetector(
                      onTap: () {
                        itemDetailEngine.scrollToOffset(0);
                      },
                      child: Container(
                          color: Color(0x6FFFFFFF),
                          child: FRoundImage.network(
                              'https://gw.alicdn.com/imgextra/i1/O1CN01eCOXNN1ONRDTMni4q_!!6000000001693-2-tps-264-264.png')),
                    ),
                  ),
                );
              } else {
                return nullWidget;
              }
            }),

        if (itemDetailEngine.itemDetailModel.floatLiveModel != null)
          Positioned(
            top: 130.0,
            right: 0.0,
            child: ComponentWidget(
                itemDetailEngine, ComponentKeyConstant.floatLive),
          )
      ],
    );
  }
}

import 'package:flutter/material.dart';

import 'package:fbridge/fbridge.dart';

class ItemDetailOverlayWidget extends StatefulWidget {

  Widget child;

  ItemDetailOverlayWidget({Key? key, required this.child}) : super(key: key);

  @override
  _ItemDetailOverlayWidgetState createState() {
    return _ItemDetailOverlayWidgetState();
  }
}

class _ItemDetailOverlayWidgetState extends State<ItemDetailOverlayWidget> with FbridgeMixin<ItemDetailOverlayWidget> {

  @override
  Widget build(BuildContext context) {
    return NotificationListener(
      onNotification: (ScrollNotification notification) {
        if (notification is ScrollStartNotification || notification is ScrollUpdateNotification) {
          if (( notification.metrics.axisDirection == AxisDirection.up && notification.metrics.extentAfter == 0.0)
              || (notification.metrics.axisDirection == AxisDirection.down && notification.metrics.extentBefore == 0.0)) {
            bridge.invoke('overlay_scroll_update', {});
          }
        }
        return false;
      },
      child: widget.child,
    );
  }
}
import 'package:flutter/material.dart';
import '../../component/mind_rule/model.dart';
import '../../utils/common_config.dart';
import '../../utils/safe_access.dart';
import 'package:flutter/services.dart';
///先囤后约等营销信息
class SkuMindRuleWidget extends StatelessWidget {
  MindRuleModel? model;

  SkuMindRuleWidget(this.model, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (model == null||model!.mindTagsVO==null) {
      return SizedBox.shrink();
    }
    return Container(
      height: 32,
      decoration: BoxDecoration(
        color: Color(0xFFFFF5EB), // 背景颜色
        borderRadius: BorderRadius.circular(6), // 设置圆角
      ),
      padding: EdgeInsets.only(left: 9, right: 9),
      margin: const EdgeInsets.only(bottom: itemDivider),
      child: Row(
        children: <Widget>[
          if (model!.mindTagsVO?.icon != null)
            Image.network(
              model!.mindTagsVO!.icon!,
              height: 13,
              fit: BoxFit.fitHeight,
            ),
          Container(
            height: 8,
            width: 0.5,
            color: Color(0x80FF8C1A),
            margin: EdgeInsets.symmetric(horizontal: 5),
          ),
          if (model!.mindTagsVO?.tag != null)
            Text(
              model!.mindTagsVO!.tag!,
              style: TextStyle(
                color: SafeAccess.hexColor(
                    model!.mindTagsVO!.tagColor ?? '0xFFFF7300'),
                fontWeight: FontWeight.w500,
                fontSize: 13,
              ),
            ),
        ],
      ),
    );
  }
}

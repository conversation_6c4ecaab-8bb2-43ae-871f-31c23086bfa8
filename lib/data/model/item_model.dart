import '../../utils/safe_access.dart';

class ItemModel {
  bool? bigdayItem;
  bool? bybtItem;
  String? categoryId;
  int? commentCount;
  bool? dreamCouponItem;
  bool? duoCaiItem;
  bool? fliggySeckillingItem;
  bool? fliggyServiceCardItem;
  bool? hotelBillionSubsidesItem;
  bool? couponItem;
  List<String>? images;
  String? itemId;
  bool? openDecoration;
  String? rootCategoryId;
  bool? routeNewDetail;
  int? source;
  String? subtitle;
  String? title;
  bool? hasMoreData;
  // 会员商城标识
  bool? memberMallItem;

  ItemModel.fromJson(Map<String, dynamic> dataModel) {
    final Map<String, dynamic> json =
        SafeAccess.safeParseMap(dataModel['item']);
    bigdayItem = SafeAccess.safeParseBoolean(json['bigdayItem']);
    hasMoreData = SafeAccess.safeParseBoolean(json['hasMoreData']);
    bybtItem = SafeAccess.safeParseBoolean(json['bybtItem']);
    categoryId = SafeAccess.safeParseString(json['categoryId']);
    commentCount = SafeAccess.safeParseInt(json['commentCount']);
    dreamCouponItem = SafeAccess.safeParseBoolean(json['dreamCouponItem']);
    duoCaiItem = SafeAccess.safeParseBoolean(json['duoCaiItem']);
    fliggySeckillingItem =
        SafeAccess.safeParseBoolean(json['fliggySeckillingItem']);
    fliggyServiceCardItem =
        SafeAccess.safeParseBoolean(json['fliggyServiceCardItem']);
    hotelBillionSubsidesItem =
        SafeAccess.safeParseBoolean(json['hotelBillionSubsidesItem']);
    couponItem =
        SafeAccess.safeParseBoolean(json['couponItem']);
    images = SafeAccess.safeParseList(json['images']).cast<String>();
    itemId = SafeAccess.safeParseString(json['itemId']);
    openDecoration = SafeAccess.safeParseBoolean(json['openDecoration']);
    rootCategoryId = SafeAccess.safeParseString(json['rootCategoryId']);
    routeNewDetail = SafeAccess.safeParseBoolean(json['routeNewDetail']);
    source = SafeAccess.safeParseInt(json['source']);
    subtitle = SafeAccess.safeParseString(json['subtitle']);
    title = SafeAccess.safeParseString(json['title']);
    memberMallItem = SafeAccess.safeParseBoolean(json['memberMallItem']);
  }
}

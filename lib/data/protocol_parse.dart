import 'dart:convert';

import 'package:ffloat/ffloat.dart';
import '../component/fee_include/model.dart';
import '../component/hotel/hotel_package_manager/hotel_package_manager.dart';
import 'package:ffperformance/ffperformance.dart';
import '../component/hotel/hotel_style_new_model.dart';
import '../component/hotel_package_to_calendar/model.dart';
import '../component/zero_save_buy_rule/model.dart';
import 'router_net/item_detail_cache_data_helper.dart';
import '../utils/common_util.dart';
import 'package:fliggy_mtop/fliggy_mtop.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';
import 'package:flutter_fliggyKV/fliggykv.dart';

import '../component/booking_notice/model.dart';
import '../component/bottom_bar/consult/model.dart';
import '../component/bottom_bar/consultv2/model.dart';
import '../component/bottom_bar/hit/model.dart';
import '../component/buy_notice_simple/blocks/notice_base.dart';
import '../component/buy_notice_simple/blocks/notice_base_factory.dart';
import '../component/buy_notice_v2/model.dart';
import '../component/item_group_date/model.dart';
import '../component/mind_rule/model.dart';
import '../component/normal_title/model.dart';
import '../component/price_coupons_introduce/model.dart';
import '../component/rank_list_info/model.dart';
import '../component/stock_shelf/model.dart';
import '../component/ticket_shelf_v3/model.dart';
import '../component/traffic/flight_package_manager/flight_package_managerV2.dart';
import '../component/traffic/flight_shelf_v2/model.dart';
import '../component/vacation_scenic_membership_card/model.dart';
import '../custom_widget/page_layer/float-live/model.dart';
import '../item_sku/model/item_sku_model.dart';
import 'model/seller_model.dart';
import 'router_net/item_detail_preload_helper.dart';

import '../component/alitrip_prom/model.dart';
import '../component/billion_service/model.dart';
import '../component/bottom_bar/model.dart';
import '../component/comment_vacation/model.dart';
import '../component/component_key_constant.dart';
import '../component/head_media/notifier/media_container_controller.dart';
import '../component/normal_factor/model.dart';
import '../component/rate_and_server/model.dart';
import '../component/rate_and_share/model.dart';
import '../component/recommend/model.dart';
import '../component/rich_content/model.dart';
import '../component/route_brand/model.dart';
import '../component/service_ensure/model.dart';
import '../component/service_guarantee/model.dart';
import '../component/service_info/model.dart';
import '../component/service_support_shop/model.dart';
import '../component/shop/model.dart';
import '../component/shop/shop_card/shop_card_model.dart';
import '../component/ticket_vacation_market/model.dart';
import '../component/titlebar/model.dart';
import '../component/vacation_server_line/model.dart';
import '../page/page_structure_manger.dart';
import '../utils/safe_access.dart';
import 'item_detail_model.dart';
import 'model/item_model.dart';
import 'net/request_type.dart';
import 'package:fjson_exports/fjson_exports.dart';

class ProtocolParse {
  ItemDetailModel customCreateStateFromJson(
      Map<String, dynamic> data, ItemDetailModel curState,
      {RequestType requestType = RequestType.first}) {
    //如果时新增，用老得对象，否则重新生成新对象
    ItemDetailModel nextState;
    if (requestType == RequestType.first) {
      nextState = ItemDetailModel(data);
      parseFirstData(nextState);
      // 给服务端回传
      // refreshTair(nextState);
    } else if (requestType == RequestType.second) {
      nextState = curState.mergeData(data);
      parseSecondData(nextState);
      // 给下单页传数据,他们要求一些二屏的数据，所以只能在二屏数据返回后处理
      orderData(nextState);
    } else if (requestType == RequestType.third) {
      nextState = curState.mergeData(data);
      parseThirdData(nextState);
    } else if (requestType == RequestType.preLoadCache ||
        requestType == RequestType.fullCache) {
      if (requestType == RequestType.preLoadCache) {
        data['isCache'] = true;
      }
      nextState = ItemDetailModel(data);
      parseFirstData(nextState);
      parseSecondData(nextState);

    } else {
      nextState = ItemDetailModel(data);
      parseFirstData(nextState);
    }
    if (nextState.itemModel?.couponItem ?? false) {
      nextState.body = pageStructureCoupon;
    } else {
      nextState.body = pageStructure;
    }
    return nextState;
  }

  /// 下单页性能优化
  void orderData(ItemDetailModel nextState) {
    final FliggyKV kv = FliggyKV('com.fliggy.fbuy.vacation',
        componentPath: '/Render/DetailToFbuy');
    // https://aliyuque.antfin.com/tzg3ua/gvrtco/hw89zfanhyv6wbg2
    final String? categoryId = nextState.itemModel?.categoryId;
    // 这里后续可以做orange配置，做线上开关
    final List<String> categoryIds = <String>[
      '*********',
      '201272290',
      '202160404'
    ];
    if (categoryIds.contains(categoryId)) {
      final String? itemId = nextState.itemModel?.itemId;
      final String? title = nextState.itemModel?.title;
      final Map<dynamic, dynamic>? couponDesc = nextState.data['couponDesc'];
      if (itemId != null && title != null) {
        final Map<String, dynamic> mapJson = <String, dynamic>{
          'title': title,
          if (couponDesc != null) 'couponDesc': couponDesc,
        };
        kv.encodeString(itemId, jsonEncode(mapJson));
      }
    }
  }

  void refreshTair(ItemDetailModel nextState) async {
    // 后续action：1 根据缓存数据和真实数据的价格/标题/头图是否变化进行更新
    // 2 根据指定的 keepKey 列表进行更新
    // 3 埋点，埋点情况回来看一下
    // https://aliyuque.antfin.com/g/tzg3ua/on0mbi/ury1n7k7hvxsupey/collaborator/join?token=36uRDCeO21iRK1NM&source=doc_collaborator# 《【mtop】批量更新详情缓存信息》
    // https://aliyuque.antfin.com/g/tzg3ua/on0mbi/gr2ftar6f6n15qpg/collaborator/join?token=uKo0ABLVNhzKFG4s&source=doc_collaborator# 《商详主屏Cache字段定义》
    final List<String> keepKey = <String>[
      'banner',
      'price',
      'couponTag',
      'newCustomCoupons',
      'soldInfo',
      'title',
      'productInfo',
      'bookRule',
      'priceCompare',
      'zeroSaveBuyRule',
      'rankListInfo',
      'share',
      'shareFission',
      'alitripProm',
      'memberPoint',
      'brand',
      'serviceGuarantee',
      'serviceGuaranteeV3',
      'serviceTimeInfo',
      'vacationRate',
      'shop',
      'seller',
      'buyBanner',
      'feature',
      'live',
      'frameGroup',
      'item',
      'dainiwanAtmosphere',
      'partnerAtmosphere',
      'detailCore',
      ''
    ];

    const String apiName = 'mtop.fliggy.travelnadetail.batchupdatedetail';
    const String apiVersion = '1.0';
    final String itemId = nextState.itemModel?.itemId ?? '';

    Map<String, dynamic> requestParams = nextState.data;

    final dynamic value = FFPerformance.getCache(
        PreLoadHelper.preLoadCacheKey(nextState.itemModel?.itemId ?? ''));
    final dynamic preLoadCacheData =
        ItemDetailCacheDataHelper.checkCacheData(value);

    ///头图
    final String requestHeadString =
        SafeAccess.safeParseList(requestParams['banner']?['data']?['pics'])
            .toString();
    final String cacheHeadString =
        SafeAccess.safeParseList(preLoadCacheData['banner']?['data']?['pics'])
            .toString();

    ///标题
    final String requestTitleString = SafeAccess.safeParseString(
        requestParams['title']?['data']?['itemTitle']);
    final String cacheTitleString = SafeAccess.safeParseString(
        preLoadCacheData['title']?['data']?['itemTitle']);

    ///大促氛围
    final String requestAmbientString =
        SafeAccess.safeParseMap(requestParams['resource']?['newBigPromotion'])
            .toString();
    final String cacheAmbientString = SafeAccess.safeParseMap(
            preLoadCacheData['resource']?['newBigPromotion'])
        .toString();

    ///会员氛围
    final String requestBgImageString = SafeAccess.safeParseString(
        requestParams['partnerAtmosphere']?['data']?['bgImage']);
    final String cacheBgImageString = SafeAccess.safeParseString(
        preLoadCacheData['partnerAtmosphere']?['data']?['bgImage']);

    if (stringEqual(requestHeadString, cacheHeadString) &&
        stringEqual(requestTitleString, cacheTitleString) &&
        stringEqual(requestAmbientString, cacheAmbientString) &&
        stringEqual(requestBgImageString, cacheBgImageString)) {
      return;
    }

    // 只使用指定的key
    requestParams = Map<String, dynamic>.fromEntries(
      requestParams.entries.where(
          (MapEntry<String, dynamic> entry) => keepKey.contains(entry.key)),
    );

    requestParams.remove('skuBase');
    requestParams.remove('trackParams');

    final Map<String, dynamic> _requestParams = <String, dynamic>{
      'itemParams': <String, dynamic>{
        itemId: jsonEncode(<String, dynamic>{
          'data': requestParams,
          'id': itemId,
          'bizCode': 'flutterItemDetail',
          'from': 'detail' // 好像没啥用，桦白说写个detail
        })
      },
      'commonParams': <String, dynamic>{
        'countryCode': 'cn',
        'longitude': 120.02345458984375,
        'latitude': 30.284969618055555
      }
    };

    final MtopRequestModel request = MtopRequestModel.buildRequset(
        api: apiName,
        method: 'POST',
        version: apiVersion,
        params: _requestParams);
    FliggyMtopApi.getInstance().send(null, request,
        successHandler: (MtopResponseModel responseModel) {
      FliggyUserTrackApi.getInstance().custom(null, 'batchupdatedetail_success',
          'flutter_item_detail', <String, String>{});
    }, errorHandler: (MtopResponseModel responseModel) {
      final Map<String, String> trackMap = <String, String>{};
      trackMap['itemId'] = itemId;
      final Map<String, dynamic> originalData =
          SafeAccess.safeParseMap(responseModel.originalData);
      final Map<String, dynamic> responseHeader =
          SafeAccess.safeParseMap(originalData['responseHeader']);
      trackMap['eagleeye-traceid'] =
          SafeAccess.safeParseString(responseHeader['eagleeye-traceid']);

      FliggyUserTrackApi.getInstance().custom(null, 'batchupdatedetail_error',
          'flutter_item_detail', <String, String>{});
    });
  }

  ///一屏解析
  void parseFirstData(ItemDetailModel nextState) {
    //一定要首先解析商品，后面很多逻辑会用到里面的类目Id
    nextState.itemModel = ItemModel.fromJson(nextState.data);
    //abTest
    nextState.abTestMap = SafeAccess.safeParseMap(
        SafeAccess.safeParseMap(nextState.data['abTest'])['data']);

    // 头图的数据初始化
    nextState.headMediaModel =
        FliggyVacationMediaModel.fromJson(nextState.data);
    nextState.titleBarModel =
        TitleBarModel(nextState.data, nextState.abTestMap);
    //标题
    nextState.normalTitleDataModel =
        NormalTitleDataModel.fromJson(nextState.data);
    //百亿保障
    nextState.billionServiceModel =
        BillionServiceModel.fromJson(nextState.data);
    //通用类目商品特点
    nextState.normalFactorModel = NormalFactorModel.fromJson(nextState.data);
    //大促氛围
    if (nextState.data.containsKey('alitripPromTag')) {
      nextState.alitripPromDataModel =
          AlitripPromDataModel.fromJson(nextState.data);
    }
    // 新版，服务模块
    if (nextState.data.containsKey('newServiceGuarantee')) {
      nextState.serviceGuaranteeDataModel =
          ServiceGuaranteeDataModel.fromJson(nextState.data);
    }
    //「国内票务」服务保障
    if (nextState.data.containsKey('storeInfo')) {
      nextState.serviceSupportShopModel =
          ServiceSupportShopModel.fromJson(nextState.data);
    }
    //套餐日历互通
    if (nextState.data['storeInfo']?['data']?['shotelComponentInfoVO'] !=
        null) {
      nextState.hotelPackageToCalendarModel =
          HotelPackageToCalendarModel.fromJson(
              SafeAccess.safeParseMap(nextState.data['storeInfo']?['data']));
    }

    //会员
    if (nextState.data.containsKey('partnerMemberRight') ||
        nextState.data.containsKey('hotelMemberRightsV2')) {
      nextState.vacationScenicMembershipCardDataModel =
          VacationScenicMembershipCardDataModel.fromJson(nextState.data);
    }
    if (nextState.data.containsKey('serviceTimeInfo')) {
      nextState.serviceInfoModel = ServiceInfoModel.fromJson(nextState.data);
    }
    if (nextState.data.containsKey('brand')) {
      nextState.serviceEnsureDataModel =
          ServiceEnsureDataModel.fromJson(nextState.data);
    }
    if (nextState.data.containsKey('routeBrand')) {
      nextState.routeBrandModel = RouteBrandModel.fromJson(nextState.data);
    }
    //通用服务
    if (nextState.data.containsKey('serviceGuarantee')) {
      nextState.vacationServerLineDataModel =
          VacationServerLineDataModel.fromJson(nextState.data);
    }

    if (nextState.data['poiInfo'] != null) {
      nextState.marketModel = VacationTicketMarketModel.fromJson(
          SafeAccess.safeParseMap(nextState.data['poiInfo']));
    }

    nextState.sellerModel = SellerModel.fromJson(nextState.data);
    //一屏-fy25新增服务和评价
    nextState.spuRankInfoModel = SpuRankInfoModel.fromJson(nextState.data);

    // 0元囤
    if (nextState.data.containsKey('zeroSaveBuyRule')) {
      nextState.zeroSaveBuyRuleModel =
          ZeroSaveBuyRuleModel.fromJson(nextState.data['zeroSaveBuyRule']);
    }
    // 榜单
    if (nextState.data.containsKey('rankListInfo')) {
      nextState.rankListInfoModel =
          RankListInfoModel.fromJson(nextState.data['rankListInfo']);
    }

    //分享评分
    if (nextState.data.containsKey('share')) {
      nextState.rateAndShareDataModel =
          RateAndShareDataModel.fromJson(nextState.data);
    }
    //评价
    if (nextState.data.containsKey('vacationRate')) {
      nextState.commentVacationModel =
          CommentVacationModel.fromJson(nextState.data);
    }
    if (nextState.data.containsKey('ticketTypeShelves')) {
      final Map<String, dynamic> ticketTypeShelves =
          safeNonNullMap(nextState.data['ticketTypeShelves'], (dynamic e) => e);
      if (ticketTypeShelves.containsKey('data')) {
        nextState.ticketShelfV3Model =
            TicketShelfV3Model.fromJson(nextState.data);
      }
    }

    //底部bar
    nextState.buyBannerDataModel = BuyBannerDataModel.fromJson(nextState.data);

    // 下面三个优先级: hit > ConsultV2 > ConsultV2
    if (nextState.data.containsKey('trade')) {
      final Map<String, dynamic> trade = nextState.data['trade'];
      if (trade.containsKey('hintBanner')) {
        nextState.bottomBarHitModel =
            BottomBarHitModel.fromJson(nextState.data);
      }
    }

    if (nextState.data.containsKey('sellerConsultV2') &&
        nextState.bottomBarHitModel == null) {
      nextState.buyBannerConsultV2Model =
          BottomBarConsultV2Model.fromJson(nextState.data);
    }

    if (nextState.data.containsKey('sellerConsult') &&
        nextState.bottomBarHitModel == null &&
        nextState.buyBannerConsultV2Model == null) {
      nextState.buyBannerConsultModel =
          BottomBarConsultModel.fromJson(nextState.data);
    }

    if (nextState.data.containsKey('mindRule') ||
        nextState.data.containsKey('priceCompare')) {
      nextState.mindRuleModel = MindRuleModel.fromJson(nextState.data);
    }

    //店铺卡片
    if (nextState.data.containsKey('shop')) {
      nextState.shopCardDataModel = ShopCardDataModel.fromJson(nextState.data);
    }

    // 机票的货架数据
    if (nextState.data.containsKey('packageShelf')) {
      final Map<String, dynamic> packageShelf =
          safeNonNullMap(nextState.data['packageShelf'], (dynamic e) => e);
      final Map<String, dynamic> packageShelfData =
          safeNonNullMap(packageShelf['data'], (dynamic e) => e);

      if (packageShelfData.isNotEmpty) {
        // 行业逻辑先在这里处理
        /// 注意，这里一定只能给一个package赋值
        if (packageShelfData.containsKey('useNewFlyjpbbShow')) {
          if (safeNonNullBool(packageShelfData['useNewFlyjpbbShow'])) {
            nextState.flightPackageManagerV2 =
                FlightPackageManagerV2.fromJson(nextState.data);
          }
        } else if (safeNonNullBool(packageShelfData['useNewHotelStyle'])) {
          nextState.hotelStyleNewModel =
              HotelStyleNewModel.fromJson(nextState.data);
        } else if (packageShelfData.containsKey('useNewHotelTemplate')) {
          if (safeNonNullBool(packageShelfData['useNewHotelTemplate'])) {
            nextState.hotelStyleNewModel =
                HotelStyleNewModel.fromJson(nextState.data);
          }
        } else if (nextState.abTestMap.containsKey('hotelScenicVersion')) {
          // nextState.stockShelfModel =
          //     StockShelfModel.fromJson(packageShelfData);
          nextState.hotelStyleNewModel =
              HotelStyleNewModel.fromJson(nextState.data, showXelements: false);
        }
      }
    }

    if (nextState.data.containsKey('strokeV2')) {
      final Map<String, dynamic> strokeV2 =
          safeNonNullMap(nextState.data['strokeV2'], (dynamic e) => e);
      if (strokeV2.containsKey('data')) {
        nextState.itemGroupDateModel = ItemGroupDateModel.fromJson(
            safeNonNullMap(strokeV2['data'], (dynamic e) => e));
      }
    }

    // 直播入口
    if (nextState.data.containsKey('live')) {
      nextState.floatLiveModel = FloatLiveModel.fromJson(nextState.data);
    }
  }

  ///二屏解析
  void parseSecondData(ItemDetailModel nextState) {
    // nextState.secondNetStatus = RequestStatus.success;
    //店铺-推荐 优惠等
    nextState.shopModel = ShopModel.fromJson(nextState.data);

    // 有价券说明
    if (nextState.data.containsKey('couponDesc')) {
      final Map<String, dynamic> couponDesc = nextState.data['couponDesc'];
      if (couponDesc.containsKey('data') && couponDesc['data'] != null) {
        nextState.priceCouponsIntroduceModel =
            PriceCouponsIntroduceModel.fromJson(couponDesc['data']);
      }
    }

    //图文详情
    nextState.richContentModel = RichContentModel.fromJson(nextState.data);

    if (nextState.data.containsKey('bookingNotice') ||
        nextState.data.containsKey('bookingProcess')) {
      nextState.bookingNoticeModel =
          BookingNoticeModel.fromJson(nextState.data);
    }

    if (nextState.data.containsKey('buyNoticeV2')) {
      final Map<String, dynamic> buyNoticeV2 = nextState.data['buyNoticeV2'];
      final Map<String, dynamic> data = buyNoticeV2['data'];
      nextState.buyNoticeV2Model = BuyNoticeV2Model.fromJson(data);
    }
    if (nextState.data.containsKey('feeExclude') ||
        nextState.data.containsKey('feeInclude')) {
      nextState.feeIncludeModel = FeeIncludeModel.fromJson(nextState.data);
    }
    nextState.buyCommonNoticeModel =
        getItemNoteLists(nextState.data, nextState);
  }

  ///各种说明解析
  List<dynamic> getItemNoteLists(
      Map<dynamic, dynamic>? moduleData, ItemDetailModel nextState) {
    final List<String> lists = <String>[
      // 通用
      'safeNotice', // 安全须知
      // 境内跟团游 50258004
      'richNotice',
      'travelReschedule',
      'travelInsurance',
      // 国内票务 *********
      'notice',
      // 行业模型，境外玩乐套餐 *********
      'bookingDetail',
      'playFee', // 玩乐新版费用详情
      'feeDetail',
      'standardRefundV2',
      // -- 通用
      'refund', // 取消政策
      'standardRefund', // 取消政策
      'waterSportsDescription', // 水上项目说明
      'priceDesc', // 价格说明
      // https://aliyuque.antfin.com/trip_plat/pu6xpg/hpq7nyhkxrclpzl8
      'electronicContract', // 线路电子合同
    ];
    final List<dynamic> result = <dynamic>[];
    for (final String key in lists) {
      if (key == 'safeNotice' &&
          (nextState.itemModel?.categoryId == '*********' ||
              nextState.itemModel?.categoryId == '*********')) {
        //酒店餐饮美食（新）平台有价券隐藏安全须知
        continue;
      }
      if (moduleData?[key] != null) {
        final NoticeBase notice = NoticeBaseFactory().create(key);
        final Map<dynamic, dynamic> noticeDataMap =
            safeNonNullMap(moduleData?[key], (dynamic e) => e);
        final Map<dynamic, dynamic> noticeData;
        if (noticeDataMap.containsKey('data')) {
          noticeData = safeNonNullMap(noticeDataMap['data'], (dynamic e) => e);
        } else {
          noticeData = noticeDataMap;
        }

        if (noticeData.containsKey('show') &&
            (noticeData['show'] == false ||
                noticeData['show'] == 'false' ||
                safeNonNullBool(noticeData['show']) == false)) {
          continue;
        }
        result.add(notice.getData(moduleData!));
      }
    }
    return result;
  }

  ///三屏解析
  void parseThirdData(ItemDetailModel nextState) {
    // nextState.thirdNetStatus = RequestStatus.success;
    //猜你喜欢
    nextState.recommendDataModel = RecommendDataModel.fromJson(nextState.data);
    downloadCdnData(nextState);
  }

  void downloadCdnData(ItemDetailModel nextState) {
    final List<String?> itemIds = <String>[];
    if (nextState.shopModel?.shopRecommendDataModel?.cdnItems != null) {
      itemIds.addAll(nextState.shopModel!.shopRecommendDataModel!.cdnItems);
    }
    if (nextState.recommendDataModel?.cdnItems != null) {
      itemIds.addAll(nextState.recommendDataModel!.cdnItems);
    }
    PreLoadHelper.requestCategoryIds(itemIds: itemIds, isDownloadImage: false);
  }
}

#能力：读取文件../lib/component/component_key_helper.dart里面的key值，生成没有创建过的组件文件夹和component.dart,model.dart,model.dart,widget.dart类
import re
from pathlib import Path

componentStr = '''import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../render/component/component.dart';
import '../../render/component/component_context.dart';
import 'notifier.dart';
import 'widget.dart';

///@description TODO 组件功能
class ReplaceStrComponent extends Component {
  @override
  Widget generateComponent(ComponentContext context) {
    return ChangeNotifierProvider<ReplaceStrChangeNotifier>.value(
      value: ReplaceStrChangeNotifier(context),
      child: const ReplaceStrWidget(),
    );
  }
}'''

notifierStr = '''import '../../render/component/component_change_notifier.dart';
import '../../render/component/component_context.dart';

class ReplaceStrChangeNotifier extends ComponentChangeNotifier {
  ReplaceStrChangeNotifier(ComponentContext context) : super(context);
}'''

widgetStr = '''import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'notifier.dart';

class ReplaceStrWidget extends StatelessWidget {
  const ReplaceStrWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ReplaceStrChangeNotifier changeNotifier =
        Provider.of<ReplaceStrChangeNotifier>(context);
    return Container();
  }
}'''

mockStr = '''const String mockData = \'\'\'

\'\'\';
'''

replaceStr = 'ReplaceStr'

modelStr = '''import '../../utils/safe_access.dart';

class ReplaceStrModel {

}'''

folderPaths = [];
#读取component_key_helper文件里面的key，生成组件文件夹
with open('../lib/component/component_key_constant.dart', 'r') as file:
    all_lines = file.readlines()
    for line in all_lines:
        pattern = r'\'(.*?)\''
        # 使用正则表达式搜索
        matches = re.search(pattern, line)
        if matches:
            folderPaths.append(matches.group(1))

for folderPath in folderPaths:
    #字符串驼峰转_  在小写字母或数字和大写字母之间插入下划线
    pathName = re.sub('([a-z0-9])([A-Z])', r'\1_\2', folderPath).lower()
    #驼峰命名
    className = folderPath[0].upper() + folderPath[1:]
    # 创建一个新文件夹
    folder_path = Path('../lib/component/'+pathName)
    # 判断文件夹是否存在
    if  not(folder_path.exists() and folder_path.is_dir()):
        print(folderPath)
        folder_path.mkdir(parents=True, exist_ok=True)  # parents=True 允许创建多级目录，exist_ok=True 表示如果目录已存在则忽略

        # 创建component新文件
        file_path = folder_path / "component.dart"
        if not (file_path.exists() and file_path.is_file()):
            # 确保文件存在，如果文件不存在，则创建它。如果文件已存在，此操作无效果
            file_path.touch()

            # 向文件写入内容
            with file_path.open("w", encoding="utf-8") as file:
                file.write(componentStr.replace(replaceStr,className))

        # 创建widget新文件
        file_path = folder_path / "widget.dart"
        if not (file_path.exists() and file_path.is_file()):
            # 确保文件存在，如果文件不存在，则创建它。如果文件已存在，此操作无效果
            file_path.touch()
            # 向文件写入内容
            with file_path.open("w", encoding="utf-8") as file:
                file.write(widgetStr.replace(replaceStr,className))

        # 创建model新文件
        file_path = folder_path / "model.dart"
        if not (file_path.exists() and file_path.is_file()):
            # 确保文件存在，如果文件不存在，则创建它。如果文件已存在，此操作无效果
            file_path.touch()
            # 向文件写入内容
            with file_path.open("w", encoding="utf-8") as file:
                file.write(modelStr.replace(replaceStr,className))

        # 创建notifer新文件
        file_path = folder_path / "notifier.dart"
        if not (file_path.exists() and file_path.is_file()):
            # 确保文件存在，如果文件不存在，则创建它。如果文件已存在，此操作无效果
            file_path.touch()
            # 向文件写入内容
            with file_path.open("w", encoding="utf-8") as file:
                file.write(notifierStr.replace(replaceStr,className))

        # 创建mock新文件
        file_path = folder_path / "mock.dart"
        if  not(file_path.exists() and file_path.is_file()):
            # 确保文件存在，如果文件不存在，则创建它。如果文件已存在，此操作无效果
            file_path.touch()
            # 向文件写入内容
            with file_path.open("w", encoding="utf-8") as file:
                file.write(mockStr)


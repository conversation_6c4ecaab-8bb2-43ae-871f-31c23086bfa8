import re

# 读取key.txt文件里面的key，生成../lib/component/component_key_constant.dart文件对应的key
with open('key.txt', 'r') as file:
    # 逐行读取
    for line in file:
        words = line.split('_')

        # 第一个单词保持小写，后续单词首字母大写
        value = words[0] + ''.join(word.title() for word in words[1:])

        pattern = r'\'(.*?)\''
        # _key
        matches_key = re.search(pattern, line);
        # 驼峰key
        matchesKey = re.search(pattern, value)

        # component_key_constant文件
        # print('static const String '+ matchesKey.group(1) + ' = '+ value)

        # 页面顺序
        # print('ComponentKeyConstant.'+matchesKey.group(1)+',')

        # 组件注册
        # print('ComponentKeyConstant.' + matchesKey.group(1) + ':' + matchesKey.group(1)[
        #     0].upper() + matchesKey.group(1)[1:] + 'Component(),')
        print('import \'../component/' + matches_key.group(1) + '/component.dart\';')
